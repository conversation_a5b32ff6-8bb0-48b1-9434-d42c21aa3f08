connection "gcp_demologinproject_443517" {
  plugin  = "gcp"
  project = "demologinproject-443517"
}

connection "gcp_gen_lang_client_0721815396" {
  plugin  = "gcp"
  project = "gen-lang-client-0721815396"
}

connection "gcp_pbmm_lz_setup_20250612" {
  plugin  = "gcp"
  project = "pbmm-lz-setup-20250612"
}

connection "gcp_aashish_test_pbmm" {
  plugin  = "gcp"
  project = "aashish-test-pbmm"
}

connection "gcp_ansh_test_prj_443118" {
  plugin  = "gcp"
  project = "ansh-test-prj-443118"
}

connection "gcp_egg_timer_83d78" {
  plugin  = "gcp"
  project = "egg-timer-83d78"
}

connection "gcp_newagent_fvne" {
  plugin  = "gcp"
  project = "newagent-fvne"
}

connection "gcp_kubeshot_admin" {
  plugin  = "gcp"
  project = "kubeshot-admin"
}

connection "gcp_kubeshot_autogpt" {
  plugin  = "gcp"
  project = "kubeshot-autogpt"
}

connection "gcp_kubeshot_devops" {
  plugin  = "gcp"
  project = "kubeshot-devops"
}

connection "gcp_kubeshot_rag" {
  plugin  = "gcp"
  project = "kubeshot-rag"
}

connection "gcp_opensas_production" {
  plugin  = "gcp"
  project = "opensas-production"
}

connection "gcp_test_ai_project_435023" {
  plugin  = "gcp"
  project = "test-ai-project-435023"
}

connection "gcp_prj_c_scc_mdzj" {
  plugin  = "gcp"
  project = "prj-c-scc-mdzj"
}

connection "gcp_prj_b_seed_94c1_2c37" {
  plugin  = "gcp"
  project = "prj-b-seed-94c1-2c37"
}

connection "gcp_prj_b_cicd_local_8bcb" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-8bcb"
}

connection "gcp_prj_b_seed_1b86_842b" {
  plugin  = "gcp"
  project = "prj-b-seed-1b86-842b"
}

connection "gcp_prj_c_billing_logs_0aj7" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-0aj7"
}

connection "gcp_prj_c_kms_vtu6" {
  plugin  = "gcp"
  project = "prj-c-kms-vtu6"
}

connection "gcp_prj_c_logging_ouj4" {
  plugin  = "gcp"
  project = "prj-c-logging-ouj4"
}

connection "gcp_prj_c_scc_qr0k" {
  plugin  = "gcp"
  project = "prj-c-scc-qr0k"
}

connection "gcp_prj_c_secrets_7whs" {
  plugin  = "gcp"
  project = "prj-c-secrets-7whs"
}

connection "gcp_prj_d_kms_hqcl" {
  plugin  = "gcp"
  project = "prj-d-kms-hqcl"
}

connection "gcp_prj_d_monitoring_krdx" {
  plugin  = "gcp"
  project = "prj-d-monitoring-krdx"
}

connection "gcp_prj_d_secrets_ng0o" {
  plugin  = "gcp"
  project = "prj-d-secrets-ng0o"
}

connection "gcp_prj_i_kms_svop" {
  plugin  = "gcp"
  project = "prj-i-kms-svop"
}

connection "gcp_prj_i_monitoring_157w" {
  plugin  = "gcp"
  project = "prj-i-monitoring-157w"
}

connection "gcp_prj_i_secrets_a78q" {
  plugin  = "gcp"
  project = "prj-i-secrets-a78q"
}

connection "gcp_prj_m_kms_sf7l" {
  plugin  = "gcp"
  project = "prj-m-kms-sf7l"
}

connection "gcp_prj_m_monitoring_xbej" {
  plugin  = "gcp"
  project = "prj-m-monitoring-xbej"
}

connection "gcp_prj_m_secrets_6mxq" {
  plugin  = "gcp"
  project = "prj-m-secrets-6mxq"
}

connection "gcp_prj_d_shared_base_0czu" {
  plugin  = "gcp"
  project = "prj-d-shared-base-0czu"
}

connection "gcp_prj_i_shared_base_gtcg" {
  plugin  = "gcp"
  project = "prj-i-shared-base-gtcg"
}

connection "gcp_prj_m_shared_base_0jt8" {
  plugin  = "gcp"
  project = "prj-m-shared-base-0jt8"
}

connection "gcp_prj_n_shared_base_87kc" {
  plugin  = "gcp"
  project = "prj-n-shared-base-87kc"
}

connection "gcp_prj_net_dns_btf0" {
  plugin  = "gcp"
  project = "prj-net-dns-btf0"
}

connection "gcp_prj_net_hub_base_lq1u" {
  plugin  = "gcp"
  project = "prj-net-hub-base-lq1u"
}

connection "gcp_prj_net_interconnect_usag" {
  plugin  = "gcp"
  project = "prj-net-interconnect-usag"
}

connection "gcp_prj_p_shared_base_r7l1" {
  plugin  = "gcp"
  project = "prj-p-shared-base-r7l1"
}

connection "gcp_prj_n_kms_6qtl" {
  plugin  = "gcp"
  project = "prj-n-kms-6qtl"
}

connection "gcp_prj_n_monitoring_60oj" {
  plugin  = "gcp"
  project = "prj-n-monitoring-60oj"
}

connection "gcp_prj_n_secrets_uc9u" {
  plugin  = "gcp"
  project = "prj-n-secrets-uc9u"
}

connection "gcp_prj_p_kms_nzli" {
  plugin  = "gcp"
  project = "prj-p-kms-nzli"
}

connection "gcp_prj_p_monitoring_a0fp" {
  plugin  = "gcp"
  project = "prj-p-monitoring-a0fp"
}

connection "gcp_prj_p_secrets_0h60" {
  plugin  = "gcp"
  project = "prj-p-secrets-0h60"
}

connection "gcp_prj_b_cicd_local_24fc" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-24fc"
}

connection "gcp_prj_b_seed_cded_8b3b" {
  plugin  = "gcp"
  project = "prj-b-seed-cded-8b3b"
}

connection "gcp_prj_b_cicd_local_90bf" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-90bf"
}

connection "gcp_prj_b_seed_f751_2438" {
  plugin  = "gcp"
  project = "prj-b-seed-f751-2438"
}

connection "gcp_prj_c_billing_logs_921t" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-921t"
}

connection "gcp_prj_c_kms_wipf" {
  plugin  = "gcp"
  project = "prj-c-kms-wipf"
}

connection "gcp_prj_c_scc_4zmd" {
  plugin  = "gcp"
  project = "prj-c-scc-4zmd"
}

connection "gcp_prj_c_secrets_itj4" {
  plugin  = "gcp"
  project = "prj-c-secrets-itj4"
}

connection "gcp_prj_d_shared_base_ds9z" {
  plugin  = "gcp"
  project = "prj-d-shared-base-ds9z"
}

connection "gcp_prj_i_shared_base_g33r" {
  plugin  = "gcp"
  project = "prj-i-shared-base-g33r"
}

connection "gcp_prj_m_shared_base_32ue" {
  plugin  = "gcp"
  project = "prj-m-shared-base-32ue"
}

connection "gcp_prj_n_shared_base_fpim" {
  plugin  = "gcp"
  project = "prj-n-shared-base-fpim"
}

connection "gcp_prj_net_dns_u629" {
  plugin  = "gcp"
  project = "prj-net-dns-u629"
}

connection "gcp_prj_net_hub_base_fezy" {
  plugin  = "gcp"
  project = "prj-net-hub-base-fezy"
}

connection "gcp_prj_net_interconnect_bkxq" {
  plugin  = "gcp"
  project = "prj-net-interconnect-bkxq"
}

connection "gcp_prj_p_shared_base_bjft" {
  plugin  = "gcp"
  project = "prj-p-shared-base-bjft"
}

connection "gcp_prj_b_cicd_local_b14e" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-b14e"
}

connection "gcp_prj_b_seed_08f4_33ee" {
  plugin  = "gcp"
  project = "prj-b-seed-08f4-33ee"
}

connection "gcp_prj_b_cicd_local_5cd9" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-5cd9"
}

connection "gcp_prj_b_seed_8fb0_beaf" {
  plugin  = "gcp"
  project = "prj-b-seed-8fb0-beaf"
}

connection "gcp_prj_c_billing_logs_79sd" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-79sd"
}

connection "gcp_prj_c_kms_xwj1" {
  plugin  = "gcp"
  project = "prj-c-kms-xwj1"
}

connection "gcp_prj_c_logging_v4t6" {
  plugin  = "gcp"
  project = "prj-c-logging-v4t6"
}

connection "gcp_prj_c_scc_0aav" {
  plugin  = "gcp"
  project = "prj-c-scc-0aav"
}

connection "gcp_prj_c_secrets_48ce" {
  plugin  = "gcp"
  project = "prj-c-secrets-48ce"
}

connection "gcp_prj_d_kms_fxun" {
  plugin  = "gcp"
  project = "prj-d-kms-fxun"
}

connection "gcp_prj_d_monitoring_m4e8" {
  plugin  = "gcp"
  project = "prj-d-monitoring-m4e8"
}

connection "gcp_prj_d_secrets_udvk" {
  plugin  = "gcp"
  project = "prj-d-secrets-udvk"
}

connection "gcp_prj_i_kms_n2mz" {
  plugin  = "gcp"
  project = "prj-i-kms-n2mz"
}

connection "gcp_prj_i_monitoring_lzqa" {
  plugin  = "gcp"
  project = "prj-i-monitoring-lzqa"
}

connection "gcp_prj_i_secrets_rpzz" {
  plugin  = "gcp"
  project = "prj-i-secrets-rpzz"
}

connection "gcp_prj_m_kms_n4k6" {
  plugin  = "gcp"
  project = "prj-m-kms-n4k6"
}

connection "gcp_prj_m_monitoring_2wys" {
  plugin  = "gcp"
  project = "prj-m-monitoring-2wys"
}

connection "gcp_prj_m_secrets_dy6h" {
  plugin  = "gcp"
  project = "prj-m-secrets-dy6h"
}

connection "gcp_prj_d_shared_base_fs1c" {
  plugin  = "gcp"
  project = "prj-d-shared-base-fs1c"
}

connection "gcp_prj_i_shared_base_o7j5" {
  plugin  = "gcp"
  project = "prj-i-shared-base-o7j5"
}

connection "gcp_prj_m_shared_base_ygse" {
  plugin  = "gcp"
  project = "prj-m-shared-base-ygse"
}

connection "gcp_prj_n_shared_base_ppj3" {
  plugin  = "gcp"
  project = "prj-n-shared-base-ppj3"
}

connection "gcp_prj_net_dns_0bao" {
  plugin  = "gcp"
  project = "prj-net-dns-0bao"
}

connection "gcp_prj_net_hub_base_ghu0" {
  plugin  = "gcp"
  project = "prj-net-hub-base-ghu0"
}

connection "gcp_prj_net_interconnect_dkez" {
  plugin  = "gcp"
  project = "prj-net-interconnect-dkez"
}

connection "gcp_prj_p_shared_base_hu4e" {
  plugin  = "gcp"
  project = "prj-p-shared-base-hu4e"
}

connection "gcp_prj_n_kms_ynzd" {
  plugin  = "gcp"
  project = "prj-n-kms-ynzd"
}

connection "gcp_prj_n_monitoring_apyb" {
  plugin  = "gcp"
  project = "prj-n-monitoring-apyb"
}

connection "gcp_prj_n_secrets_vgko" {
  plugin  = "gcp"
  project = "prj-n-secrets-vgko"
}

connection "gcp_prj_p_kms_brwn" {
  plugin  = "gcp"
  project = "prj-p-kms-brwn"
}

connection "gcp_prj_p_monitoring_tiqs" {
  plugin  = "gcp"
  project = "prj-p-monitoring-tiqs"
}

connection "gcp_prj_p_secrets_2xf5" {
  plugin  = "gcp"
  project = "prj-p-secrets-2xf5"
}

connection "gcp_prj_b_cicd_local_196d" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-196d"
}

connection "gcp_prj_b_seed_3e15_8c39" {
  plugin  = "gcp"
  project = "prj-b-seed-3e15-8c39"
}

connection "gcp_prj_c_billing_logs_v7iz" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-v7iz"
}

connection "gcp_prj_c_kms_v3wx" {
  plugin  = "gcp"
  project = "prj-c-kms-v3wx"
}

connection "gcp_prj_c_logging_x19k" {
  plugin  = "gcp"
  project = "prj-c-logging-x19k"
}

connection "gcp_prj_c_scc_0bje" {
  plugin  = "gcp"
  project = "prj-c-scc-0bje"
}

connection "gcp_prj_c_secrets_iml0" {
  plugin  = "gcp"
  project = "prj-c-secrets-iml0"
}

connection "gcp_prj_d_kms_6sap" {
  plugin  = "gcp"
  project = "prj-d-kms-6sap"
}

connection "gcp_prj_d_monitoring_cb3t" {
  plugin  = "gcp"
  project = "prj-d-monitoring-cb3t"
}

connection "gcp_prj_d_secrets_kbi5" {
  plugin  = "gcp"
  project = "prj-d-secrets-kbi5"
}

connection "gcp_prj_i_kms_1kuv" {
  plugin  = "gcp"
  project = "prj-i-kms-1kuv"
}

connection "gcp_prj_i_monitoring_216n" {
  plugin  = "gcp"
  project = "prj-i-monitoring-216n"
}

connection "gcp_prj_i_secrets_6w4c" {
  plugin  = "gcp"
  project = "prj-i-secrets-6w4c"
}

connection "gcp_prj_m_kms_5syw" {
  plugin  = "gcp"
  project = "prj-m-kms-5syw"
}

connection "gcp_prj_m_monitoring_tq1e" {
  plugin  = "gcp"
  project = "prj-m-monitoring-tq1e"
}

connection "gcp_prj_m_secrets_hh7e" {
  plugin  = "gcp"
  project = "prj-m-secrets-hh7e"
}

connection "gcp_prj_d_shared_base_6sq7" {
  plugin  = "gcp"
  project = "prj-d-shared-base-6sq7"
}

connection "gcp_prj_i_shared_base_aojm" {
  plugin  = "gcp"
  project = "prj-i-shared-base-aojm"
}

connection "gcp_prj_m_shared_base_3akl" {
  plugin  = "gcp"
  project = "prj-m-shared-base-3akl"
}

connection "gcp_prj_n_shared_base_4eys" {
  plugin  = "gcp"
  project = "prj-n-shared-base-4eys"
}

connection "gcp_prj_net_dns_sm1a" {
  plugin  = "gcp"
  project = "prj-net-dns-sm1a"
}

connection "gcp_prj_net_hub_base_fotp" {
  plugin  = "gcp"
  project = "prj-net-hub-base-fotp"
}

connection "gcp_prj_net_interconnect_u9uy" {
  plugin  = "gcp"
  project = "prj-net-interconnect-u9uy"
}

connection "gcp_prj_p_shared_base_dcjo" {
  plugin  = "gcp"
  project = "prj-p-shared-base-dcjo"
}

connection "gcp_prj_n_kms_hetl" {
  plugin  = "gcp"
  project = "prj-n-kms-hetl"
}

connection "gcp_prj_n_monitoring_4lmb" {
  plugin  = "gcp"
  project = "prj-n-monitoring-4lmb"
}

connection "gcp_prj_n_secrets_fh9x" {
  plugin  = "gcp"
  project = "prj-n-secrets-fh9x"
}

connection "gcp_prj_p_kms_ajfw" {
  plugin  = "gcp"
  project = "prj-p-kms-ajfw"
}

connection "gcp_prj_p_monitoring_5tq4" {
  plugin  = "gcp"
  project = "prj-p-monitoring-5tq4"
}

connection "gcp_prj_p_secrets_ndhw" {
  plugin  = "gcp"
  project = "prj-p-secrets-ndhw"
}

connection "gcp_prj_b_cicd_local_1a22" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-1a22"
}

connection "gcp_prj_b_seed_3585_0609" {
  plugin  = "gcp"
  project = "prj-b-seed-3585-0609"
}

connection "gcp_prj_c_billing_logs_mzwv" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-mzwv"
}

connection "gcp_prj_c_kms_pvix" {
  plugin  = "gcp"
  project = "prj-c-kms-pvix"
}

connection "gcp_prj_c_logging_u76g" {
  plugin  = "gcp"
  project = "prj-c-logging-u76g"
}

connection "gcp_prj_c_scc_2q5n" {
  plugin  = "gcp"
  project = "prj-c-scc-2q5n"
}

connection "gcp_prj_c_secrets_akn1" {
  plugin  = "gcp"
  project = "prj-c-secrets-akn1"
}

connection "gcp_prj_d_kms_bhbe" {
  plugin  = "gcp"
  project = "prj-d-kms-bhbe"
}

connection "gcp_prj_d_monitoring_uw7t" {
  plugin  = "gcp"
  project = "prj-d-monitoring-uw7t"
}

connection "gcp_prj_d_secrets_bvt8" {
  plugin  = "gcp"
  project = "prj-d-secrets-bvt8"
}

connection "gcp_prj_d_shared_base_mhnr" {
  plugin  = "gcp"
  project = "prj-d-shared-base-mhnr"
}

connection "gcp_prj_i_shared_base_38i3" {
  plugin  = "gcp"
  project = "prj-i-shared-base-38i3"
}

connection "gcp_prj_m_shared_base_ygs6" {
  plugin  = "gcp"
  project = "prj-m-shared-base-ygs6"
}

connection "gcp_prj_n_shared_base_1s86" {
  plugin  = "gcp"
  project = "prj-n-shared-base-1s86"
}

connection "gcp_prj_net_dns_ldlu" {
  plugin  = "gcp"
  project = "prj-net-dns-ldlu"
}

connection "gcp_prj_net_hub_base_pvlj" {
  plugin  = "gcp"
  project = "prj-net-hub-base-pvlj"
}

connection "gcp_prj_net_interconnect_5vot" {
  plugin  = "gcp"
  project = "prj-net-interconnect-5vot"
}

connection "gcp_prj_p_shared_base_xmir" {
  plugin  = "gcp"
  project = "prj-p-shared-base-xmir"
}

connection "gcp_prj_b_cicd_local_2e57" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-2e57"
}

connection "gcp_prj_b_seed_aab1_72b0" {
  plugin  = "gcp"
  project = "prj-b-seed-aab1-72b0"
}

connection "gcp_prj_b_cicd_local_3c45" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-3c45"
}

connection "gcp_prj_b_seed_af27_89e8" {
  plugin  = "gcp"
  project = "prj-b-seed-af27-89e8"
}

connection "gcp_prj_b_cicd_local_3272" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-3272"
}

connection "gcp_prj_b_seed_ca73_0235" {
  plugin  = "gcp"
  project = "prj-b-seed-ca73-0235"
}

connection "gcp_prj_c_billing_logs_w71r" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-w71r"
}

connection "gcp_prj_c_kms_sx80" {
  plugin  = "gcp"
  project = "prj-c-kms-sx80"
}

connection "gcp_prj_c_logging_cf7j" {
  plugin  = "gcp"
  project = "prj-c-logging-cf7j"
}

connection "gcp_prj_c_scc_gddx" {
  plugin  = "gcp"
  project = "prj-c-scc-gddx"
}

connection "gcp_prj_c_secrets_e9gs" {
  plugin  = "gcp"
  project = "prj-c-secrets-e9gs"
}

connection "gcp_prj_d_kms_9xk3" {
  plugin  = "gcp"
  project = "prj-d-kms-9xk3"
}

connection "gcp_prj_d_monitoring_k9xh" {
  plugin  = "gcp"
  project = "prj-d-monitoring-k9xh"
}

connection "gcp_prj_d_secrets_ifuw" {
  plugin  = "gcp"
  project = "prj-d-secrets-ifuw"
}

connection "gcp_prj_m_kms_hbje" {
  plugin  = "gcp"
  project = "prj-m-kms-hbje"
}

connection "gcp_prj_m_monitoring_occw" {
  plugin  = "gcp"
  project = "prj-m-monitoring-occw"
}

connection "gcp_prj_m_secrets_116n" {
  plugin  = "gcp"
  project = "prj-m-secrets-116n"
}

connection "gcp_prj_d_shared_base_5ger" {
  plugin  = "gcp"
  project = "prj-d-shared-base-5ger"
}

connection "gcp_prj_i_shared_base_02hp" {
  plugin  = "gcp"
  project = "prj-i-shared-base-02hp"
}

connection "gcp_prj_m_shared_base_h0bq" {
  plugin  = "gcp"
  project = "prj-m-shared-base-h0bq"
}

connection "gcp_prj_n_shared_base_wzsb" {
  plugin  = "gcp"
  project = "prj-n-shared-base-wzsb"
}

connection "gcp_prj_net_dns_19xd" {
  plugin  = "gcp"
  project = "prj-net-dns-19xd"
}

connection "gcp_prj_net_hub_base_97es" {
  plugin  = "gcp"
  project = "prj-net-hub-base-97es"
}

connection "gcp_prj_net_interconnect_ohbt" {
  plugin  = "gcp"
  project = "prj-net-interconnect-ohbt"
}

connection "gcp_prj_p_shared_base_1b4a" {
  plugin  = "gcp"
  project = "prj-p-shared-base-1b4a"
}

connection "gcp_prj_n_kms_4ur9" {
  plugin  = "gcp"
  project = "prj-n-kms-4ur9"
}

connection "gcp_prj_n_monitoring_17ty" {
  plugin  = "gcp"
  project = "prj-n-monitoring-17ty"
}

connection "gcp_prj_n_secrets_io9u" {
  plugin  = "gcp"
  project = "prj-n-secrets-io9u"
}

connection "gcp_prj_p_kms_qktj" {
  plugin  = "gcp"
  project = "prj-p-kms-qktj"
}

connection "gcp_prj_p_monitoring_d0st" {
  plugin  = "gcp"
  project = "prj-p-monitoring-d0st"
}

connection "gcp_prj_p_secrets_51sv" {
  plugin  = "gcp"
  project = "prj-p-secrets-51sv"
}

connection "gcp_prj_b_cicd_local_de3d" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-de3d"
}

connection "gcp_prj_b_seed_82a5_d051" {
  plugin  = "gcp"
  project = "prj-b-seed-82a5-d051"
}

connection "gcp_prj_b_cicd_local_ec14" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-ec14"
}

connection "gcp_prj_b_seed_1e63_ffa5" {
  plugin  = "gcp"
  project = "prj-b-seed-1e63-ffa5"
}

connection "gcp_aashish_test_prj" {
  plugin  = "gcp"
  project = "aashish-test-prj"
}

connection "gcp_ansh_test_prj" {
  plugin  = "gcp"
  project = "ansh-test-prj"
}

connection "gcp_vratant_test_prj" {
  plugin  = "gcp"
  project = "vratant-test-prj"
}

connection "gcp_bansari_test" {
  plugin  = "gcp"
  project = "bansari-test"
}

connection "gcp_bucket_rsync" {
  plugin  = "gcp"
  project = "bucket-rsync"
}

connection "gcp_neosec_ai_solutions" {
  plugin  = "gcp"
  project = "neosec-ai-solutions"
}

connection "gcp_test_harjot_prj" {
  plugin  = "gcp"
  project = "test-harjot-prj"
}

connection "gcp_shubhi_test_prj" {
  plugin  = "gcp"
  project = "shubhi-test-prj"
}

connection "gcp_backstage_412018" {
  plugin  = "gcp"
  project = "backstage-412018"
}

connection "gcp_scrapper_419215" {
  plugin  = "gcp"
  project = "scrapper-419215"
}

connection "gcp_prj_ottpol_bootstrap_test" {
  plugin  = "gcp"
  project = "prj-ottpol-bootstrap-test"
}

connection "gcp_prj_c_billing_psi" {
  plugin  = "gcp"
  project = "prj-c-billing-psi"
}

connection "gcp_prj_c_logging_psi" {
  plugin  = "gcp"
  project = "prj-c-logging-psi"
}

connection "gcp_prj_c_core_networking_np_psi" {
  plugin  = "gcp"
  project = "prj-c-core-networking-np-psi"
}

connection "gcp_prj_c_host_networking_np_psi" {
  plugin  = "gcp"
  project = "prj-c-host-networking-np-psi"
}

connection "gcp_prj_c_core_networking_p_psi" {
  plugin  = "gcp"
  project = "prj-c-core-networking-p-psi"
}

connection "gcp_prj_c_host_networking_p_psi" {
  plugin  = "gcp"
  project = "prj-c-host-networking-p-psi"
}

connection "gcp_prj_c_sec_sentinal_np_psi" {
  plugin  = "gcp"
  project = "prj-c-sec-sentinal-np-psi"
}

connection "gcp_prj_c_sec_sentinal_p_psi" {
  plugin  = "gcp"
  project = "prj-c-sec-sentinal-p-psi"
}

connection "gcp_prj_c_api_gateway_np_psi" {
  plugin  = "gcp"
  project = "prj-c-api-gateway-np-psi"
}

connection "gcp_prj_c_observability_np_psi" {
  plugin  = "gcp"
  project = "prj-c-observability-np-psi"
}

connection "gcp_prj_c_api_gateway_p_psi" {
  plugin  = "gcp"
  project = "prj-c-api-gateway-p-psi"
}

connection "gcp_prj_c_observability_p_psi" {
  plugin  = "gcp"
  project = "prj-c-observability-p-psi"
}

connection "gcp_prj_c_tools_np_psi" {
  plugin  = "gcp"
  project = "prj-c-tools-np-psi"
}

connection "gcp_prj_c_tools_p_psi" {
  plugin  = "gcp"
  project = "prj-c-tools-p-psi"
}

connection "gcp_prj_opol_core_networks" {
  plugin  = "gcp"
  project = "prj-opol-core-networks"
}

connection "gcp_prj_foundation_psi" {
  plugin  = "gcp"
  project = "prj-foundation-psi"
}

connection "gcp_test_prj_for_psi_wif" {
  plugin  = "gcp"
  project = "test-prj-for-psi-wif"
}

connection "gcp_sys_10584336580509349754235258" {
  plugin  = "gcp"
  project = "sys-10584336580509349754235258"
}

connection "gcp_sys_16473596295331132258055880" {
  plugin  = "gcp"
  project = "sys-16473596295331132258055880"
}

connection "gcp_sys_16577995987289835251666163" {
  plugin  = "gcp"
  project = "sys-16577995987289835251666163"
}

connection "gcp_sys_18228428281390614574756768" {
  plugin  = "gcp"
  project = "sys-18228428281390614574756768"
}

connection "gcp_sys_25365120900523877734101376" {
  plugin  = "gcp"
  project = "sys-25365120900523877734101376"
}

connection "gcp_sys_47687987468820982859752416" {
  plugin  = "gcp"
  project = "sys-47687987468820982859752416"
}

connection "gcp_sys_49349059182320262405653747" {
  plugin  = "gcp"
  project = "sys-49349059182320262405653747"
}

connection "gcp_sys_51815433874226388266640321" {
  plugin  = "gcp"
  project = "sys-51815433874226388266640321"
}

connection "gcp_sys_57733743513041139174379021" {
  plugin  = "gcp"
  project = "sys-57733743513041139174379021"
}

connection "gcp_sys_77432550242619391028652600" {
  plugin  = "gcp"
  project = "sys-77432550242619391028652600"
}

connection "gcp_sys_83396747445816645808979860" {
  plugin  = "gcp"
  project = "sys-83396747445816645808979860"
}

connection "gcp_prj_i_shared_base_vtix" {
  plugin  = "gcp"
  project = "prj-i-shared-base-vtix"
}

connection "gcp_prj_m_shared_base_ue4h" {
  plugin  = "gcp"
  project = "prj-m-shared-base-ue4h"
}

connection "gcp_prj_c_billing_logs_igh9" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-igh9"
}

connection "gcp_prj_c_kms_a0bj" {
  plugin  = "gcp"
  project = "prj-c-kms-a0bj"
}

connection "gcp_prj_c_logging_yozr" {
  plugin  = "gcp"
  project = "prj-c-logging-yozr"
}

connection "gcp_prj_c_scc_d6ki" {
  plugin  = "gcp"
  project = "prj-c-scc-d6ki"
}

connection "gcp_prj_c_secrets_308g" {
  plugin  = "gcp"
  project = "prj-c-secrets-308g"
}

connection "gcp_prj_d_kms_3k9a" {
  plugin  = "gcp"
  project = "prj-d-kms-3k9a"
}

connection "gcp_prj_d_monitoring_n9jv" {
  plugin  = "gcp"
  project = "prj-d-monitoring-n9jv"
}

connection "gcp_prj_d_secrets_67yy" {
  plugin  = "gcp"
  project = "prj-d-secrets-67yy"
}

connection "gcp_prj_d_bu1_b_app_s1_3qz3" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-app-s1-3qz3"
}

connection "gcp_prj_d_bu1_b_dat_s1_opyk" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-dat-s1-opyk"
}

connection "gcp_prj_d_bu1_b_pub_s1_ng3l" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-pub-s1-ng3l"
}

connection "gcp_prj_d_bu2_b_app_s2_scuy" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-app-s2-scuy"
}

connection "gcp_prj_d_bu2_b_dat_s2_acpp" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-dat-s2-acpp"
}

connection "gcp_prj_d_bu2_b_pub_s2_ecm4" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-pub-s2-ecm4"
}

connection "gcp_prj_i_kms_w6ei" {
  plugin  = "gcp"
  project = "prj-i-kms-w6ei"
}

connection "gcp_prj_i_monitoring_4oby" {
  plugin  = "gcp"
  project = "prj-i-monitoring-4oby"
}

connection "gcp_prj_i_secrets_9a17" {
  plugin  = "gcp"
  project = "prj-i-secrets-9a17"
}

connection "gcp_prj_i_bu1_b_idn_s1_f4zt" {
  plugin  = "gcp"
  project = "prj-i-bu1-b-idn-s1-f4zt"
}

connection "gcp_prj_i_bu2_b_idn_s2_1p4g" {
  plugin  = "gcp"
  project = "prj-i-bu2-b-idn-s2-1p4g"
}

connection "gcp_prj_m_kms_9101" {
  plugin  = "gcp"
  project = "prj-m-kms-9101"
}

connection "gcp_prj_m_monitoring_5azq" {
  plugin  = "gcp"
  project = "prj-m-monitoring-5azq"
}

connection "gcp_prj_m_secrets_rygd" {
  plugin  = "gcp"
  project = "prj-m-secrets-rygd"
}

connection "gcp_prj_m_bu1_b_mgt_s1_u5z4" {
  plugin  = "gcp"
  project = "prj-m-bu1-b-mgt-s1-u5z4"
}

connection "gcp_prj_d_shared_base_ui45" {
  plugin  = "gcp"
  project = "prj-d-shared-base-ui45"
}

connection "gcp_prj_i_shared_base_5p6b" {
  plugin  = "gcp"
  project = "prj-i-shared-base-5p6b"
}

connection "gcp_prj_m_shared_base_l23e" {
  plugin  = "gcp"
  project = "prj-m-shared-base-l23e"
}

connection "gcp_prj_n_shared_base_q37n" {
  plugin  = "gcp"
  project = "prj-n-shared-base-q37n"
}

connection "gcp_prj_net_dns_xl09" {
  plugin  = "gcp"
  project = "prj-net-dns-xl09"
}

connection "gcp_prj_net_hub_base_yhyv" {
  plugin  = "gcp"
  project = "prj-net-hub-base-yhyv"
}

connection "gcp_prj_net_interconnect_cz6f" {
  plugin  = "gcp"
  project = "prj-net-interconnect-cz6f"
}

connection "gcp_prj_p_shared_base_tp4m" {
  plugin  = "gcp"
  project = "prj-p-shared-base-tp4m"
}

connection "gcp_prj_n_kms_8rwi" {
  plugin  = "gcp"
  project = "prj-n-kms-8rwi"
}

connection "gcp_prj_n_monitoring_lmkv" {
  plugin  = "gcp"
  project = "prj-n-monitoring-lmkv"
}

connection "gcp_prj_n_secrets_re9a" {
  plugin  = "gcp"
  project = "prj-n-secrets-re9a"
}

connection "gcp_prj_n_bu1_b_app_s1_ed86" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-app-s1-ed86"
}

connection "gcp_prj_n_bu1_b_dat_s1_gsoo" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-dat-s1-gsoo"
}

connection "gcp_prj_n_bu1_b_pub_s1_eokf" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-pub-s1-eokf"
}

connection "gcp_prj_n_bu2_b_app_s2_y42x" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-app-s2-y42x"
}

connection "gcp_prj_n_bu2_b_dat_s2_5qs6" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-dat-s2-5qs6"
}

connection "gcp_prj_n_bu2_b_pub_s2_rg1i" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-pub-s2-rg1i"
}

connection "gcp_prj_p_kms_mv3y" {
  plugin  = "gcp"
  project = "prj-p-kms-mv3y"
}

connection "gcp_prj_p_monitoring_ftqd" {
  plugin  = "gcp"
  project = "prj-p-monitoring-ftqd"
}

connection "gcp_prj_p_secrets_5an9" {
  plugin  = "gcp"
  project = "prj-p-secrets-5an9"
}

connection "gcp_prj_p_bu1_b_app_s1_1ezr" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-app-s1-1ezr"
}

connection "gcp_prj_p_bu1_b_dat_s1_q8si" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-dat-s1-q8si"
}

connection "gcp_prj_p_bu1_b_pub_s1_f8tj" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-pub-s1-f8tj"
}

connection "gcp_prj_p_bu2_b_app_s2_jrxg" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-app-s2-jrxg"
}

connection "gcp_prj_p_bu2_b_dat_s2_1kcx" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-dat-s2-1kcx"
}

connection "gcp_prj_p_bu2_b_pub_s2_fyt7" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-pub-s2-fyt7"
}

connection "gcp_prj_b_seed_46ef_0c2d" {
  plugin  = "gcp"
  project = "prj-b-seed-46ef-0c2d"
}

connection "gcp_prj_b_seed_7c39_66c1" {
  plugin  = "gcp"
  project = "prj-b-seed-7c39-66c1"
}

connection "gcp" {
  plugin = "gcp"
  type        = "aggregator"
  connections = ["gcp_*"]
}