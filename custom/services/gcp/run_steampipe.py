import subprocess
import json
import os
import argparse
from google.cloud import resourcemanager_v3

# Function to get projects using gcloud (for local development)
def get_gcp_projects_local():
    try:
        result = subprocess.run(
            ["gcloud", "projects", "list", "--format=json"],
            capture_output=True,
            text=True,
            check=True
        )
        projects = json.loads(result.stdout)
        return projects
    except subprocess.CalledProcessError as e:
        print("Error fetching projects:", e)
        return []
        
def get_gcp_projects_cloud(credentials=None, organization_id=None):
    """
    List all projects accessible with the given credentials, optionally filtering by organization.
    If organization_id is provided, will recursively list all projects in the organization,
    including those in nested folders.
    """
    try:
        # Initialize the clients with the provided credentials if available
        if credentials:
            projects_client = resourcemanager_v3.ProjectsClient(credentials=credentials)
            folders_client = resourcemanager_v3.FoldersClient(credentials=credentials)
            print("Using provided credentials for project listing")
        else:
            projects_client = resourcemanager_v3.ProjectsClient()
            folders_client = resourcemanager_v3.FoldersClient()
            print("Using default credentials for project listing")
            
        projects = []
        
        # If organization ID is provided, use recursive folder traversal
        if organization_id:
            print(f"Listing all projects in organization {organization_id}, including nested folders")
            seen_folders = set()
            
            def process_container(container: str):
                """Recursively process folders and projects in a container"""
                # Process projects
                try:
                    for project in projects_client.list_projects(
                        request={"parent": container, "show_deleted": False}
                    ):
                        # Include all projects regardless of state
                        if project.state == resourcemanager_v3.Project.State.ACTIVE:
                            projects.append({
                                "projectId": project.project_id,
                                "name": project.display_name,
                                "lifecycleState": project.state.name
                            })
                except Exception as e:
                    print(f"Error listing projects in {container}: {str(e)}")

                # Process folders
                try:
                    for folder in folders_client.list_folders(
                        request={"parent": container}
                    ):
                        folder_name = folder.name
                        if folder_name not in seen_folders:
                            seen_folders.add(folder_name)
                            process_container(folder_name)  # Recursive call
                except Exception as e:
                    print(f"Error listing folders in {container}: {str(e)}")

            # Start with organization root
            process_container(f"organizations/{organization_id}")
            
        else:
            # Fall back to search_projects for non-organization based listing
            try:
                search_request = resourcemanager_v3.SearchProjectsRequest()
                search_result = projects_client.search_projects(request=search_request)
                
                for project in search_result:
                    # Include all projects regardless of state
                    if project.state == resourcemanager_v3.Project.State.ACTIVE:
                        projects.append({
                            "projectId": project.project_id,
                            "name": project.display_name,
                            "lifecycleState": project.state.name
                        })
                
            except Exception as search_error:
                print(f"Project search failed: {search_error}")
                return []
                
        print(f"Successfully found {len(projects)} projects")
        return projects
                
    except Exception as e:
        print(f"Error initializing client: {e}")
        return []
# Function to generate Steampipe connection configurations
def generate_steampipe_config(projects, auth_type):
    config_lines = []

    for project in projects:
        # Ensure we're always using projectId for the project setting
        project_id = project["projectId"]
        # Create a safe connection name from the project ID
        connection_name = "gcp_" + project_id.replace("-", "_")
        
        config_lines.append(f'connection "{connection_name}" {{')
        config_lines.append('  plugin  = "gcp"')
        config_lines.append(f'  project = "{project_id}"')
        if auth_type == 'sa':
            config_lines.append(f'  credentials = "/app/authentication/service-account.json"')
        config_lines.append("}\n")
        # Add organization_id if provided

    config_lines.append('connection "gcp" {')
    config_lines.append('  plugin = "gcp"')
    config_lines.append('  type        = "aggregator"')
    if auth_type == 'sa':
        config_lines.append(f'  credentials = "/app/authentication/service-account.json"')
    # Add organization_id to the aggregator connection if provided
    config_lines.append('  connections = ["gcp_*"]')
    config_lines.append("}")

    return "\n".join(config_lines)

# Function to save config to file
def save_config(config_content, is_cloud_run=False):
    if is_cloud_run:
        # For Cloud Build/Cloud Run environment, save to both locations
        folder_paths = [
            "steampipe-config",  # For the build process
            "/home/<USER>/.steampipe/config"  # Fixed path for the container
        ]
    else:
        folder_paths = ["/home/<USER>/.steampipe/config"]  # For local development (mounted directory)    
    filename = "gcp.spc"
    saved_paths = []

    for folder_path in folder_paths:
        # Ensure the directory exists
        os.makedirs(folder_path, exist_ok=True)
        
        # Full path to save the file
        file_path = os.path.join(folder_path, filename)
        
        # Write the config file
        with open(file_path, "w") as f:
            f.write(config_content)
        
        saved_paths.append(file_path)
        print(f"Configuration saved to {file_path}")

    # Print the content for verification
    print("\nGenerated gcp.spc content:")
    print("------------------------")
    print(config_content)
    print("------------------------")
    
    return saved_paths[0]  # Return the first path for compatibility

def generate_and_save_config(credentials=None, is_cloud_run=False, organization_id=None):
    """
    Helper function to generate and save configuration for both environments
    
    Args:
        credentials: Google Cloud credentials object (optional)
        is_cloud_run: Boolean indicating if running in Cloud Run environment
        organization_id: GCP organization ID to filter projects and include in config (optional)
    """
    auth_type = os.getenv('AUTH_TYPE', 'sa').lower()
    print(f"Using authentication type: {auth_type}")
    
    if organization_id:
        print(f"Using organization ID: {organization_id}")
    
    if is_cloud_run or credentials:
        print("Using cloud environment for project listing")
        projects = get_gcp_projects_cloud(credentials, organization_id)
    else:
        print("Using local environment for project listing")
        projects = get_gcp_projects_local()
        
    if not projects:
        print("No projects found or an error occurred.")
        return None
    
    print(f"Found {len(projects)} projects to configure")

    # Log first few projects for debugging
    for i, project in enumerate(projects[:5]):
        print(f"  Project {i+1}: {project.get('projectId', 'N/A')} - {project.get('name', 'N/A')} - State: {project.get('lifecycleState', 'N/A')}")
    if len(projects) > 5:
        print(f"  ... and {len(projects) - 5} more projects")

    config_content = generate_steampipe_config(projects, auth_type)
    return save_config(config_content, is_cloud_run)

# Only run this if the script is executed directly (not imported)
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--cloud-run', action='store_true', 
                       help='Run in Cloud Run mode')
    parser.add_argument('--organization-id', type=str,
                       help='GCP organization ID to filter projects')
    args = parser.parse_args()

    is_cloud_run = args.cloud_run or os.getenv('K_SERVICE') is not None
    organization_id = args.organization_id
    
    if is_cloud_run:
        print("Running in Cloud Run environment")
        # Cloud Run/Build environment - just generate config
        generate_and_save_config(is_cloud_run=True, organization_id=organization_id)
    else:
        print("Running in local environment")
        generate_and_save_config(is_cloud_run=False, organization_id=organization_id)