import subprocess
import json
import os
import argparse
import time
import logging
from azure.identity import DefaultAzureCredential, ClientSecretCredential
from azure.mgmt.subscription import SubscriptionClient
from azure.core.exceptions import AzureError
import traceback  # Add this import
from azure.identity import DefaultAzureCredential, ClientSecretCredential

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_azure_subscriptions(credentials=None, tenant_id=None):
    """
    Get Azure subscriptions using provided credentials
    Returns a tuple of (subscriptions, error)
    """
    try:
        if credentials is None:
            logger.info("No credentials provided, attempting to use DefaultAzureCredential")
            credential = DefaultAzureCredential()
        else:
            logger.info("Using provided credentials for subscription access")
            credential = credentials
            
        # Try to get subscriptions via API
        subscription_client = SubscriptionClient(credential)
        subscriptions = []
        for subscription in subscription_client.subscriptions.list():
            subscriptions.append({
                "subscriptionId": subscription.subscription_id,
                "displayName": subscription.display_name,
                "state": subscription.state
            })

        return subscriptions
    except AzureError as e:
        print(f"Error fetching Azure subscriptions: {e}")
        return []
    except Exception as e:
        print(f"Unexpected error: {e}")
        return []
        

# Function to generate Steampipe connection configurations for Azure
def generate_steampipe_config(subscriptions):
    config_lines = []

    # Add the default Azure connection
    # config_lines.append('connection "azure" {')
    # config_lines.append('  plugin = "azure"')
    # config_lines.append('}\n')

    # Add connections for each subscription
    for subscription in subscriptions:
        subscription_id = subscription["subscriptionId"]
        # Replace dashes with underscores in the connection name
        connection_name = f"azure_{subscription_id.replace('-', '_')}"

        config_lines.append(f'connection "{connection_name}" {{')
        config_lines.append('  plugin = "azure"')
        config_lines.append(f'  subscription_id = "{subscription_id}"')
        config_lines.append('}\n')

    config_lines.append('connection "azure" {')
    config_lines.append('  plugin = "azure"')
    config_lines.append('  type        = "aggregator"')
 # Add organization_id to the aggregator connection if provided
    config_lines.append('  connections = ["azure_*"]')
    config_lines.append("}")

    return "\n".join(config_lines)

# Function to save config to file
def save_config(config_content, is_cloud_run=False):
    if is_cloud_run:
        # For Cloud Build/Cloud Run environment, save to both locations
        folder_paths = [
            "steampipe-config",  # For the build process
            "/home/<USER>/.steampipe/config"  # Fixed path for the container
        ]
    else:
        folder_paths = [
            "steampipe-config",  # For local development
            os.path.expanduser("~/.steampipe/config")  # Local user config
        ]
    
    filename = "azure.spc"
    saved_paths = []

    for folder_path in folder_paths:
        # Ensure the directory exists
        os.makedirs(folder_path, exist_ok=True)
        
        # Full path to save the file
        file_path = os.path.join(folder_path, filename)
        
        # Write the config file
        with open(file_path, "w") as f:
            f.write(config_content)
        
        saved_paths.append(file_path)
        logger.info(f"Configuration saved to {file_path}")

    # Print the content for verification (debug level)
    logger.info("\nGenerated azure.spc content:")
    logger.info("------------------------")
    logger.info(config_content)
    logger.info("------------------------")
    
    return saved_paths[0]  # Return the first path for compatibility


def generate_and_save_config(credentials=None, is_cloud_run=False, tenant_id=None):
    """
    Helper function to generate and save configuration for both environments
    
    Args:
        credentials: Azure credentials object (optional)
        is_cloud_run: Boolean indicating if running in Cloud Run environment
        tenant_id: Azure tenant ID to filter subscriptions (optional)
    """
    
    if tenant_id:
        logger.info(f"Using tenant ID: {tenant_id}")
    
    # Using the same method for both local and cloud environments
    logger.info(f"Getting Azure subscriptions using credentials")
    subscriptions = get_azure_subscriptions(credentials, tenant_id)
        
    if not subscriptions:
        logger.warning("No subscriptions found or an error occurred.")
        return None
    
    logger.info(f"Found {len(subscriptions)} subscriptions to configure")
        
    config_content = generate_steampipe_config(subscriptions)
    return save_config(config_content, is_cloud_run)

# Main Execution
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--cloud-run', action='store_true',
                       help='Run in Cloud Run mode')
    parser.add_argument('--tenant-id', type=str,
                       help='Azure tenant ID to filter subscriptions')
    args = parser.parse_args()

    is_cloud_run = args.cloud_run or os.getenv('K_SERVICE') is not None
    tenant_id = args.tenant_id
    
    try:
        # Create default Azure credentials for both modes
        credentials = DefaultAzureCredential()
        
        if is_cloud_run:
            logger.info("Running in Cloud Run environment")
            # Cloud Run/Build environment - just generate config
            generate_and_save_config(credentials=credentials, is_cloud_run=True, tenant_id=tenant_id)
        else:
            logger.info("Running in local environment")
            # Local development - generate config and run Docker Compose
            generate_and_save_config(credentials=credentials, is_cloud_run=False, tenant_id=tenant_id)
    except Exception as e:
        logger.error(f"Error: {e}")