#!/bin/bash

# Powerpipe wrapper script
# This script wraps steampipe to provide powerpipe functionality
# It translates powerpipe benchmark commands to steampipe check commands

# Check if this is a benchmark command
if [ "$1" = "benchmark" ] && [ "$2" = "run" ]; then
    # Replace "benchmark run" with "check"
    shift 2  # Remove "benchmark" and "run"

    # The remaining arguments should be passed to steampipe check
    # but we need to handle the --export flag differently
    args=()
    output_format=""
    export_file=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --export=*)
                # Extract format from --export=json
                output_format="${1#*=}"
                if [ "$output_format" = "json" ]; then
                    # Generate a unique filename for export
                    timestamp=$(date +%Y%m%d_%H%M%S)
                    export_file="/tmp/steampipe_export_${timestamp}.json"
                    args+=("--output=json")
                fi
                shift
                ;;
            --export)
                # Handle --export json (space separated)
                shift
                if [ "$1" = "json" ]; then
                    # Generate a unique filename for export
                    timestamp=$(date +%Y%m%d_%H%M%S)
                    export_file="/tmp/steampipe_export_${timestamp}.json"
                    args+=("--output=json")
                    shift
                fi
                ;;
            *)
                # Pass through all other arguments
                args+=("$1")
                shift
                ;;
        esac
    done

    # Execute steampipe check with the processed arguments and capture output
    if [ -n "$export_file" ]; then
        # Redirect output to file and also capture for display
        steampipe check "${args[@]}" | tee "$export_file"
        exit_code=$?

        # Print the export message that the app expects
        if [ -f "$export_file" ]; then
            echo "File exported to $export_file"
        fi
    else
        # No export, just run normally
        steampipe check "${args[@]}"
        exit_code=$?
    fi

    exit $exit_code
else
    # For all other commands, pass through to steampipe
    exec steampipe "$@"
fi