from flask import Blueprint, request, jsonify, abort
from azure.identity import DefaultAzureCredential, ClientSecretCredential
from google.auth import default
from google.oauth2 import service_account
from google.cloud import secretmanager
from functools import wraps
import os
import sys
import json
from google.auth.credentials import Credentials
import logging
import subprocess
import traceback
sys.path.append('/app/shared')
try:
    from secret_manager import SecretManagerClient, validate_credentials
except ImportError:
    # Fallback for local development
    SecretManagerClient = None
    validate_credentials = None

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create a blueprint
auth = Blueprint('auth', __name__)

# Use a writable directory for authentication files
AUTHENTICATION_FOLDER = os.path.join('/tmp', 'authentication')
SERVICE_ACCOUNT_FILE = os.path.join(AUTHENTICATION_FOLDER, 'azure-service-account.json')

def get_service_credentials(customer_id=None):
    try:
        # Get authentication type from environment variables
        auth_type = os.getenv('AUTH_TYPE', 'sa').lower()  # Default to service account if not specified
        logger.info(f"Using authentication type: {auth_type}")

        # If customer_id is provided and we have secret_manager auth type, try customer-specific credentials
        if auth_type == 'secret_manager' and customer_id and SecretManagerClient:
            logger.info(f"Using Secret Manager for customer: {customer_id}")
            creds, error = get_customer_credentials_from_secret(customer_id)
            if creds:
                return creds, error
            else:
                # If customer is 'test' or credentials not found, fall back to default
                if customer_id == 'test':
                    logger.warning(f"Customer '{customer_id}' credentials not found, using default credentials")
                    auth_type = 'sa'  # Fall back to service account
                else:
                    return None, error
        elif auth_type == 'adc':
            try:
                logger.info("Attempting to use Azure Default Credentials")

                # Check if environment variables are set for service principal
                client_id = os.getenv('AZURE_CLIENT_ID')
                client_secret = os.getenv('AZURE_CLIENT_SECRET')
                tenant_id = os.getenv('AZURE_TENANT_ID')

                if client_id and client_secret and tenant_id:
                    logger.info("Using Azure Service Principal from environment variables")
                    credentials = ClientSecretCredential(
                        tenant_id=tenant_id,
                        client_id=client_id,
                        client_secret=client_secret
                    )
                else:
                    logger.info("Using Azure Default Credential chain")
                    credentials = DefaultAzureCredential()

                # For ADC, return only the credentials object (like old approach)
                return credentials
            except Exception as e:
                logger.error(f"ADC authentication failed: {str(e)}")
                logger.error(f"Detailed error: {traceback.format_exc()}")
                # For ADC, return None on error (like old approach)
                return None
        
        elif auth_type == 'sa':
            try:
                # Initialize Secret Manager client
                credentials, project_id = default()
                secret_client = secretmanager.SecretManagerServiceClient(credentials=credentials)
                
                # Use the customer_id parameter passed to the function, not from environment
                if not customer_id:
                    customer_id = os.getenv('CUSTOMER_ID')
                
                project_id = os.getenv('PROJECT_ID')
                
                if not customer_id:
                    error_msg = "No customer_id provided and CUSTOMER_ID environment variable is not set"
                    logger.error(error_msg)
                    return None, error_msg
                
                secret_id = f"sa-azure-{customer_id}-secops".lower().replace('_', '-')
                name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"

                try:
                    response = secret_client.access_secret_version(request={"name": name})
                    secret_content = response.payload.data.decode("UTF-8")
                except Exception as e:
                    logger.error(f"Failed to access secret: {str(e)}")
                    logger.error(f"Secret path attempted: {name}")
                    logger.error(f"Detailed error: {traceback.format_exc()}")
                    return None, f"Failed to access secret: {str(e)}"

                try:
                    service_account_json = json.loads(secret_content)
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON in secret content: {str(e)}")
                    return None, f"Invalid JSON in secret content: {str(e)}"

                try:
                    os.makedirs(AUTHENTICATION_FOLDER, mode=0o755, exist_ok=True)
                except Exception as e:
                    logger.error(f"Failed to create authentication folder: {str(e)}")
                    logger.error(f"Folder path: {AUTHENTICATION_FOLDER}")
                    return None, f"Failed to create authentication folder: {str(e)}"
                
                file_path = os.path.join(AUTHENTICATION_FOLDER, 'azure-service-account.json')
                logger.info(f"File path: {file_path}")
                
                try:
                    with open(file_path, 'w') as f:
                        json.dump(service_account_json, f, indent=2)
                except Exception as e:
                    logger.error(f"Failed to write service account file: {str(e)}")
                    logger.error(f"File path: {file_path}")
                    return None, f"Failed to write service account file: {str(e)}"

                # Extract Azure service principal credentials
                client_id = service_account_json.get('client_id')
                client_secret = service_account_json.get('client_secret')
                tenant_id = service_account_json.get('tenant_id')

                if not all([client_id, client_secret, tenant_id]):
                    error_msg = "Required fields (client_id, client_secret, tenant_id) not found in service account JSON"
                    logger.error(error_msg)
                    return None, error_msg

                try:
                    # Create Azure client credentials
                    credentials = ClientSecretCredential(
                        tenant_id=tenant_id,
                        client_id=client_id,
                        client_secret=client_secret
                    )
                    
                    logger.info(f"Credential type: ClientSecretCredential")
                    logger.info(f"Created Azure Service Principal credentials")
                    
                    # Store these values in environment variables for other components
                    os.environ['AZURE_TENANT_ID'] = tenant_id
                    # os.environ['AZURE_SUBSCRIPTION_ID'] = subscription_id
                    os.environ['AZURE_CLIENT_ID'] = client_id
                    os.environ['AZURE_CLIENT_SECRET'] = client_secret
                    
                    return credentials

                except Exception as e:
                    logger.error(f"Failed to create credentials from service account file: {str(e)}")
                    logger.error(f"Detailed error: {traceback.format_exc()}")
                    return None, f"Failed to create credentials from service account file: {str(e)}"

            except Exception as e:
                logger.error(f"Service account authentication failed: {str(e)}")
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return None, f"Service account authentication failed: {str(e)}"
                
        else:
            error_msg = f"Unsupported authentication type: {auth_type}. Must be 'adc', 'sa', or 'secret_manager'."
            logger.error(error_msg)
            return None, error_msg
            
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Authentication error: {str(e)}"

def get_customer_credentials_from_secret(customer_id):
    """Get customer-specific Azure credentials from Secret Manager"""
    try:
        # Get project ID
        project_id = os.getenv('GCP_PROJECT_ID')
        if not project_id:
            credentials, project_id = default()
        
        # Initialize Secret Manager client
        sm_client = SecretManagerClient(project_id=project_id)
        
        # Get customer credentials
        creds, error = sm_client.get_customer_credentials(customer_id, 'azure')
        if error:
            logger.error(f"Error getting customer credentials: {error}")
            return None, error
        
        # Validate credentials
        if validate_credentials:
            valid, validation_error = validate_credentials('azure', creds)
            if not valid:
                logger.error(f"Invalid credentials: {validation_error}")
                return None, validation_error
        
        # Clear any Azure-specific environment variables that could interfere
        azure_env_vars = [
            'AZURE_SUBSCRIPTION_ID', 'AZURE_TENANT_ID', 
            'AZURE_CLIENT_ID', 'AZURE_CLIENT_SECRET',
            'AZURE_CREDENTIAL_SCOPE'
        ]
        
        for env_var in azure_env_vars:
            if env_var in os.environ:
                del os.environ[env_var]
        
        # Extract Azure service principal credentials
        client_id = creds['client_id']
        client_secret = creds['client_secret']
        tenant_id = creds['tenant_id']
        
        # Create Azure client credentials
        azure_credentials = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret
        )
        
        # Store these values in environment variables for other components
        os.environ['AZURE_TENANT_ID'] = tenant_id
        os.environ['AZURE_CLIENT_ID'] = client_id
        os.environ['AZURE_CLIENT_SECRET'] = client_secret
        
        # Also store subscription_id if provided
        if 'subscription_id' in creds:
            os.environ['AZURE_SUBSCRIPTION_ID'] = creds['subscription_id']
        
        logger.info(f"Successfully loaded Azure credentials for customer {customer_id}")
        return azure_credentials
        
    except Exception as e:
        logger.error(f"Error with Secret Manager authentication: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Secret Manager authentication failed: {str(e)}"
