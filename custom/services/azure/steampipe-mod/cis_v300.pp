locals {
  cis_v300_common_tags = merge(local.azure_compliance_common_tags, {
    cis         = "true"
    cis_version = "v3.0.0"
  })
}

benchmark "cis_v300" {
  title         = "CIS v3.0.0"
  description   = "The CIS Microsoft Azure Foundations Security Benchmark provides prescriptive guidance for establishing a secure baseline configuration for Microsoft Azure."
  
  children = [
    control.cis_v300_2_1_1,
    control.cis_v300_2_1_2,
    control.cis_v300_3_1_1,
    control.cis_v300_4_1_1,
    control.cis_v300_5_1_1
  ]

  tags = merge(local.cis_v300_common_tags, {
    type = "Benchmark"
  })
}

# Sample controls - these would normally be in separate files
control "cis_v300_2_1_1" {
  title       = "Ensure that standard pricing tier is selected"
  description = "Ensure that standard pricing tier is selected for Azure Security Center."
  query       = query.security_center_standard_pricing_enabled

  tags = merge(local.cis_v300_common_tags, {
    cis_item_id = "2.1.1"
    cis_level   = "2"
    cis_type    = "automated"
    service     = "Azure/SecurityCenter"
  })
}

control "cis_v300_2_1_2" {
  title       = "Ensure that 'Automatic provisioning of monitoring agent' is set to 'On'"
  description = "Enable automatic provisioning of the monitoring agent to collect security data."
  query       = query.security_center_auto_provisioning_on

  tags = merge(local.cis_v300_common_tags, {
    cis_item_id = "2.1.2"
    cis_level   = "2"
    cis_type    = "automated"
    service     = "Azure/SecurityCenter"
  })
}

control "cis_v300_3_1_1" {
  title       = "Ensure that 'Secure transfer required' is set to 'Enabled'"
  description = "Ensure that 'Secure transfer required' is set to 'Enabled' for storage accounts."
  query       = query.storage_account_secure_transfer_required_enabled

  tags = merge(local.cis_v300_common_tags, {
    cis_item_id = "3.1.1"
    cis_level   = "1"
    cis_type    = "automated"
    service     = "Azure/Storage"
  })
}

control "cis_v300_4_1_1" {
  title       = "Ensure that 'Auditing' is set to 'On'"
  description = "Enable auditing on SQL servers."
  query       = query.sql_server_auditing_on

  tags = merge(local.cis_v300_common_tags, {
    cis_item_id = "4.1.1"
    cis_level   = "1"
    cis_type    = "automated"
    service     = "Azure/SQL"
  })
}

control "cis_v300_5_1_1" {
  title       = "Ensure that a Log Profile exists"
  description = "Ensure that a Log Profile exists for the subscription."
  query       = query.monitor_log_profile_enabled

  tags = merge(local.cis_v300_common_tags, {
    cis_item_id = "5.1.1"
    cis_level   = "1"
    cis_type    = "automated"
    service     = "Azure/Monitor"
  })
}
