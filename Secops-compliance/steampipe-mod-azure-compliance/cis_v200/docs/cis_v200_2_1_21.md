## Description

This integration setting enables Microsoft Defender for Cloud Apps (formerly 'Microsoft Cloud App Security' or 'MCAS' - see additional info) to communicate with Microsoft Defender for Cloud.

Microsoft Defender for Cloud offers an additional layer of protection by using Azure Resource Manager events, which is considered to be the control plane for Azure. By analyzing the Azure Resource Manager records, Microsoft Defender for Cloud detects unusual or potentially harmful operations in the Azure subscription environment. Several of the preceding analytics are powered by Microsoft Defender for Cloud Apps. To benefit from these analytics, subscription must have a Cloud App Security license.

Microsoft Defender for Cloud Apps works only with Standard Tier subscriptions.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Defender for Cloud`.
3. Select `Environment Settings` blade.
4. Select the subscription.
5. Select `Integrations`.
6. Check `Allow Microsoft Defender for Cloud Apps to access my data`.
7. Select `Save`.

### From Azure CLI

Use the below command to enable Standard pricing tier for Storage Accounts

```bash
az account get-access-token --query
"{subscription:subscription,accessToken:accessToken}" --out tsv | xargs -L1 bash -c 'curl -X PUT -H "Authorization: Bearer $1" -H "Content-Type: application/json"
https://management.azure.com/subscriptions/<subscription_ID>/providers/Micros oft.Security/settings/MCAS?api-version=2021-06-01 -d@"input.json"'
```

Where input.json contains the Request body json data as mentioned below.

```bash
{
  "id":"/subscriptions/<Your_Subscription_Id>/providers/Microsoft.Security/settings/MCAS",
  "kind": "DataExportSetting",
  "type": "Microsoft.Security/settings",
  "properties": {
    "enabled": true
  }
}
```

### Default Value

With Cloud App Security license, these alerts are enabled by default.
