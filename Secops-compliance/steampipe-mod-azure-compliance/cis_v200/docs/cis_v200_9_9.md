## Description

Periodically, newer versions are released for HTTP either due to security flaws or to include additional functionality. Using the latest HTTP version for web apps to take advantage of security fixes, if any, and/or new functionalities of the newer version.

Newer versions may contain security enhancements and additional functionality. Using the latest version is recommended in order to take advantage of enhancements and new capabilities. With each software installation, organizations need to determine if a given update meets their requirements. They must also verify the compatibility and support provided for any additional software against the update revision that is selected.

HTTP 2.0 has additional performance improvements on the head-of-line blocking problem of old HTTP version, header compression, and prioritization of requests. HTTP 2.0 no longer supports HTTP 1.1's chunked transfer encoding mechanism, as it provides its own, more efficient, mechanisms for data streaming.

## Remediation

### Azure Portal

1. Login to Azure Portal using [https://portal.azure.com](https://portal.azure.com).
2. Go to `App Services`.
3. Click on each App.
4. Under `Setting` section, Click on `Configuration`.
5. Set `HTTP version` to `2.0` under `General settings`.

**NOTE:** Most modern browsers support HTTP 2.0 protocol over TLS only, while non- encrypted traffic continues to use HTTP 1.1. To ensure that client browsers connect to your app with HTTP/2, either buy an App Service Certificate for your app's custom domain or bind a third party certificate.

### From Azure CLI

To set HTTP 2.0 version for an existing app, run the following command:

```bash
az webapp config set --resource-group <RESOURCE_GROUP_NAME> --name <APP_NAME> --http20-enabled true
```

### From PowerShell

To enable HTTP 2.0 version support, run the following command:

```bash
Set-AzWebApp -ResourceGroupName <app resource group> -Name <app name> - Http20Enabled $true
```
