## Description

Ensure that the latest OS patches for all virtual machines are applied.

Windows and Linux virtual machines should be kept updated to:
- Address a specific bug or flaw
- Improve an OS or application’s general stability
- Fix a security vulnerability

The Azure Security Center retrieves a list of available security and critical updates from Windows Update or Windows Server Update Services (WSUS), depending on which service is configured on a Windows VM. The security center also checks for the latest updates in Linux systems. If a VM is missing a system update, the security center will recommend system updates be applied.

## Remediation

Follow Microsoft Azure documentation to apply security patches from the security center. Alternatively, you can employ your own patch assessment and management tool to periodically assess, report, and install the required security patches for your OS.

### Default Value

By default, patches are not automatically deployed.
