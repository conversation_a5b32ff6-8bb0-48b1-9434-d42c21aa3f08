## Description

Enable security alert emails to subscription owners.

Enabling security alert emails to subscription owners ensures that they receive security alert emails from Microsoft. This ensures that they are aware of any potential security issues and can mitigate the risk in a timely fashion.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Defender for Cloud`.
3. Click on `Environment Settings`.
4. <PERSON>lick on the appropriate Management Group, Subscription, or Workspace.
5. Click on `Email notifications`.
6. In the drop down of the `All users with the following roles` field select `Owner`.
7. Click `Save`.

### From Azure CLI

Use the below command to set `Send email also to subscription owners` to `On`.

```bash
az account get-access-token --query
"{subscription:subscription,accessToken:accessToken}" --out tsv | xargs -L1 bash -c 'curl -X PUT -H "Authorization: Bearer $1" -H "Content-Type: application/json"
https://management.azure.com/subscriptions/$0/providers/Microsoft.Security/se curityContacts/default1?api-version=2017-08-01-preview -d@"input.json"'
```

Where `input.json` contains the data below, replacing `valid<PERSON><PERSON><PERSON><PERSON><PERSON>` with a single email address or multiple comma-separated email addresses:

```bash
{
  "id":"/subscriptions/<Your_Subscription_Id>/providers/Microsoft.Security/securityC ontacts/default1",
  "name": "default1",
  "type": "Microsoft.Security/securityContacts",
  "properties": {
    "email": "<validEmailAddress>",
    "alertNotifications": "On",
    "alertsToAdmins": "On",
    "notificationsByRole": "Owner"
  }
}
```

### Default Value

By default, `Owner` is selected.
