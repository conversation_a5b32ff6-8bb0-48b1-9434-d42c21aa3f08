## Description

Transparent Data Encryption (TDE) with Customer-managed key support provides increased transparency and control over the TDE Protector, increased security with an HSM-backed external service, and promotion of separation of duties.

With TDE, data is encrypted at rest with a symmetric key (called the database encryption key) stored in the database or data warehouse distribution. To protect this data encryption key (DEK) in the past, only a certificate that the Azure SQL Service managed could be used. Now, with Customer-managed key support for TDE, the DEK can be protected with an asymmetric key that is stored in the Azure Key Vault. The Azure Key Vault is a highly available and scalable cloud-based key store which offers central key management, leverages FIPS 140-2 Level 2 validated hardware security modules (HSMs), and allows separation of management of keys and data for additional security.

Based on business needs or criticality of data/databases hosted on a SQL server, it is recommended that the TDE protector is encrypted by a key that is managed by the data owner (Customer-managed key).

Customer-managed key support for Transparent Data Encryption (TDE) allows user control of TDE encryption keys and restricts who can access them and when. Azure Key Vault, Azure’s cloud-based external key management system, is the first key management service where TDE has integrated support for Customer-managed keys. With Customer-managed key support, the database encryption key is protected by an asymmetric key stored in the Key Vault. The asymmetric key is set at the server level and inherited by all databases under that server.

## Remediation

### From Azure Console

1. Go to `SQL servers`.
For the desired server instance

2. Click On `Transparent data encryption`.
3. Set `Transparent data encryption` to `Customer-managed key`.
4. Browse through your `key vaults` to Select an existing key or create a new key in the Azure Key Vault.
5. Check `Make selected key the default TDE protector`.

### From Azure CLI

Use the below command to encrypt SQL server's TDE protector with a Customer- managed key

```bash
az sql server tde-key set --resource-group <resourceName> --server <dbServerName> --server-key-type {AzureKeyVault} --kid <keyIdentifier>
```

### From PowerShell

Use the below command to encrypt SQL server's TDE protector with a Customer- managed Key Vault key

```bash
Set-AzSqlServerTransparentDataEncryptionProtector -Type AzureKeyVault -KeyId <KeyIdentifier> -ServerName <ServerName> -ResourceGroupName
<ResourceGroupName>
```

Select `Y` when prompted

### Default Value

By Default, Microsoft managed TDE protector is enabled for a SQL server.