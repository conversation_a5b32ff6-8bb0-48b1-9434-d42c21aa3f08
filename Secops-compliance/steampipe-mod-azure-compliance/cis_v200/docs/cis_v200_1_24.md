## Description

Resource locking is a powerful protection mechanism that can prevent inadvertent modification/deletion of resources within Azure subscriptions/Resource Groups and is a recommended NIST configuration.

Given the resource lock functionality is outside of standard Role Based Access Control(RBAC), it would be prudent to create a resource lock administrator role to prevent inadvertent unlocking of resources.

## Remediation

### From Azure Portal

1. In the Azure portal, open a subscription or resource group where you want the custom role to be assigned.
2. `Select Access control (IAM).`
3. Click `Add`.
4. Select `Add custom role`.
5. In the `Custom Role Name` field enter `Resource Lock Administrator`.
6. In the Description field enter `Can Administer Resource Locks`.
7. For Baseline permissions select `Start from scratch`
8. Select `next`.
9. In the Permissions tab select `Add permissions`.
10. In the Search for a permission box, type in `Microsoft.Authorization/locks` to search for permissions.
11. Select the check box next to the permission `Microsoft.Authorization/locks`.
12. Select `Add`.
13. Select `Review + create`.
14. Select `Create`.
15. Assign the newly created role to the appropriate user.

### From PowerShell:

Below is a power shell definition for a resource lock administrator role created at an Azure Management group level

```bash
Import-Module Az.Accounts
Connect-AzAccount

$role = Get-AzRoleDefinition "User Access Administrator" $role.Id = $null
$role.Name = "Resource Lock Administrator" $role.Description = "Can Administer Resource Locks" $role.Actions.Clear() $role.Actions.Add("Microsoft.Authorization/locks/*") $role.AssignableScopes.Clear()

* Scope at the Management group level Management group

$role.AssignableScopes.Add("/providers/Microsoft.Management/managementGroups/ MG-Name")

New-AzRoleDefinition -Role $role Get-AzureRmRoleDefinition "Resource Lock Administrator"
```
