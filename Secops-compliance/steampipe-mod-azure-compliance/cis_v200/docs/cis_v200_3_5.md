## Description

The Storage Queue service stores messages that may be read by any client who has access to the storage account. A queue can contain an unlimited number of messages, each of which can be up to 64KB in size using version 2011-08-18 or newer. Storage Logging happens server-side and allows details for both successful and failed requests to be recorded in the storage account. These logs allow users to see the details of read, write, and delete operations against the queues. Storage Logging log entries contain the following information about individual requests: Timing information such as start time, end-to-end latency, and server latency, authentication details, concurrency information, and the sizes of the request and response messages.

Storage Analytics logs contain detailed information about successful and failed requests to a storage service. This information can be used to monitor individual requests and to diagnose issues with a storage service. Requests are logged on a best-effort basis.

Storage Analytics logging is not enabled by default for your storage account.

## Remediation

### From Azure Portal

1. Go to `Storage Accounts`.
2. Select the specific Storage Account.
3. Click the `Diagnostics settings (classic)` blade from `Monitoring (classic)` section.
4. Set the `Status` to `On`, if set to `Off`.
5. Select `Queue properties`.
6. Select `Read`, `Write` and `Delete` options under the `Logging` section to enable Storage Logging for Queue service.

### From Azure CLI

Use the below command to enable the Storage Logging for Queue service.

```bash
az storage logging update --account-name <storageAccountName> --account-key <storageAccountKey> --services q --log rwd --retention 90
```

### Default Value

By default storage account queue services are not logged.
