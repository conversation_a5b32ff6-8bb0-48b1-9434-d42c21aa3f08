## Description

SQL Server Audit Retention should be configured to be greater than 90 days.

Audit Logs can be used to check for anomalies and give insight into suspected breaches or misuse of information and access.

## Remediation

### From Azure Console

1. Go to `SQL servers`.
2. For each server instance.
3. Click on `Auditing`.
4. If storage is selected, expand `Advanced properties`.
5. Set the `Retention (days)` setting greater than `90` days or `0` for unlimited retention.
6. Select `Save`.

### From Powershell

For each Server, set retention policy to more than 90 days

### Log Analytics Example

```bash
Set-AzSqlServerAudit -ResourceGroupName <resource group name> -ServerName <SQL Server name> -RetentionInDays <Number of Days to retain the audit logs, should be more than 90 days> -LogAnalyticsTargetState Enabled - WorkspaceResourceId "/subscriptions/<subscription ID>/resourceGroups/insights- integration/providers/Microsoft.OperationalInsights/workspaces/<workspace name>
```

### Event Hub Example

```bash
Set-AzSqlServerAudit -ResourceGroupName "<resource group name>" -ServerName "<SQL Server name>" -EventHubTargetState Enabled -EventHubName
"<Event Hub name>" -EventHubAuthorizationRuleResourceId "<Event Hub Authorization Rule Resource ID>"
```

### Blob Storage Example*

```bash
Set-AzSqlServerAudit -ResourceGroupName "<resource group name>" -ServerName "<SQL Server name>" -BlobStorageTargetState Enabled
-StorageAccountResourceId "/subscriptions/<subscription_ID>/resourceGroups/<Resource_Group>/providers/M icrosoft.Stora
ge/storageAccounts/<Storage Account name>"
```

### Default Value

By default, SQL Server audit storage is `disabled`.
