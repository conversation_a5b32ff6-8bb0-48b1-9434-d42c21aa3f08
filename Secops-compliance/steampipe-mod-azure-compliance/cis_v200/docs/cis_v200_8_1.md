## Description

Ensure that all Keys in Role Based Access Control (RBAC) Azure Key Vaults have an expiration date set.

Azure Key Vault enables users to store and use cryptographic keys within the Microsoft Azure environment. The `exp` (expiration date) attribute identifies the expiration date on or after which the key MUST NOT be used for encryption of new data, wrapping of new keys, and signing. By default, keys never expire. It is thus recommended that keys be rotated in the key vault and set an explicit expiration date for all keys to help enforce the key rotation. This ensures that the keys cannot be used beyond their assigned lifetimes.

## Remediation

### From Azure Portal

1. Go to `Key vaults`.
2. For each Key vault, click on `Keys`.
3. In the main pane, ensure that an appropriate `Expiration date` is set for any keys that are `Enabled`.

### From Azure CLI

Update the `Expiration date` for the secret using the below command:

```bash
az keyvault key set-attributes --name <keyName> --vault-name <vaultName> -- expires Y-m-d'T'H:M:S'Z'
```

**Note:** To view the expiration date on all keys in a Key Vault using Microsoft API, the "List" Key permission is required.

To update the expiration date for the keys:
1. Go to the Key vault, click on Access Control (IAM).
2. Click on Add role assignment and assign the role of Key Vault Crypto Officer to the appropriate user.

### From PowerShell

```bash
Set-AzKeyVaultKeyAttribute -VaultName <VaultName> -Name <KeyName> -Expires <DateTime>
```

### Default Value

By default, keys do not expire.
