## Overview

Microsoft Defender for SQL provides a layer of security which enables customers to detect and respond to potential threats as they occur through security alerts on anomalous activities. Users will receive an alert upon suspicious database activities, potential vulnerabilities, and SQL injection attacks, as well as anomalous database access patterns. SQL Server Threat Detection alerts provide details of suspicious activity and recommend action on how to investigate and mitigate the threat.

Microsoft Defender for SQL may incur additional cost per SQL server.
