## Description

Ensure that OS disks (boot volumes) and data disks (non-boot volumes) are encrypted with CMK (Customer Managed Keys). Customer Managed keys can be either ADE or Server Side Encryption (SSE).

Encrypting the IaaS VM's OS disk (boot volume) and Data disks (non-boot volume) ensures that the entire content is fully unrecoverable without a key, thus protecting the volume from unwanted reads. PMK (Platform Managed Keys) are enabled by default in Azure-managed disks and allow encryption at rest. CMK is recommended because it gives the customer the option to control which specific keys are used for the encryption and decryption of the disk. The customer can then change keys and increase security by disabling them instead of relying on the PMK key that remains unchanging. There is also the option to increase security further by using automatically rotating keys so that access to disk is ensured to be limited. Organizations should evaluate what their security requirements are, however, for the data stored on the disk. For high-risk data using CMK is a must, as it provides extra steps of security. If the data is low risk, PMK is enabled by default and provides sufficient data security.

## Remediation

### From Azure Portal

**Note:** Disks must be detached from VMs to have encryption changed.

1. Go to `Virtual machines`.
2. For each virtual machine, go to `Settings`.
3. Click on `Disks`.
4. Click the ellipsis (...), then click `Detach` to detach the disk from the VM.
5. Now search for `Disks` and locate the unattached disk.
6. Click the disk then select `Encryption`.
7. Change your encryption type, then select your encryption set.
8. Click `Save`.
9. Go back to the VM and re-attach the disk.

### From PowerShell

```bash
$KVRGname = 'MyKeyVaultResourceGroup';
$VMRGName = 'MyVirtualMachineResourceGroup';
$vmName = 'MySecureVM';
$KeyVaultName = 'MySecureVault';
$KeyVault = Get-AzKeyVault -VaultName $KeyVaultName -ResourceGroupName
$KVRGname;
$diskEncryptionKeyVaultUrl = $KeyVault.VaultUri; $KeyVaultResourceId = $KeyVault.ResourceId;
Set-AzVMDiskEncryptionExtension -ResourceGroupName $VMRGname -VMName $vmName -DiskEncryptionKeyVaultUrl $diskEncryptionKeyVaultUrl - DiskEncryptionKeyVaultId $KeyVaultResourceId;
```

**NOTE:** During encryption it is likely that a reboot will be required. It may take up to 15 minutes to complete the process.

**NOTE 2:** This may differ for Linux machines as you may need to set the `-skipVmBackup` parameter

### Default Value

By default, Azure disks are encrypted using SSE with PMK.
