## Description

Create an Activity Log Alert for the Create or Update Network Security Group event.

Monitoring for Create or Update Network Security Group events gives insight into network access changes and may reduce the time it takes to detect suspicious activity.

## Remediation

### From Azure Portal

1. Navigate to the `Monitor` blade.
2. Select `Alerts`.
3. Select `Create`.
4. Select `Alert rule`.
5. Under `Filter by subscription`, choose a subscription.
6. Under `Filter by resource type`, select `Network security groups`.
7. Under `Filter by location`, select `All`.
8. From the results, select the subscription.
9. Select `Done`.
10. Select the `Condition` tab.
11. Under `Signal name`, click `Create or Update Network Security Group (Microsoft.Network/networkSecurityGroups)`.
12. Select the `Actions` tab.
13. To use an existing action group, click `Select action groups`. To create a new action group, click `Create action group`. Fill out the appropriate details for the selection.
14. Select the `Details` tab.
15. Select a `Resource group`, provide an `Alert rule name` and an optional `Alert rule description`.
16. Click `Review + create`.
17. Click `Create`.

### From Azure CLI

```bash
az monitor activity-log alert create --resource-group "<resource group name>" --condition category=Administrative and operationName=Microsoft.Network/networkSecurityGroups/write and level=verbose --scope "/subscriptions/<subscription ID>" --name "<activity log rule name>" --subscription <subscription id> --action-group <action group ID> --location global
```

### From PowerShell

Create the `Conditions` object.

```bash
$conditions = @()
$conditions += New-AzActivityLogAlertAlertRuleAnyOfOrLeafConditionObject - Equal Administrative -Field category
$conditions += New-AzActivityLogAlertAlertRuleAnyOfOrLeafConditionObject - Equal Microsoft.Network/networkSecurityGroups/write -Field operationName $conditions += New-AzActivityLogAlertAlertRuleAnyOfOrLeafConditionObject - Equal Verbose -Field level
```

Retrieve the `Action Group` information and store in a variable, then create the `Actions` object.

```bash
$actionGroup = Get-AzActionGroup -ResourceGroupName <resource group name> - Name <action group name>
$actionObject = New-AzActivityLogAlertActionGroupObject -Id $actionGroup.Id
```

Create the `Scope` object

```bash
$scope = "/subscriptions/<subscription id>"
```

Create the `Activity Log Alert Rule` for `Microsoft.Network/networkSecurityGroups/write`

```bash
New-AzActivityLogAlert -Name "<activity log alert rule name>" - ResourceGroupName "<resource group name>" -Condition $conditions -Scope $scope -Location global -Action $actionObject -Subscription <subscription ID> -Enabled $true
```

### Default Value

By default, no monitoring alerts are created.
