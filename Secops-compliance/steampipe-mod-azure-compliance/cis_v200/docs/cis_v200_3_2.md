## Description

Enabling double encryption at the hardware level on top of the default software encryption for Storage Accounts accessing Azure storage solutions.

Azure Storage automatically encrypts all data in a storage account at the network level using 256-bit AES encryption, which is one of the strongest, FIPS 140-2-compliant block ciphers available. Customers who require higher levels of assurance that their data is secure can also enable 256-bit AES encryption at the Azure Storage infrastructure level for double encryption. Double encryption of Azure Storage data protects against a scenario where one of the encryption algorithms or keys may be compromised. Similarly, data is encrypted even before network transmission and in all backups. In this scenario, the additional layer of encryption continues to protect your data. For the most secure implementation of key based encryption, it is recommended to use a Customer Managed asymmetric RSA 2048 Key in Azure Key Vault.

## Remediation

### From Azure Portal

1. When creating a storage account, proceed as normal, but stop on the `Advanced` tab.
2. Select `Enabled` next to Infrastructure Encryption.

### From Azure CLI

Replace the information within &lt;&gt; with your values.

```bash
az storage account create \
 --name <storage-account> \
 --resource-group <resource-group> \
 --location <location> \
 --sku Standard_RAGRS \
 --kind StorageV2 \
 --require-infrastructure-encryption
```

### From Powershell

```bash
New-AzStorageAccount -ResourceGroupName <resource_group> `
 -AccountName <storage-account> `
 -Location <location> `
 -SkuName "Standard_RAGRS" `
 -Kind StorageV2 `
 -RequireInfrastructureEncryption
```

`Enabling Infrastructure Encryption after Storage Account Creation`
If a infrastructure encryption was not enabled on blob storage creation, there is no official way to enable it. Please see the additional information section.

### Default Value

By default, Infrastructure Encryption is disabled in blob creation.