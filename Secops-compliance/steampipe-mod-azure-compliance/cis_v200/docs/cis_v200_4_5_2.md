## Description

Private endpoints limit network traffic to approved sources.

For sensitive data, private endpoints allow granular control of which services can communicate with Cosmos DB and ensure that this network traffic is private. You set this up on a case by case basis for each service you wish to be connected.

## Remediation

### From Azure Portal

1. Open the portal menu.
2. Select the Azure Cosmos DB blade.
3. Select the Azure Cosmos DB account.
4. Select `Networking`.
5. Select `Private access`.
6. Click `+ Private Endpoint`.
7. Provide a Name.
8. Click `Next`.
9. From the Resource type drop down, select `Microsoft.AzureCosmosDB/databaseAccounts`.
10. From the Resource drop down, select the Cosmos DB account.
11. Click `Next`.
12. Provide appropriate Virtual Network details.
13. Click `Next`.
14. Provide appropriate DNS details.
15. Click `Next`.
16. Optionally provide Tags.
17. Click `Next` : `Review + create`.
18. Click `Create`.

### Default Value

By default Cosmos DB does not have private endpoints enabled and its traffic is public to the network.
