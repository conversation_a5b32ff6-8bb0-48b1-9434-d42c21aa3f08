## Description

Enable Diagnostic settings for exporting activity logs. Diagnostic settings are available for each individual resource within a subscription. Settings should be configured for all appropriate resources for your environment.

A diagnostic setting controls how a diagnostic log is exported. By default, logs are retained only for 90 days. Diagnostic settings should be defined so that logs can be exported and stored for a longer duration in order to analyze security activities within an Azure subscription.

## Remediation

### From Azure Portal

To enable Diagnostic Settings on a Subscription:

1. Go to `Monitor`.
2. Click on `Activity Log`.
3. <PERSON>lick on `Export Activity Logs`.
4. Click `+ Add diagnostic setting`.
5. Enter a `Diagnostic setting name`.
6. Select `Categories` for the diagnostic settings.
7. Select the appropriate `Destination details` (this may be Log Analytics/Storage Account/Event Hub or Partner solution).
8. Click `Save`.

To enable Diagnostic Settings on a specific resource:

1. Go to `Monitor`.
2. Click `Diagnostic settings`.
3. Click on the resource that has a diagnostics status of `disabled`.
4. Select `Add Diagnostic Setting`.
5. Enter a `Diagnostic setting name`.
6. Select the appropriate log, metric, and destination. (This may be Log Analytics/Storage account or Event Hub).
7. Click `save`.

Repeat these step for all resources as needed.

### From Azure CLI

To configure Diagnostic Settings on a Subscription:

```bash
az monitor diagnostic-settings subscription create --subscription <subscription id> --name <diagnostic settings name> --location <location> <[- -event-hub <event hub ID> --event-hub-auth-rule <event hub auth rule ID>] [-- storage-account <storage account ID>] [--workspace <log analytics workspace ID>] --logs "<JSON encoded categories>" (e.g. [{category:Security,enabled:true},{category:Administrative,enabled:true},{cat egory:Alert,enabled:true},{category:Policy,enabled:true}])
```

To configure Diagnostic Settings on a specific resource:

```bash
az monitor diagnostic-settings create --subscription <subscription ID> -- resource <resource ID> --name <diagnostic settings name> <[--event-hub <event hub ID> --event-hub-rule <event hub auth rule ID>] [--storage-account <storage account ID>] [--workspace <log analytics workspace ID>] --logs <resource specific JSON encoded log settings> --metrics <metric settings (shorthand|json-file|yaml-file)>
```

### From PowerShell

To configure Diagnostic Settings on a subscription:

```bash
$logCategories = @();
$logCategories += New-AzDiagnosticSettingSubscriptionLogSettingsObject - Category Administrative -Enabled $true
$logCategories += New-AzDiagnosticSettingSubscriptionLogSettingsObject - Category Security -Enabled $true
$logCategories += New-AzDiagnosticSettingSubscriptionLogSettingsObject - Category ServiceHealth -Enabled $true
$logCategories += New-AzDiagnosticSettingSubscriptionLogSettingsObject - Category Alert -Enabled $true
$logCategories += New-AzDiagnosticSettingSubscriptionLogSettingsObject - Category Recommendation -Enabled $true
$logCategories += New-AzDiagnosticSettingSubscriptionLogSettingsObject -
Category Policy -Enabled $true
$logCategories += New-AzDiagnosticSettingSubscriptionLogSettingsObject - Category Autoscale -Enabled $true
$logCategories += New-AzDiagnosticSettingSubscriptionLogSettingsObject - Category ResourceHealth -Enabled $true

New-AzSubscriptionDiagnosticSetting -SubscriptionId <subscription ID> -Name <Diagnostic settings name> <[-EventHubAuthorizationRule <event hub auth rule ID> -EventHubName <event hub name>] [-StorageAccountId <storage account ID>] [-WorkSpaceId <log analytics workspace ID>] [-MarketplacePartner ID <full ARM Marketplace resource ID>]> -Log $logCategories
```

To configure Diagnostic Settings on a specific resource:

```bash
$logCategories = @()
$logCategories += New-AzDiagnosticSettingLogSettingsObject -Category <resource specific log category> -Enabled $true

Repeat command and variable assignment for each Log category specific to the resource where this Diagnostic Setting will get configured.

$metricCategories = @()
$metricCategories += New-AzDiagnosticSettingMetricSettingsObject -Enabled $true [-Category <resource specific metric category | AllMetrics>] [- RetentionPolicyDay <Integer>] [-RetentionPolicyEnabled $true]

Repeat command and variable assignment for each Metric category or use the 'AllMetrics' category.

New-AzDiagnosticSetting -ResourceId <resource ID> -Name <Diagnostic settings name> -Log $logCategories -Metric $metricCategories [- EventHubAuthorizationRuleId <event hub auth rule ID> -EventHubName <event hub name>] [-StorageAccountId <storage account ID>] [-WorkspaceId <log analytics workspace ID>] [-MarketplacePartnerId <full ARM marketplace resource ID>]>
```

### Default Value

By default, diagnostic setting is not set.
