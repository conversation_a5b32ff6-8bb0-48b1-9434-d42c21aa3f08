## Description

Security defaults in Azure Active Directory (Azure AD) make it easier to be secure and help protect your organization. Security defaults contain preconfigured security settings for common attacks.

Security defaults is available to everyone. The goal is to ensure that all organizations have a basic level of security enabled at no extra cost. You may turn on security defaults in the Azure portal.

Security defaults provide secure default settings that we manage on behalf of organizations to keep customers safe until they are ready to manage their own identity security settings.
For example, doing the following:
- Requiring all users and admins to register for MFA.
- Challenging users with MFA - when necessary, based on factors such as location, device, role, and task.
- Disabling authentication from legacy authentication clients, which can’t do MFA.

## Remediation

### From Azure Portal

To enable security defaults in your directory:

1. From Azure Home select the Portal Menu.
2. Browse to `Azure Active Directory` > `Properties`.
3. Select `Manage security defaults`.
4. Set the `Enable security defaults toggle to Yes`.
5. Select `Save`.

### Default Value

If your tenant was created on or after October 22, 2019, security defaults may already be enabled in your tenant.
