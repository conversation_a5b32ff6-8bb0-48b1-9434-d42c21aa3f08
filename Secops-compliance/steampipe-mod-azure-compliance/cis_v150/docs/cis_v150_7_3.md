## Description

Ensure that unattached disks in a subscription are encrypted with a Customer Managed Key (CMK).

Managed disks are encrypted by default with Platform-managed keys. Using Customermanaged keys may provide an additional level of security or meet an organization's regulatory requirements. Encrypting managed disks ensures that its entire content is fully unrecoverable without a key and thus protects the volume from unwarranted reads. Even if the disk is not attached to any of the VMs, there is always a risk where a compromised user account with administrative access to VM service can mount/attach these data disks, which may lead to sensitive information disclosure and tampering.

## Remediation

If data stored in the disk is no longer useful, refer to Azure documentation to delete unattached data disks at:

```bash
- https://docs.microsoft.com/en-us/rest/api/compute/disks/delete
- https://docs.microsoft.com/en-us/cli/azure/disk?view=azure-cli-latest#azdisk-delete
```

If data stored in the disk is important, To encrypt the disk refer azure documentation at:

```bash
- https://docs.microsoft.com/en-us/azure/virtual-machines/disks-enablecustomer-managed-keys-portal
- https://docs.microsoft.com/enus/rest/api/compute/disks/update#encryptionsettings
```

### Default Value

By default, managed disks are encrypted with a Platform-managed key.