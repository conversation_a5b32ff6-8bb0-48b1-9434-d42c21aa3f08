## Description

Expire shared access signature tokens within an hour.

A shared access signature (SAS) is a URI that grants restricted access rights to Azure Storage resources. A shared access signature can be provided to clients who should not be trusted with the storage account key but for whom it may be necessary to delegate access to certain storage account resources. Providing a shared access signature URI to these clients allows them access to a resource for a specified period of time. This time should be set as low as possible and preferably no longer than an hour.

## Remediation

When generating shared access signature tokens, use start and end time such that it falls within an hour.

### From Azure Portal

1. Go to Storage Accounts
2. For each storage account, go to Shared access signature
3. Set Start and expiry date/time within an hour

**Note:** At this point of time, there is no API/CLI mechanism available to programmatically conduct security assessment for this recommendation.

### Default Value

By default, expiration for shared access signature is set to 8 hours.