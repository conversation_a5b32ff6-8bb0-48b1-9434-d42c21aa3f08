## Description

Azure Key Vault will store multiple types of sensitive information such as encryption keys, certificate thumbprints, and Managed Identity Credentials. Access to these 'Secrets' can be controlled through granular permissions.

The credentials given to an application have permissions to create, delete, or modify data stored within the systems they access. If these credentials are stored within the application itself, anyone with access to the application or a copy of the code has access to them. Storing within Azure Key Vault as secrets increases security by controlling access. This also allows for updates of the credentials without redeploying the entire application.

## Remediation

Remediation has 2 steps
1. Set up the Key Vault
2. Set up the App Service to use the Key Vault

### Step 1: Set up the Key Vault

### From Azure CLI

```bash
az keyvault create --name "<name>" --resource-group "<myResourceGroup>" -- location myLocation
```

### From Powershell

```bash
New-AzKeyvault -name <name> -ResourceGroupName <myResourceGroup> -Location <myLocation>
```

### Step 2: Set up the App Service to use the Key Vault

Sample JSON Template for App Service Configuration:

```json
{
   "resources":[
      {
        "type":"Microsoft.Storage/storageAccounts",
        "name":"[variables('storageAccountName')]",
      },
      {
        "type":"Microsoft.Insights/components",
        "name":"[variables('appInsightsName')]",
      },
      {
        "type":"Microsoft.Web/sites",
        "name":"[variables('functionAppName')]",
        "identity":{
            "type":"SystemAssigned"
         },
         "resources":[
            {
               "type":"config",
               "name":"appsettings",
               "dependsOn":[
                  "[resourceId('Microsoft.Web/sites', variables('functionAppName'))]",
                  "[resourceId('Microsoft.KeyVault/vaults/', variables('keyVaultName'))]",
                  "[resourceId('Microsoft.KeyVault/vaults/secrets', variables('keyVaultName'), variables('storageConnectionStringName'))]",
                  "[resourceId('Microsoft.KeyVault/vaults/secrets', variables('keyVaultName'), variables('appInsightsKeyName'))]"
               ],
               "properties":{
                  "AzureWebJobsStorage":"[concat('@Microsoft.KeyVault(SecretUri=', reference(variables('storageConnectionStringResourceId')).secretUriWithVersio n, ')')]",
                  "WEBSITE_CONTENTAZUREFILECONNECTIONSTRING":"[concat('@Microsoft.KeyVault(SecretUri=',
    reference(variables('storageConnectionStringResourceId')).secretUriWithVersio n, ')')]",
                  "APPINSIGHTS_INSTRUMENTATIONKEY":"[concat('@Microsoft.KeyVault(SecretUri=',
    reference(variables('appInsightsKeyResourceId')).secretUriWithVersion, ')')]",
                  "WEBSITE_ENABLE_SYNC_UPDATE_SITE":"true"
               }
            },
            {
               "type":"sourcecontrols",
               "name":"web",
               "dependsOn":[
                  "[resourceId('Microsoft.Web/sites', variables('functionAppName'))]",
                  "[resourceId('Microsoft.Web/sites/config', variables('functionAppName'), 'appsettings')]"
               ]
            }{
               "type":"Microsoft.KeyVault/vaults",
               "name":"[variables('keyVaultName')]",
            ]
         },
         "dependsOn":[
            "[resourceId('Microsoft.Web/sites',
    variables('functionAppName'))]"
         ],
         "properties":{
            "accessPolicies":[
               {
                  "tenantId":"[reference(concat('Microsoft.Web/sites/', variables('functionAppName'),
    '/providers/Microsoft.ManagedIdentity/Identities/default'), '2015-08-31- PREVIEW').tenantId]",
                  "objectId":"[reference(concat('Microsoft.Web/sites/', variables('functionAppName'),
    '/providers/Microsoft.ManagedIdentity/Identities/default'), '2015-08-31- PREVIEW').principalId]",
                  "permissions":{
                     "secrets":[
                        "get"
                     ]
                  }
               }"resources":[
                  {
                     "type":"secrets"
                  ]
               },
               "name":"[variables('storageConnectionStringName')]",
               "dependsOn":[
                  "[resourceId('Microsoft.KeyVault/vaults/', variables('keyVaultName'))]",
                  "[resourceId('Microsoft.Storage/storageAccounts', variables('storageAccountName'))]"
               ],
               "properties":{
                  "value":"[concat('DefaultEndpointsProtocol=https;AccountName=',
    variables('storageAccountName'), ';AccountKey=', listKeys(variables('storageAccountResourceId'),'2015-05-01-preview').key1)]"
               }
            },
            {
               "type":"secrets",
               "name":"[variables('appInsightsKeyName')]",
               "dependsOn":[
                  "[resourceId('Microsoft.KeyVault/vaults/', variables('keyVaultName'))]",
                  "[resourceId('Microsoft.Insights/components', variables('appInsightsName'))]"
               ],
               "properties":{
                  "value":"[reference(resourceId('microsoft.insights/components/',
    variables('appInsightsName')), '2015-05-01').InstrumentationKey]"
               }
            }
         ]
      }
   ]
}
```

### Default Value

By default, no Azure Key Vaults are created.