## Description

Enable `SSL connection` on `MYSQL` Servers.

SSL connectivity helps to provide a new layer of security by connecting database server to client applications using Secure Sockets Layer (SSL). Enforcing SSL connections between database server and client applications helps protect against "man in the middle" attacks by encrypting the data stream between the server and application.

## Remediation

### From Azure Portal

1. Login to Azure Portal using https://portal.azure.com
2. Go to Azure Database for `MySQL servers`
3. For each database, click on `Connection security`
4. In `SSL` settings, click on `ENABLED` to `Enforce SSL connections`

### From Azure CLI

Use the below command to set MYSQL Databases to Enforce SSL connection

```bash
az mysql server update --resource-group <resourceGroupName> --name <serverName> --ssl-enforcement Enabled
```

### Default Value

Azure Database for MySQL when provisioned through the Azure portal or CLI will require SSL connections by default.