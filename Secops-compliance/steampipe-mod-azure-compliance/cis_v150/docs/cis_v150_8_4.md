## Description

Ensure that all Secrets in Non Role Based Access Control (RBAC) Azure Key Vaults have an expiration time set.

The Azure Key Vault enables users to store and keep secrets within the Microsoft Azure environment. Secrets in the Azure Key Vault are octet sequences with a maximum size of 25k bytes each. The exp (expiration time) attribute identifies the expiration time on or after which the secret MUST NOT be used. By default, secrets never expire. It is thus recommended to rotate secrets in the key vault and set an explicit expiration time for all secrets. This ensures that the secrets cannot be used beyond their assigned lifetimes.

## Remediation

### From Azure Portal

1. Go to `Key vaults`
2. For each Key vault, click on `Secrets`.
3. Under the `Settings` section, Make sure `Enabled` is set to `Yes`
4. Set an appropriate `EXPIRATION DATE` on all secrets.

### From Azure CLI

Use the below command to set `EXPIRATION DATE` on the all secrets

```bash
az keyvault secret set-attributes --name <secretName> --vault-name <vaultName> --expires Y-m-d'T'H:M:S'Z'
```

### From PowerShell

For each Key Vault with the `EnableRbacAuthorization` setting set to `False` or empty, run the following command.

```bash
Set-AzKeyVaultSecret -VaultName <Vault Name> -Name <Secret Name> -Expires <DateTime>
```

### Default Value

By default, secrets do not expire.
