## Description

Private endpoints will secure network traffic from Azure Key Vault to the resources requesting secrets and keys.

Private endpoints will keep network requests to Azure Key Vault limited to the endpoints attached to the resources that are whitelisted to communicate with each other. Assigning the Key Vault to a network without an endpoint will allow other resources on that network to view all traffic from the Key Vault to its destination. In spite of the complexity in configuration, this is recommended for high security secrets.

## Remediation

Please see the additional information about the requirements needed before starting this remediation procedure.

### From Azure Portal

1. From Azure Home open the Portal Menu in the top left.
2. Select Key Vaults.
3. Select each Key Vault to be audited.
4. Select `Networking` in the left column.
5. Then select `Private End points` in the row menu.
6. Select `+ Create`
7. Select the subscription your Key Vault is within, and other desired configuration.
8. Select `Next`
9. For resource type select `Microsoft.KeyVault/vaults`.
10. Select the Key Vault you wish to associate the Private Endpoint to.
11. In the `Virtual Networking` field, select the network you wish to assign the Endpoint. Or if creating a new network name it here.
12. Select other configuration options as desired, including an existing or new application security group.
13. Select the private DNS the Private Endpoints will use.
14. Review this information and select `Create`. Follow the Audit Procedure to determine if it has successfully applied.

### From Azure CLI

1. To create an end point, run the following command:

```bash
az network private-endpoint create --resource-group <resourceGroup --vnet- name <vnetName> --subnet <subnetName> --name <PrivateEndpointName> -- private-connection-resource-id "/subscriptions/<AZURE SUBSCRIPTION
ID>/resourceGroups/<resourceGroup>/providers/Microsoft.KeyVault/vaults/<keyVa ultName>" --group-ids vault --connection-name <privateLinkConnectionName> -- location <azureRegion> --manual-request
```

2. To manually approve the end point request, run the following command:

```bash
az keyvault private-endpoint-connection approve --resource-group <resourceGroup> --vault-name <keyVaultName> –name <privateLinkName>
```

3. Determine the Private Endpoint's IP address to connect the Key Vault to the Private DNS you have previously created:

4. Look for the property networkInterfaces then id; the value must be placed in the variable &lt;privateEndpointNIC&gt; within step 6.

```bash
az network private-endpoint show -g <resourceGroupName> -n <privateEndpointName>
```

5. Look for the property networkInterfaces then id; the value must be placed on &lt;privateEndpointNIC&gt; in step 6.

```bash
az network nic show --ids <privateEndpointName>
```

6. Create a Private DNS record within the DNS Zone you created for the Private Endpoint:

```bash
az network private-dns record-set a add-record -g <resourcecGroupName> -z "privatelink.vaultcore.azure.net" -n <keyVaultName> -a <privateEndpointNIC>
```

7. nslookup the private endpoint to determine if the DNS record is correct:

```bash
nslookup <keyVaultName>.vault.azure.net
nslookup <keyVaultName>.privatelink.vaultcore.azure.n
```

### Default Value

By default, Private Endpoints are not enabled for any services within Azure.
