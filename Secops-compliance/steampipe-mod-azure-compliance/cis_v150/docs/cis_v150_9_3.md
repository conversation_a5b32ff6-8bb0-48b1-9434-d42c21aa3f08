## Description

The TLS (Transport Layer Security) protocol secures transmission of data over the internet using standard encryption technology. Encryption should be set with the latest version of TLS. App service allows TLS 1.2 by default, which is the recommended TLS level by industry standards such as PCI DSS.

App service currently allows the web app to set TLS versions 1.0, 1.1 and 1.2. It is highly recommended to use the latest TLS 1.2 version for web app secure connections.

## Remediation

### From Azure Portal

1. Login to Azure Portal using https://portal.azure.com
2. Go to `App Services`
3. Click on each App
4. Under `Setting` section, Click on `SSL settings`
5. Under the `Bindings` pane, set `Minimum TLS Version` to `1.2` under `Protocol Settings` section

### From Azure CLI

To set TLS Version for an existing app, run the following command:

```bash
az webapp config set --resource-group <RESOURCE_GROUP_NAME> --name <APP_NAME> --min-tls-version 1.2
```

### From Powershell

```bash
Set-AzWebApp -ResourceGroupName <RESOURCE_GROUP_NAME> -Name <APP_NAME> - MinTlsVersion 1.2
```

### Default Value

By default, TLS Version feature will be set to 1.2 when a new app is created using the command-line tool or Azure Portal console.