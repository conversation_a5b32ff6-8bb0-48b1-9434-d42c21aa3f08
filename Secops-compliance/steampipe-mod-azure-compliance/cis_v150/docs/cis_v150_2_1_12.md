## Description

Microsoft Defender for IoT acts as a central security hub for IoT devices within your organization.

IoT devices are very rarely patched and can be potential attack vectors for enterprise networks. Updating their network configuration to use a central security hub allows for detection of these breaches.

## Remediation

### From Azure Portal

1. Go to `Microsoft Defender for Cloud`
2. Select `Environment Settings` blade
3. Click on the subscription name
4. Select the `Defender plans` blade
5. For the `IoT` resource type `Plan` set the radio button to `On`.

### Default Value

By default, Microsoft Defender for IoT is not enabled.