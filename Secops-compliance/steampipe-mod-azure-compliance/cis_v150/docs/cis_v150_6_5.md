## Description

Network Security Group Flow Logs should be enabled and the retention period set to greater than or equal to 90 days.

Flow logs enable capturing information about IP traffic flowing in and out of network security groups. Logs can be used to check for anomalies and give insight into suspected breaches.

## Remediation

### From Azure Console

1. Go to `Network Watcher`
2. Select `NSG flow logs` blade in the Logs section
3. Select each Network Security Group from the list
4. Ensure `Status` is set to `On`
5. Ensure `Retention(days)` setting `greater than 90 days`
6. Select your storage account in the `Storage account` field
7. Select `Save`

### From Azure CLI

Enable the `NSG flow logs` and set the Retention (days) to greater than or equal to 90 days.

```bash
az network watcher flow-log configure --nsg <NameorID of the Network Security Group> --enabled true --resource-group <resourceGroupName> --retention 91 -- storage-account <NameorID of the storage account to save flow logs>
```

### Default Value

By default, Network Security Group Flow Logs are `disabled`.