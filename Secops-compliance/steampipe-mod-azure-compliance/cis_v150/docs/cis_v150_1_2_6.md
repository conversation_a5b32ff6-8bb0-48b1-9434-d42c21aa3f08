## Description

For designated users, they will be prompted to use their multi-factor authentication (MFA) process on logins.

Enabling multi-factor authentication is a recommended setting to limit the use of Administrative actions and to prevent intruders from changing settings.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu and select `Azure Active Directory`.
2. Scroll down in the menu on the left, and select `Security`.
3. Select on the left side `Conditional Access`.
4. Click the `+ New policy`

### Default Value

MFA is not enabled by default for administrative actions.
