## Description

Access Keys authenticate application access requests to data contained in Storage Accounts. A periodic rotation of these keys is recommended to ensure that potentially compromised keys cannot result in a long-term exploitable credential. The "Rotation Reminder" is an automatic reminder feature for a manual procedure

Reminders such as those generated by this recommendation will help maintain a regular and healthy cadence for activities which improve the overall efficacy of a security program.

Cryptographic key rotation periods will vary depending on your organization's security requirements and the type of data which is being stored in the Storage Account. For example, PCI DSS mandates that cryptographic keys be replaced or rotated 'regularly,' and advises that keys for static data stores be rotated every 'few months.'

For the purposes of this recommendation, 90 days will prescribed for the reminder. Review and adjustment of the 90 day period is recommended, and may even be necessary. Your organization's security requirements should dictate the appropriate setting.

## Remediation

### From Azure Portal

1. Go to `Storage Accounts`
2. For each Storage Account that is not compliant, go to `Access keys`
3. Click `Set rotation reminder`
4. Check the `Enable key rotation reminders`

In the `Send reminders` field select `Custom`, then set the `Remind me every`: field to `90` and the period drop down menu to `Days`.

### Default Value

By default, Key rotation reminders is not configured.