## Description

Automatic Key Rotation is available in Public Preview. The currently supported applications are Key Vault, Managed Disks, and Storage accounts accessing keys within Key Vault. The number of supported applications will incrementally increased.

Once set up, Automatic Private Key Rotation removes the need for manual administration when keys expire at intervals determined by your organization's policy. The recommended key lifetime is 2 years. Your organization should determine its own key expiration policy.

## Remediation

**Note:** Azure CLI and Powershell use ISO8601 flags to input timespans. Every timespan input will be in the format P&lt;timespanInISO8601Format&gt;(Y,M,D). The leading P is required with it denoting `period`. The (Y,M,D) are for the duration of Year, Month,and Day respectively. A time frame of 2 years, 2 months, 2 days would be (P2Y2M2D).

### From Azure Portal

1. From Azure Portal select the Portal Menu in the top left.
2. Select Key Vaults.
3. Select the Key Vault you wish t oaudit.
4. Under the `Settings` heading select `Keys`.
5. Select the key you wish to audit within this vault and repeat the next steps for each.
6. In the top row select `Rotation policy`.
7. Next to `Set key rotation policy` select the link `Not Configured`.
8. Select the `Expiry Time` in for your need.
9. In this menu select `Enable` in the row `Enable auto rotation`
10. Set the `Rotation Option` to `Automatically renew`.. To automatically rotate keys, set the rotation time to or near the `Expiry Time`.
11. If desired set the `Notification duration` to a duration before the expiration.

### From Azure CLI

Run the following command for each key to update its policy to be auto-rotated:

```bash
az keyvault key rotation-policy update -n <keyName> --vault-name <vaultName> --value <path/to/policy.json>

Note: It is easiest to supply the policy flags in a .json file. An example
json file would be:

{
 "lifetimeActions": [
  {
    "trigger": {
    "timeAfterCreate": "<timespanInISO8601Format>",
    "timeBeforeExpiry" : null
    },
    "action": {
    "type": "Rotate"
    }
  },
  {
    "trigger": {
    "timeBeforeExpiry" : "<timespanInISO8601Format>"
    },
    "action": {
    "type": "Notify"
    }
  }
 ],
  "attributes": {
  "expiryTime": "<timespanInISO8601Format>"
  }
}
```

### From Powershell

Run the following command for each key to update its policy:

```bash
Set-AzKeyVaultKeyRotationPolicy -VaultName test-kv -Name test-key -PolicyPath
rotation_policy.json
```

**Note**: It is easiest to supply the policy flags in a .json file. An example json file would be:

```bash
<#
rotation_policy.json
{
 "lifetimeActions": [
  {
    "trigger": {
    "timeAfterCreate": "P<timespanInISO8601Format>M",
    "timeBeforeExpiry": null
    },
    "action": {
    "type": "Rotate"
    }
  },
  {
    "trigger": {
    "timeBeforeExpiry": "P<timespanInISO8601Format>D"
    },
    "action": {
    "type": "Notify"
    }
  }
 ],
  "attributes": {
  "expiryTime": "P<timespanInISO8601Format>Y"
  }
}
#>
```

### Default Value:

By default, Automatic Key Rotation is not enabled.
