## Description

The Azure Storage blobs contain data like ePHI or Financial, which can be secret or personal. Data that is erroneously modified or deleted by an application or other storage account user will cause data loss or unavailability.

It is recommended that both Azure Containers with attached Blob Storage and standalone containers with Blob Storage be made recoverable by enabling the soft delete configuration. This is to save and recover data when blobs or blob snapshots are deleted.

Containers and Blob Storage data can be incorrectly deleted. An attacker/malicious user may do this deliberately in order to cause disruption. Deleting an Azure Storage blob causes immediate data loss. Enabling this configuration for Azure storage ensures that even if blobs/data were deleted from the storage account, Blobs/data objects are recoverable for a particular time which is set in the "Retention policies," ranging from 7 days to 365 days.

## Remediation

### From Azure Portal

1. From the Azure home page, open the hamburger menu in the top left or click on the arrow pointing right with 'More services' underneath.
2. Select Storage.
3. Select Storage Accounts.
4. For each Storage Account, navigate to Data protection in the left scroll column.
5. Check soft delete for both blobs and containers. Set the retention period to a sufficient length for your organization.

### From Azure CLI

Update blob storage retention days in below command

```bash
az storage blob service-properties delete-policy update --days-retained
<RetentionDaysValue> --account-name <StorageAccountName> > --account-key
<AccountKey> --enable true
```

Update container retention with the below command

```bash
az storage account blob-service-properties update
--enable-container-delete-retention true
--container-delete-retention-days <days>
--account-name <storage-account>
--resource-group <resource_group>
--account-key <AccountKey>
```

### Default Value

By default, Storage Accounts will accept connections from clients on any network.When a new storage account is created, soft delete for containers and blob storage is by default disabled.