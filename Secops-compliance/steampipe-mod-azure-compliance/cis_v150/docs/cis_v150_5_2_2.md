## Description

Create an activity log alert for the Delete Policy Assignment event.

Monitoring for delete policy assignment events gives insight into changes done in "azure policy - assignments" and can reduce the time it takes to detect unsolicited changes.

## Remediation

### From Azure Portal

1. Navigate to the `Monitor` blade
2. Click on `Alerts`
3. <PERSON><PERSON> on `Create`
4. <PERSON><PERSON> on `<PERSON>ert rule`
5. Under the Scope tab, click `Select scope`
6. In the `Select a resource` window, select the appropriate filters:
   - Filter by subscription: < `choose the subscription alerts are needed` for >
   - Filter by resource location: Policy assignment (policyAssignments)
   - Filter by location: `All`
   - Click on the subscription name or resource group to apply the Log Alert Rule to
7. Verify that the selection preview shows:
   - `All Policy assignment (policyAssignments)`
   - `< Resource Name >` - The subscription, group, or resource you selected
8. <PERSON>lick `Done`
9. Under the Condition tab, click `Add Condition` (the `Select a signal` window may automatically open without clicking)
10. In the `Select a signal` window, under the "Signal Name" heading, click Delete policy assignment (Microsoft.Authorization/policyAssignments)
11. Under the Actions tab, choose appropriately:
    - Select action groups - If you have an existing action group to notify the necessary personnel.
    - Create action group - If you do not have an existing action group or want to create a new one.
12. Under the Details tab, fill in:
    - Resource group - Select the resource group you want the alert rule to reside in.
    - Alert rule name - Give your alert a recognizable and standardized name.
    - Alert rule description - (Optional)
13. Click `Review + create` then verify the summary details
14. Click `Create`

### From Azure CLI

[Azure CLI has been temporarily removed from the Activity Log Alerts section in version 1.5 and will be added back in the next release]

### Default Value

By default, no monitoring alerts are created.