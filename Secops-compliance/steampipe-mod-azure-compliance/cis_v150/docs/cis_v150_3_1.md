## Description

Enable data encryption in transit.

The secure transfer option enhances the security of a storage account by only allowing requests to the storage account by a secure connection. For example, when calling REST APIs to access storage accounts, the connection must use HTTPS. Any requests using HTTP will be rejected when 'secure transfer required' is enabled. When using the Azure files service, connection without encryption will fail, including scenarios using SMB 2.1, SMB 3.0 without encryption, and some flavors of the Linux SMB client. Because Azure storage doesn’t support HTTPS for custom domain names, this option is not applied when using a custom domain name.

## Remediation

### From Azure Portal

1. Go to `Storage Accounts`
2. For each storage account, go to `Configuration`
3. Set `Secure transfer` required to `Enabled`

### From Azure CLI

Use the below command to enable Secure transfer required for a Storage Account

```bash
az storage account update --name <storageAccountName> --resource-group
<resourceGroupName> --https-only true
```

### Default Value

By default, `Secure transfer required` is set to `Disabled`.