## Description

Enable Vulnerability Assessment (VA) service scans for critical SQL servers and corresponding SQL databases.

Enabling Microsoft Defender for SQL server does not enables Vulnerability Assessment capability for individual SQL databases unless storage account is set to store the scanning data and reports.

The Vulnerability Assessment service scans databases for known security vulnerabilities and highlights deviations from best practices, such as misconfigurations, excessive permissions, and unprotected sensitive data. Results of the scan include actionable steps to resolve each issue and provide customized remediation scripts where applicable. Additionally, an assessment report can be customized by setting an acceptable baseline for permission configurations, feature configurations, and database settings.

## Remediation

### From Azure Console

1. Go to `SQL servers`
2. Select a server instance
3. Click on `Security Center`
4. Select `Configure` next to `Enabled at subscription-level`
5. In Section `Vulnerability Assessment Settings`, <PERSON>lick `Select Storage account`
6. Choose Storage Account (Existing or `Create New`). Click `Ok`
7. Click `Save`

### From Powershell

If not already, Enable Microsoft Defender for a SQL:

```bash
Set-AZSqlServerThreatDetectionPolicy -ResourceGroupName <resource group name>
-ServerName <server name> -EmailAdmins $True
```

To enable ADS-VA service by setting Storage Account

```bash
Update-AzSqlServerVulnerabilityAssessmentSetting `
-ResourceGroupName "<resource group name>"`
-ServerName "<Server Name>"`
-StorageAccountName "<Storage Name from same subscription andsame Location" `
-ScanResultsContainerName "vulnerability-assessment" `
-RecurringScansInterval Weekly `
-EmailSubscriptionAdmins $true `
-NotificationEmail @("<EMAIL>" , "<EMAIL>")
```

### Default Value

By default, Microsoft Defender for SQL is not enabled for a SQL server. Enabling Microsoft Defender for SQL does not enable VA scanning by setting Storage Account automatically.