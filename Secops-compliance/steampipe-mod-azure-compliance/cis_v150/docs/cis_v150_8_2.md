## Description

Ensure that all Keys in Non Role Based Access Control (RBAC) Azure Key Vaults have an expiration time set.

Azure Key Vault enables users to store and use cryptographic keys within the Microsoft Azure environment. The exp (expiration time) attribute identifies the expiration time on or after which the key MUST NOT be used for a cryptographic operation. By default, keys never expire. It is thus recommended that keys be rotated in the key vault and set an explicit expiration time for all keys. This ensures that the keys cannot be used beyond their assigned lifetimes.

## Remediation

### From Azure Portal

1. Go to `Key vaults`
2. For each Key vault, click on `Keys`.
3. Under the `Settings` section, Make sure `Enabled` is set to Yes
4. Set an appropriate `EXPIRATION DATE` on all `keys`.

### From Azure CLI

Update the `EXPIRATION DATE` for the key using below command.

```bash
az keyvault key set-attributes --name <keyName> --vault-name <vaultName> -- expires Y-m-d'T'H:M:S'Z'
```

**Note:** To access expiration time on all keys in Azure Key Vault using Microsoft API, "List" Key permission is required.

To provide required access follow below steps,

### For Key Vaults using Key Vault Access Policy

1. Go to `Key vaults`
2. For each Key vault, click on `Access Policy`.
3. Add access policy with `Key permission` as `List`

### From PowerShell
```bash
Set-AzKeyVaultKeyAttribute -VaultName <Vault Name> -Name <Key Name> -Expires <DateTime>
```

### Default Value

By default, keys do not expire.