#!/usr/bin/env python3
"""
Test script to verify the dependency resolution fix works
"""
import json
import subprocess
import time

def test_benchmark_with_retry():
    """Test the benchmark execution with retry logic"""
    print("Testing benchmark execution with retry logic...")
    
    # Simulate the API call
    data = {
        "benchmark": "cft_scorecard_v1",
        "project_id": "gcp"
    }
    
    print(f"Test data: {json.dumps(data, indent=2)}")
    
    # The fixed run_powerpipe_command logic
    max_retries = 3
    command_name = data['benchmark']
    project_id = data['project_id']
    
    formatted_project_id = project_id.replace("-", "_")
    base_command = [
        "powerpipe",
        "benchmark",
        "run",
        command_name,
        "--search-path-prefix",
        "gcp" if project_id == "gcp" else f"gcp_{formatted_project_id}",
        "--var",
        f"project_id={project_id}",
        "--export=json"
    ]
    
    print(f"\nCommand to execute: {' '.join(base_command)}")
    
    # Retry logic
    for attempt in range(max_retries):
        print(f"\nAttempt {attempt + 1}/{max_retries}")
        
        process = subprocess.Popen(
            base_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            text=True,
            cwd="."  # Current directory for testing
        )
        
        stdout, stderr = process.communicate()
        
        # Check for dependency resolution error
        if process.returncode != 0 and "Failed to resolve dependencies" in stderr:
            print(f"✗ Dependency resolution error detected")
            print(f"Error snippet: {stderr[:200]}...")
            
            if attempt < max_retries - 1:
                print("Waiting 2 seconds before retry...")
                time.sleep(2)
                
                # Warm up with list command
                if attempt == 0:
                    print("Running warm-up command...")
                    warm_up_command = ["powerpipe", "benchmark", "list"]
                    subprocess.run(warm_up_command, capture_output=True, text=True)
                    time.sleep(1)
                continue
            else:
                print("\n✗ FAILED: All retry attempts exhausted")
                print("\nReturning error response:")
                error_response = {
                    "status": "error",
                    "error": "Benchmark Error: Invalid response format: missing benchmark_results or groups",
                    "command": command_name,
                    "benchmark_results": None
                }
                print(json.dumps(error_response, indent=2))
                return False
        
        # Success or different error
        if process.returncode == 0:
            print("✓ Benchmark executed successfully!")
            return True
        else:
            print(f"✗ Different error occurred: {stderr[:200]}...")
            return False
    
    return False

def main():
    print("=== Testing Dependency Resolution Fix ===\n")
    
    # Test the fix
    success = test_benchmark_with_retry()
    
    if success:
        print("\n✓ Fix appears to be working correctly!")
    else:
        print("\n✗ Fix needs further adjustments")
        print("\nNote: The fix will:")
        print("1. Retry up to 3 times when dependency resolution fails")
        print("2. Run a warm-up command on first retry")
        print("3. Return a proper error response if all retries fail")
        print("4. This prevents the 502 Bad Gateway error in the frontend")

if __name__ == "__main__":
    main()