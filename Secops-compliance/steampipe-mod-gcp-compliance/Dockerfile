# Use the base image for Steampipe Powerpipe
FROM us-central1-docker.pkg.dev/vratant-test-prj/steampipe/steampipe-powerpipe:latest

# Switch to root user for installing dependencies
USER root

ARG DOMAIN
ARG PROJECT_ID

# Install Python, pip, and PostgreSQL dependencies
RUN apt-get update && \
    apt-get install -y python3-pip \
                       libpq-dev \
                       curl \
                       unzip && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set environment variables for Steampipe
ENV STEAMPIPE_HOME=/app/.steampipe
ENV PATH=$STEAMPIPE_HOME:$PATH

# Create necessary directories and set correct ownership
RUN mkdir -p /app && \
    chown -R steampipe:steampipe /app && \
    mkdir -p $STEAMPIPE_HOME && \
    chown -R steampipe:steampipe $STEAMPIPE_HOME && \
    chmod -R 755 $STEAMPIPE_HOME

# Copy requirements.txt and install dependencies as root
COPY requirements.txt /tmp/
RUN pip3 install --no-cache-dir -r /tmp/requirements.txt

# Switch to steampipe user and set working directory
USER steampipe
WORKDIR /app

# Initialize powerpipe - Ensure it runs with the correct permissions
RUN powerpipe mod init || echo "Powerpipe already initialized, continuing..."

# Don't copy static config - let the app generate it dynamically at runtime
# COPY --chown=steampipe:steampipe steampipe-config/gcp.spc /home/<USER>/.steampipe/config/gcp.spc

# Copy the entire repository, including app.py
COPY --chown=steampipe:steampipe ./ /app/steampipe-mod-gcp-compliance

# Install the module from local path
RUN powerpipe mod install /app/steampipe-mod-gcp-compliance || echo "Module already installed, continuing..."

# Ensure Steampipe extensions are handled properly
RUN steampipe plugin install steampipe-postgres-fdw || echo "Extension already installed, continuing..."

# Create a startup script to launch the Flask application
# Create a startup script to launch the Flask application and ensure Steampipe runs reliably
RUN echo '#!/bin/bash\n\
\n\
# Start Flask application on port 8080\n\
cd /app/steampipe-mod-gcp-compliance\n\
exec python3 app.py\n' > /home/<USER>/start.sh && \
    chmod +x /home/<USER>/start.sh

# Expose port 8080 for Cloud Run
EXPOSE 8080

# Set entrypoint
ENTRYPOINT ["/home/<USER>/start.sh"]