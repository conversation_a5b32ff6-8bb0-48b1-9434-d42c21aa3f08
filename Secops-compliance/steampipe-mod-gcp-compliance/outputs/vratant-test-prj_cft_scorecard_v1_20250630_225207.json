{"project_id": "vratant-test-prj", "benchmark": "cft_scorecard_v1", "timestamp": "20250630_225207", "results": {"command_output": "\nCFT… 51 / 172 [==========]\n| \n+ V…  0 /   0 [          ]\n| \n+ P…  0 /   1 [=         ]\n| | \n| OK   : No public … vrat…\n| \n+ E…  0 /   0 [          ]\n| \n+ E…  0 /   0 [          ]\n| \n+ E…  0 /   0 [          ]\n| \n+ C…  0 /   0 [          ]\n| \n+ E…  0 /   1 [=         ]\n| | \n| SKIP : private-go… vrat…\n| \n+ E…  0 /   1 [=         ]\n| | \n| SKIP : private-go… vrat…\n| \n+ E…  0 /   0 [          ]\n| \n+ E…  0 /   0 [          ]\n| \n+ E…  0 /   0 [          ]\n| \n+ E…  0 /   0 [          ]\n| \n+ E…  1 /  42 [===       ]\n| | \n| ALARM: test flow loggi… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| OK   : default flow lo… \n| \n+ E… 40 /  42 [====      ]\n| | \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: test private Go… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| ALARM: default private… \n| OK   : default private… \n| OK   : default private… \n| \n+ E…  0 /   0 [          ]\n| \n+ C…  0 /   0 [          ]\n| \n+ P…  0 /   0 [          ]\n| \n+ C…  0 /  14 [=         ]\n| | \n| OK   : open_data_batch… \n| OK   : open_data_test_… \n| OK   : ocr_results not… \n| OK   : open_data_fixed… \n| OK   : bigquery not an… \n| OK   : open_data_canad… \n| OK   : open_data_test_… \n| OK   : open_data_test_… \n| OK   : open_data_test_… \n| OK   : open_data_test_… \n| OK   : flow_logs_datas… \n| OK   : open_data_canad… \n| OK   : open_data_test_… \n| OK   : vpc_flow_logs_d… \n| \n+ C…  8 /  12 [==        ]\n| | \n| ALARM: 816268782019_us… \n| ALARM: ocr-output-vrat… \n| ALARM: vratant-bucket … \n| ALARM: reddit-k8s-stuf… \n| ALARM: vratant-test-pr… \n| ALARM: vratant-test-pr… \n| ALARM: vratant-test-pr… \n| ALARM: ocr-input-vrata… \n| OK   : 816268782019-no… \n| OK   : www.neosecai.co… \n| OK   : build-logs-vrat… \n| OK   : vratant-test-pr… \n| \n+ C…  0 /   0 [          ]\n| \n+ C…  1 /  15 [==        ]\n| | \n| ALARM: default-allow-r… \n| OK   : default-allow-h… \n| OK   : allow-tcp-5000 … \n| OK   : allow-googleapi… \n| OK   : wasp-port-open-… \n| OK   : default-allow-h… \n| OK   : allow-redis-tra… \n| OK   : gke-csm-thc-9b9… \n| OK   : default-allow-i… \n| OK   : default-allow-h… \n| OK   : default-allow-s… \n| OK   : default-allow-h… \n| OK   : ingress restric… \n| OK   : allow-google-ap… \n| OK   : default-allow-i… \n| \n+ C…  1 /  15 [==        ]\n| | \n| ALARM: default-allow-s… \n| OK   : default-allow-r… \n| OK   : default-allow-h… \n| OK   : allow-tcp-5000 … \n| OK   : allow-googleapi… \n| OK   : wasp-port-open-… \n| OK   : default-allow-h… \n| OK   : allow-redis-tra… \n| OK   : gke-csm-thc-9b9… \n| OK   : default-allow-i… \n| OK   : default-allow-h… \n| OK   : default-allow-h… \n| OK   : ingress restric… \n| OK   : allow-google-ap… \n| OK   : default-allow-i… \n| \n+ E…  0 /  14 [=         ]\n| | \n| OK   : open_data_batch… \n| OK   : open_data_test_… \n| OK   : ocr_results enf… \n| OK   : open_data_fixed… \n| OK   : bigquery enforc… \n| OK   : open_data_canad… \n| OK   : open_data_test_… \n| OK   : open_data_test_… \n| OK   : open_data_test_… \n| OK   : open_data_test_… \n| OK   : flow_logs_datas… \n| OK   : open_data_canad… \n| OK   : open_data_test_… \n| OK   : vpc_flow_logs_d… \n| \n+ E…  0 /  14 [=         ]\n| | \n| OK   : open_data_batch… \n| OK   : open_data_test_… \n| OK   : ocr_results enf… \n| OK   : open_data_fixed… \n| OK   : bigquery enforc… \n| OK   : open_data_canad… \n| OK   : open_data_test_… \n| OK   : open_data_test_… \n| OK   : open_data_test_… \n| OK   : open_data_test_… \n| OK   : flow_logs_datas… \n| OK   : open_data_canad… \n| OK   : open_data_test_… \n| OK   : vpc_flow_logs_d… \n| \n+ L…  0 /   1 [=         ]\n| | \n| INFO : Manual ver… vrat…\n| \n+ C…  0 /   0 [          ]\n  \nSummary\n\nOK ....... 118 [=======   ]\nSKIP ....... 2 [=         ]\nINFO ....... 1 [=         ]\nALARM ..... 51 [===       ]\nERROR ...... 0 [          ]\n\nTOTAL 51 / 172 [==========]\n\nFile exported to /app/steampipe-mod-gcp-compliance/gcp_compliance.benchmark.cft_scorecard_v1.20250630T225207.json\n", "command_error": null, "benchmark_results": {"group_id": "root_result_group", "title": "CFT Scorecard v1", "description": "", "tags": {}, "summary": {"status": {"alarm": 51, "ok": 118, "info": 1, "skip": 2, "error": 0}}, "groups": [{"group_id": "gcp_compliance.benchmark.cft_scorecard_v1", "title": "CFT Scorecard v1", "description": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 51, "ok": 118, "info": 1, "skip": 2, "error": 0}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.allow_only_private_cluster", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Verify all GKE clusters are Private Clusters", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 1, "info": 0, "skip": 0, "error": 0}, "results": [{"reason": "No public users have access to resources via IAM.", "resource": "vratant-test-prj", "status": "ok", "dimensions": [{"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.denylist_public_users", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "plugin": "gcp", "service": "GCP/IAM", "severity": "high"}, "title": "Prevent public users from having access to resources via IAM", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.disable_gke_dashboard", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Kubernetes web UI/Dashboard is disabled", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.disable_gke_default_service_account", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure default Service account is not used for Project access in Kubernetes Engine clusters", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.disable_gke_legacy_abac", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Legacy Authorization is set to Disabled on Kubernetes Engine Clusters", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.disable_gke_legacy_endpoints", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Check that legacy metadata endpoints are disabled on Kubernetes clusters(disabled by default since GKE 1.12+)", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 1, "error": 0}, "results": [{"reason": "private-googleapis-zone is private.", "resource": "https://www.googleapis.com/dns/v1/projects/vratant-test-prj/managedZones/private-googleapis-zone", "status": "skip", "dimensions": [{"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.dnssec_prevent_rsasha1_ksk", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/DNS", "severity": "high", "soc_2_2017": "true"}, "title": "Ensure that RSASHA1 is not used for key-signing key in Cloud DNS", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 1, "error": 0}, "results": [{"reason": "private-googleapis-zone is private.", "resource": "https://www.googleapis.com/dns/v1/projects/vratant-test-prj/managedZones/private-googleapis-zone", "status": "skip", "dimensions": [{"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.dnssec_prevent_rsasha1_zsk", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/DNS", "severity": "high"}, "title": "Ensure that RSASHA1 is not used for zone-signing key in Cloud DNS", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.enable_alias_ip_ranges", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Kubernetes Cluster is created with Alias IP ranges enabled", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.enable_auto_repair", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure automatic node repair is enabled on all node pools in a GKE cluster", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.enable_auto_upgrade", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Automatic node upgrades is enabled on Kubernetes Engine Clusters nodes", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.enable_gke_master_authorized_networks", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Master authorized networks is set to Enabled on Kubernetes Engine Clusters", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 1, "ok": 41, "info": 0, "skip": 0, "error": 0}, "results": [{"reason": "test flow logging disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-central1/subnetworks/test", "status": "alarm", "dimensions": [{"key": "location", "value": "us-central1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-south1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "asia-south1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-southwest1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-southwest1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-south1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "us-south1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-east1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "us-east1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-west2/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "us-west2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-east5/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "us-east5"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/me-west1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "me-west1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-east1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "asia-east1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west8/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-west8"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-northeast2/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "asia-northeast2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west12/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-west12"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-southeast1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "asia-southeast1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west6/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-west6"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/me-central1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "me-central1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west4/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-west4"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-north1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-north1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/southamerica-east1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "southamerica-east1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-east2/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "asia-east2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-east4/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "us-east4"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-south2/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "asia-south2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west3/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-west3"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west2/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-west2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-north2/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-north2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-west1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/southamerica-west1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "southamerica-west1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/australia-southeast2/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "australia-southeast2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-central2/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-central2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/northamerica-northeast2/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "northamerica-northeast2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-central1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "us-central1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-northeast1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "asia-northeast1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-northeast3/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "asia-northeast3"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-west1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "us-west1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/northamerica-south1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "northamerica-south1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-southeast2/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "asia-southeast2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-west3/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "us-west3"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/australia-southeast1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "australia-southeast1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/northamerica-northeast1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "northamerica-northeast1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-west4/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "us-west4"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/africa-south1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "africa-south1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west9/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-west9"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default flow logging enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west10/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "europe-west10"}, {"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.enable_network_flow_logs", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high", "soc_2_2017": "true"}, "title": "Ensure VPC Flow logs is enabled for every subnet in VPC Network", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 40, "ok": 2, "info": 0, "skip": 0, "error": 0}, "results": [{"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-south1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "asia-south1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-southwest1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-southwest1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-south1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "us-south1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-east1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "us-east1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-west2/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "us-west2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-east5/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "us-east5"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/me-west1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "me-west1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-east1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "asia-east1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west8/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-west8"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-northeast2/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "asia-northeast2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west12/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-west12"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-southeast1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "asia-southeast1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west6/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-west6"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/me-central1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "me-central1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west4/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-west4"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-north1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-north1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/southamerica-east1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "southamerica-east1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-east2/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "asia-east2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-east4/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "us-east4"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-south2/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "asia-south2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west3/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-west3"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west2/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-west2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-north2/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-north2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-west1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/southamerica-west1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "southamerica-west1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/australia-southeast2/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "australia-southeast2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-central2/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-central2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/northamerica-northeast2/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "northamerica-northeast2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "test private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-central1/subnetworks/test", "status": "alarm", "dimensions": [{"key": "location", "value": "us-central1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-northeast1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "asia-northeast1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-northeast3/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "asia-northeast3"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-west1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "us-west1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/northamerica-south1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "northamerica-south1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/asia-southeast2/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "asia-southeast2"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-west3/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "us-west3"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/australia-southeast1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "australia-southeast1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-west4/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "us-west4"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/africa-south1/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "africa-south1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west9/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-west9"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is disabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/europe-west10/subnetworks/default", "status": "alarm", "dimensions": [{"key": "location", "value": "europe-west10"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/us-central1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "us-central1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default private Google Access is enabled.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/regions/northamerica-northeast1/subnetworks/default", "status": "ok", "dimensions": [{"key": "location", "value": "northamerica-northeast1"}, {"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.enable_network_private_google_access", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high"}, "title": "Ensure Private Google Access is enabled for all subnetworks in VPC", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.gke_container_optimized_os", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Container-Optimized OS (cos) is used for Kubernetes engine clusters", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.gke_restrict_pod_traffic", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Check that GKE clusters have a Network Policy installed", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.prevent_public_ip_cloudsql", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "severity": "high"}, "title": "Prevent a public IP from being assigned to a Cloud SQL instance", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 14, "info": 0, "skip": 0, "error": 0}, "results": [{"reason": "open_data_batch_100 not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/open_data_batch_100", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_100_final not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/open_data_test_100_final", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "ocr_results not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/ocr_results", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_fixed_100 not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/open_data_fixed_100", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "bigquery not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/bigquery", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_canada not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/open_data_canada", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_small not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/open_data_test_small", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_100 not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/open_data_test_100", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_working not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/open_data_test_working", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_pdf not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/open_data_test_pdf", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "flow_logs_dataset not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/flow_logs_dataset", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_canada_test not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/open_data_canada_test", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_100_quick not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/open_data_test_100_quick", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "vpc_flow_logs_dataset not anonymously or publicly accessible.", "resource": "https://bigquery.googleapis.com/bigquery/v2/projects/vratant-test-prj/datasets/vpc_flow_logs_dataset", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.require_bq_table_iam", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/BigQuery", "severity": "high", "soc_2_2017": "true"}, "title": "Check if BigQuery datasets are publicly readable", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 8, "ok": 4, "info": 0, "skip": 0, "error": 0}, "results": [{"reason": "816268782019_us_import_content_with_faq_csv bucket only policy turned off", "resource": "https://www.googleapis.com/storage/v1/b/816268782019_us_import_content_with_faq_csv", "status": "alarm", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "ocr-output-vratant-test-prj-1749158078 bucket only policy turned off", "resource": "https://www.googleapis.com/storage/v1/b/ocr-output-vratant-test-prj-1749158078", "status": "alarm", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "vratant-bucket bucket only policy turned off", "resource": "https://www.googleapis.com/storage/v1/b/vratant-bucket", "status": "alarm", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "reddit-k8s-stuff bucket only policy turned off", "resource": "https://www.googleapis.com/storage/v1/b/reddit-k8s-stuff", "status": "alarm", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "vratant-test-prj-open-data bucket only policy turned off", "resource": "https://www.googleapis.com/storage/v1/b/vratant-test-prj-open-data", "status": "alarm", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "vratant-test-prj-fabric-test-478998 bucket only policy turned off", "resource": "https://www.googleapis.com/storage/v1/b/vratant-test-prj-fabric-test-478998", "status": "alarm", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "vratant-test-prj-terraform-plans bucket only policy turned off", "resource": "https://www.googleapis.com/storage/v1/b/vratant-test-prj-terraform-plans", "status": "alarm", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "ocr-input-vratant-test-prj-1749158078 bucket only policy turned off", "resource": "https://www.googleapis.com/storage/v1/b/ocr-input-vratant-test-prj-1749158078", "status": "alarm", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "816268782019-northamerica-northeast1-blueprint-config bucket only policy turned on.", "resource": "https://www.googleapis.com/storage/v1/b/816268782019-northamerica-northeast1-blueprint-config", "status": "ok", "dimensions": [{"key": "location", "value": "NORTHAMERICA-NORTHEAST1"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "www.neosecai.com bucket only policy turned on.", "resource": "https://www.googleapis.com/storage/v1/b/www.neosecai.com", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "build-logs-vratant-test-prj bucket only policy turned on.", "resource": "https://www.googleapis.com/storage/v1/b/build-logs-vratant-test-prj", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "vratant-test-prj_cloudbuild bucket only policy turned on.", "resource": "https://www.googleapis.com/storage/v1/b/vratant-test-prj_cloudbuild", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.require_bucket_policy_only", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/Storage", "severity": "high", "soc_2_2017": "true"}, "title": "Check if Cloud Storage buckets have Bucket Only Policy turned on", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.require_ssl_sql", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/SQL", "severity": "high"}, "title": "Check if Cloud SQL instances have SSL turned on", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 1, "ok": 14, "info": 0, "skip": 0, "error": 0}, "results": [{"reason": "default-allow-rdp allows RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-rdp", "status": "alarm", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-health-check-ipv6 restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-health-check-ipv6", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "allow-tcp-5000 restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/allow-tcp-5000", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "allow-googleapis-egress restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/allow-googleapis-egress", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "wasp-port-open-debugging restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/wasp-port-open-debugging", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-health-check restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-health-check", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "allow-redis-traffic restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/allow-redis-traffic", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "gke-csm-thc-9b94f025 restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/gke-csm-thc-9b94f025", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-icmp restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-icmp", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-https restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-https", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-ssh restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-ssh", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-http restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-http", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "ingress restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/ingress", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "allow-google-apis-egress restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/allow-google-apis-egress", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-internal restricts RDP access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-internal", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.restrict_firewall_rule_rdp_world_open", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_800_53_rev_5": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high", "soc_2_2017": "true"}, "title": "Check for open firewall rules allowing RDP from the internet", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 1, "ok": 14, "info": 0, "skip": 0, "error": 0}, "results": [{"reason": "default-allow-ssh allows SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-ssh", "status": "alarm", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-rdp restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-rdp", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-health-check-ipv6 restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-health-check-ipv6", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "allow-tcp-5000 restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/allow-tcp-5000", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "allow-googleapis-egress restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/allow-googleapis-egress", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "wasp-port-open-debugging restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/wasp-port-open-debugging", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-health-check restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-health-check", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "allow-redis-traffic restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/allow-redis-traffic", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "gke-csm-thc-9b94f025 restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/gke-csm-thc-9b94f025", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-icmp restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-icmp", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-https restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-https", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-http restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-http", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "ingress restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/ingress", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "allow-google-apis-egress restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/allow-google-apis-egress", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "default-allow-internal restricts SSH access from internet.", "resource": "https://www.googleapis.com/compute/v1/projects/vratant-test-prj/global/firewalls/default-allow-internal", "status": "ok", "dimensions": [{"key": "location", "value": "global"}, {"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.restrict_firewall_rule_ssh_world_open", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "nist_800_53_rev_5": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high", "soc_2_2017": "true"}, "title": "Check for open firewall rules allowing SSH from the internet", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 14, "info": 0, "skip": 0, "error": 0}, "results": [{"reason": "open_data_batch_100 enforces corporate domain by banning gmail.com addresses access.", "resource": "open_data_batch_100", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_100_final enforces corporate domain by banning gmail.com addresses access.", "resource": "open_data_test_100_final", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "ocr_results enforces corporate domain by banning gmail.com addresses access.", "resource": "ocr_results", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_fixed_100 enforces corporate domain by banning gmail.com addresses access.", "resource": "open_data_fixed_100", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "bigquery enforces corporate domain by banning gmail.com addresses access.", "resource": "big<PERSON>y", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_canada enforces corporate domain by banning gmail.com addresses access.", "resource": "open_data_canada", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_small enforces corporate domain by banning gmail.com addresses access.", "resource": "open_data_test_small", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_100 enforces corporate domain by banning gmail.com addresses access.", "resource": "open_data_test_100", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_working enforces corporate domain by banning gmail.com addresses access.", "resource": "open_data_test_working", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_pdf enforces corporate domain by banning gmail.com addresses access.", "resource": "open_data_test_pdf", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "flow_logs_dataset enforces corporate domain by banning gmail.com addresses access.", "resource": "flow_logs_dataset", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_canada_test enforces corporate domain by banning gmail.com addresses access.", "resource": "open_data_canada_test", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_100_quick enforces corporate domain by banning gmail.com addresses access.", "resource": "open_data_test_100_quick", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "vpc_flow_logs_dataset enforces corporate domain by banning gmail.com addresses access.", "resource": "vpc_flow_logs_dataset", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.restrict_gmail_bigquery_dataset", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "plugin": "gcp", "service": "GCP/BigQuery", "severity": "high"}, "title": "Enforce corporate domain by banning gmail.com addresses access to BigQuery datasets", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 14, "info": 0, "skip": 0, "error": 0}, "results": [{"reason": "open_data_batch_100 enforces corporate domain by banning googlegroups.com addresses access.", "resource": "open_data_batch_100", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_100_final enforces corporate domain by banning googlegroups.com addresses access.", "resource": "open_data_test_100_final", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "ocr_results enforces corporate domain by banning googlegroups.com addresses access.", "resource": "ocr_results", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_fixed_100 enforces corporate domain by banning googlegroups.com addresses access.", "resource": "open_data_fixed_100", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "bigquery enforces corporate domain by banning googlegroups.com addresses access.", "resource": "big<PERSON>y", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_canada enforces corporate domain by banning googlegroups.com addresses access.", "resource": "open_data_canada", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_small enforces corporate domain by banning googlegroups.com addresses access.", "resource": "open_data_test_small", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_100 enforces corporate domain by banning googlegroups.com addresses access.", "resource": "open_data_test_100", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_working enforces corporate domain by banning googlegroups.com addresses access.", "resource": "open_data_test_working", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_pdf enforces corporate domain by banning googlegroups.com addresses access.", "resource": "open_data_test_pdf", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "flow_logs_dataset enforces corporate domain by banning googlegroups.com addresses access.", "resource": "flow_logs_dataset", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_canada_test enforces corporate domain by banning googlegroups.com addresses access.", "resource": "open_data_canada_test", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "open_data_test_100_quick enforces corporate domain by banning googlegroups.com addresses access.", "resource": "open_data_test_100_quick", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}, {"reason": "vpc_flow_logs_dataset enforces corporate domain by banning googlegroups.com addresses access.", "resource": "vpc_flow_logs_dataset", "status": "ok", "dimensions": [{"key": "location", "value": "US"}, {"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.restrict_googlegroups_bigquery_dataset", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "plugin": "gcp", "service": "GCP/BigQuery", "severity": "high"}, "title": "Enforce corporate domain by banning googlegroups.com addresses access to BigQuery datasets", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 1, "skip": 0, "error": 0}, "results": [{"reason": "Manual verification required.", "resource": "https://cloudresourcemanager.googleapis.com/v1/projects/vratant-test-prj", "status": "info", "dimensions": [{"key": "project", "value": "vratant-test-prj"}]}], "control_id": "control.service_versions", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/AppEngine", "severity": "high"}, "title": "Limit the number of App Engine application versions simultaneously running or installed", "run_status": 4, "run_error": ""}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 0}, "results": null, "control_id": "control.sql_world_readable", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "nist_800_53_rev_5": "true", "plugin": "gcp", "service": "GCP/SQL", "severity": "high", "soc_2_2017": "true"}, "title": "Check if Cloud SQL instances are world readable", "run_status": 4, "run_error": ""}]}], "controls": null}}}