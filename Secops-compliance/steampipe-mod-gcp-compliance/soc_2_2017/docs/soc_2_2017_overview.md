## Overview

SOC 2, aka Service Organization Control Type 2, is a cybersecurity compliance framework developed by the American Institute of Certified Public Accountants (AICPA). The primary purpose of SOC 2 is to ensure that third-party service providers store and process client data in a secure manner.

The framework specifies criteria to uphold high standards of data security, based on five trust service principles: security, privacy, availability, confidentiality, and processing integrity.

Unlike other compliance frameworks, which have a predefined set of conditions for all companies, SOC 2 requirements are different for every organization. Depending on their own operating models, each organization must formulate its own security controls to become compliant with the five trust principles.

### Security

Broadly speaking, the security principle enforces the protection of data and systems, against unauthorized access. To that end, you may need to implement some form of access control, e.g. using access control lists or identity management systems.

You may also have to strengthen your firewalls, by introducing stricter outbound and incoming rules, introduce intrusion detection and recovery systems, and enforce multi-factor authentication.

### Confidentiality

Data qualifies as confidential if only a specific group of people should access it. This may include application source code, usernames and passwords, credit card information, or business plans, etc.

To adhere to this principle, confidential data must be encrypted, both at rest and during transit. Moreover, while providing access to confidential data, adhere to the principle of leastprivilege, i.e. grant the bare-minimum permissions/rights that people need to do their jobs.

### Availability

Systems should meet availability SLAs at all times. This requires building inherently fault-tolerant systems, which do not crumble under high load. It also requires organizations to invest in network monitoring systems and have disaster recovery plans in place.

### Privacy

The collection, storage, processing, and disclosure of any personally identifiable information (PII) must adhere to the organization’s data usage and privacy policy, along with the conditions defined by the AICPA, in the Generally Accepted Privacy Principles (GAPP).

PII is any information that can be used to uniquely identify an individual, e.g. name, age, phone number, credit card information, or social security number etc. An organization must enforce rigorous controls to protect PII from unauthorized access.

### Processing integrity

All systems must always function as per design, devoid of any delays, vulnerabilities, errors, or bugs. Quality assurance and performance monitoring applications and procedures are crucial to achieve adherence to this principle.
