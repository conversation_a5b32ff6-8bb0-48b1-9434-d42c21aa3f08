#!/usr/bin/env python3
"""
Test script to reproduce the benchmark dependency resolution error
"""
import subprocess
import json
import os

def test_benchmark_directly():
    """Test running the benchmark directly without the Flask app"""
    
    # Set environment variables
    os.environ['STEAMPIPE_DATABASE_PASSWORD'] = ''
    
    print("Testing benchmark execution...")
    
    # The command that the app.py would run
    command = [
        "powerpipe",
        "benchmark",
        "run",
        "cft_scorecard_v1",
        "--search-path-prefix",
        "gcp",
        "--var",
        "project_id=gcp",
        "--export=json"
    ]
    
    print(f"Running command: {' '.join(command)}")
    print(f"Working directory: {os.getcwd()}")
    
    try:
        # Run the command
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            text=True,
            cwd=os.getcwd()  # Current directory
        )
        
        stdout, stderr = process.communicate()
        
        print("\n=== STDOUT ===")
        print(stdout[:1000] if stdout else "No stdout")
        
        print("\n=== STDERR ===")
        print(stderr[:1000] if stderr else "No stderr")
        
        print(f"\n=== Return code: {process.returncode} ===")
        
        # Check if it's the dependency resolution error
        if "Failed to resolve dependencies" in stderr:
            print("\n!!! FOUND THE DEPENDENCY RESOLUTION ERROR !!!")
            print("\nExtracting dependency issues...")
            
            # Extract the unresolved blocks
            lines = stderr.split('\n')
            in_unresolved = False
            unresolved_items = []
            
            for line in lines:
                if "Unresolved blocks:" in line:
                    in_unresolved = True
                elif in_unresolved and line.strip():
                    if line.strip().startswith('benchmark.') or line.strip().startswith('control.') or line.strip().startswith('query.') or line.strip().startswith('local.'):
                        unresolved_items.append(line.strip())
                elif in_unresolved and not line.strip():
                    break
            
            print(f"\nFound {len(unresolved_items)} unresolved items")
            print("\nFirst 20 unresolved items:")
            for item in unresolved_items[:20]:
                print(f"  - {item}")
        
    except Exception as e:
        print(f"Error running command: {e}")

if __name__ == "__main__":
    test_benchmark_directly()