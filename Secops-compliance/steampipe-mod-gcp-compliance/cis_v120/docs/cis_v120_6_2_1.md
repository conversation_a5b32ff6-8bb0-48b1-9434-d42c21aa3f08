## Description

Ensure that the *log_checkpoints* database flag for the Cloud SQL PostgreSQL instance is set to on. Default value for *log_checkpoints* flag is off.

Enabling *log_checkpoints* causes checkpoints and restart points to be logged in the server log. Some statistics are included in the log messages, including the number of buffers written and the time spent writing them. This parameter can only be set in the postgresql.conf file or on the server command line. This recommendation is applicable to PostgreSQL database instances.

## Remediation

### From Console:

1. <PERSON>gin to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance for which the database flag needs to be enabled.
3. Click **EDIT**.
4. Scroll down to the *Flags* section.
5. Click **ADD FLAG**, choose the `flag log_checkpoints` from the drop-down menu, and set its to `on`.
6. Click **SAVE**.
7. Confirm the changes under Flags on the Overview page.

### From Command Line:

1. List all Cloud SQL database instances using the following command

```bash
gcloud sql instances list
```
2. Configure the log_checkpoints database flag for every Cloud SQL PosgreSQL database instance using the below command

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags log_checkpoints=on
```

**Note**
-
- This command will overwrite all previously set database flags. To keep those and add new ones, include the values for all flags to be set on the instance. Any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
- Configuring the above flag **restarts** the Cloud PostgreSQL instance.