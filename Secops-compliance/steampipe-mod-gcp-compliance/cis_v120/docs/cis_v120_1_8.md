## Description

It is recommended that the principle of 'Separation of Duties' is enforced while assigning
service-account related roles to users

The built-in/predefined IAM role `Service Account admin` allows the user/identity to create, delete, and manage service account(s). The built-in/predefined IAM role `Service Account User` allows the user/identity (with adequate privileges on Compute and App Engine) to assign service account(s) to Apps/Compute Instances.

Separation of duties is the concept of ensuring that one individual does not have all necessary permissions to be able to complete a malicious action. In Cloud IAM - service accounts, this could be an action such as using a service account to access resources that user should not normally have access to.

## Remediation

No user should have `Service Account Admin` and `Service Account User` roles assigned at the same time. Removal of a role should be done based on the business requirements.

### From Console

1. Login to GCP [Admin/IAM](https://console.cloud.google.com/iam-admin/iam).
2. For any member having both `Service Account Admin` and `Service account User`roles granted/assigned as shown in the `Role` column, remove either role from the member.
