## Description

The *log_min_error_statement* flag defines the minimum message severity level that are considered as an error statement. Messages for error statements are logged with the SQL statement. Valid values include *DEBUG5*, *DEBUG4*, *DEBUG3*, *DEBUG2*, *DEBUG1*, *INFO*, *NOTICE*, *WARNING*, *ERROR*, *LOG*, *FATAL*, and *PANIC*. Each severity level includes the subsequent levels mentioned above. Ensure a value of *ERROR* or stricter is set.

Default value for *log_min_error_statement* flag is *ERROR*.

Auditing helps in troubleshooting operational problems and also permits forensic analysis. If *log_min_error_statement* is not set to the correct value, messages may not be classified as error messages appropriately. Considering general log messages as error messages would make is difficult to find actual errors and considering only stricter severity levels as error messages may skip actual errors to log their SQL statements.

The *log_min_error_statement* flag should be set to *ERROR* or stricter.

## Remediation

### From Console:

1. Login to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance for which the database flag needs to be enabled.
3. Click **EDIT**.
4. Scroll down to the *Flags* section.
5. Click **ADD FLAG**, choose the `log_min_error_statement` from the drop-down menu, and set to `off`.
6. Click **SAVE**.
7. Confirm the changes under Flags on the Overview page.

### From Command Line:

1. List all Cloud SQL database instances using the following command

```bash
gcloud sql instances list
```
2. Configure the *log_min_error_statement* database flag for every Cloud SQL PostgreSQL database instance using the below command

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags log_min_error_statement=<DEBUG5|DEBUG4|DEBUG3|DEBUG2|DEBUG1|INFO|NOTICE|WARNI NG|ERROR>
```

**Note**
- This command will overwrite all previously set database flags. To keep those and add new ones, include the values for all flags to be set on the instance. Any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
- Configuring the above flag does not require **restarting** the Cloud PostgreSQL instance.