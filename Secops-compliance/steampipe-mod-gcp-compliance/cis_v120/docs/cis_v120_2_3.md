## Description

Enabling retention policies on log buckets will protect logs stored in cloud storage buckets from being overwritten or accidentally deleted. It is recommended to set up retention policies and configure Bucket Lock on all storage buckets that are used as log sinks.

Storage buckets used as log sinks do not have retention policies and bucket lock configured by default.

Logs can be exported by creating one or more sinks that include a log filter and a destination. As cloud logging receives new log entries, they are compared against each sink. If a log entry matches a sink's filter, then a copy of the log entry is written to the destination.

Sinks can be configured to export logs in storage buckets. It is recommended to configure a data retention policy for these cloud storage buckets and to lock the data retention policy; thus permanently preventing the policy from being removed.

Locking a bucket is an irreversible action. Once you lock a bucket, you cannot remove the retention policy from the bucket or decrease the retention period for the policy.

## Remediation

### From Console

1. Ensure that sinks are configured for all Log entries.
2. For each storage bucket configured as a sink, go to the [Cloud Storage browser](https://console.cloud.google.com/storage/browser/).
3. Select the Bucket name.
4. Go to **RETENTION** tab.
5. Enter the desired length of time for the `retention period` and click **Save**.
6. Set the `Lock status` for this retention policy to **Locked**.

### From Command Line

1. To list all sinks destined to storage buckets:

```bash
gcloud logging sinks list --folder=FOLDER_ID | --organization=ORGANIZATION_ID | --project=PROJECT_ID
```

2. For each storage bucket listed above, set a retention policy and lock it:

```bash
gsutil retention set [TIME_DURATION] gs://[BUCKET_NAME]
gsutil retention lock gs://[BUCKET_NAME]
```


