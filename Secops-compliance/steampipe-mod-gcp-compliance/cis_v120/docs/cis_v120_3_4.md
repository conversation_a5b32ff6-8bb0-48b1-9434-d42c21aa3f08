## Description

DNSSEC algorithm numbers in this registry may be used in CERT RRs. Zone signing (DNSSEC) and transaction security mechanisms (SIG(0) and TSIG) make use of particular subsets of these algorithms. The algorithm used for `key signing` should be a recommended one and it should be strong.

The algorithm used for key signing should be a recommended one and it should be strong. When enabling `DNSSEC` for a managed zone, or creating a managed zone with DNSSEC, the user can select the DNSSEC signing algorithms and the denial-of-existence type. Changing the DNSSEC settings is only effective for a managed zone if DNSSEC is not already enabled. If there is a need to change the settings for a managed zone where it has been enabled, turn
DNSSEC off and then re-enable it with different settings.

## Remediation

### From Command Line

1. Ensure the property algorithm for keyType `keySigning` is not using RSASHA1.

```bash
gcloud dns managed-zones describe ZONENAME --format="json(dnsName,dnssecConfig.state,dnssecConfig.defaultKeySpecs)"
```

2. If it is necessary to change the settings for a managed zone where it has been enabled, `NSSEC` must be turned off and re-enabled with different settings. To turn off DNSSEC, run the following command:

```bash
gcloud dns managed-zones update ZONE_NAME --dnssec-state off
```

3. To update key-signing for a reported managed DNS Zone, run the following command:

```bash
gcloud dns managed-zones update ZONE_NAME \
--dnssec-state on \
--ksk-algorithm KSK_ALGORITHM \
--ksk-key-length KSK_KEY_LENGTH \
--zsk-algorithm ZSK_ALGORITHM \
--zsk-key-length ZSK_KEY_LENGTH \
--denial-of-existence DENIAL_OF_EXISTENCE
```
For example the following command enables DNSSEC with 256-bit ECDSA and NSEC for the smallest DNSSEC-signed response packets possible using Cloud DNS:

```bash
gcloud dns managed-zones update EXAMPLE_ZONE \
  --dnssec-state on \
  --ksk-algorithm ECDSAP256SHA256 \
  --ksk-key-length 256 \
  --zsk-algorithm ECDSAP256SHA256 \
  --zsk-key-length 256 \
  --denial-of-existence NSEC
```
**Note** Supported algorithm options and key lengths are as follows.

```
Algorithm         KSK Length    ZSK Length
---------         ----------    ----------
RSASHA1           1024,2048     1024,2048
RSASHA256         1024,2048     1024,2048
RSASHA512         1024,2048     1024,2048
ECDSAP256SHA256   256           256
ECDSAP384SHA384   384           384
```
