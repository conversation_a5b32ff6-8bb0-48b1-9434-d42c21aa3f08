## Description

It is recommended that a metric filter and alarm be established for *Virtual Private Cloud* (VPC) network firewall rule changes.

Monitoring for create or update firewall rule events gives insight to network access changes and may reduce the time it takes to detect suspicious activity.

## Remediation

### From Console

Create a required log metric:

1. Login to GCP console and navigate to [Logs-based Metrics](https://console.cloud.google.com/logs/metrics).
2. Click **CREATE METRIC**.
3. Select `Metric Type` as `Counter`, enter `Log metric name` and `Units` to 1.
4. Clear any text in the `Build filter` field and add below text
```
resource.type="gce_firewall_rule"
AND protoPayload.methodName="v1.compute.firewalls.patch" OR protoPayload.methodName="v1.compute.firewalls.insert"
```
5. Click **CREATE METRIC**. The logs display based on the filter text entered by the user.

Create a required alert policy for the log metric::

1. Identify the newly created metric under the section [User-defined Metrics](https://console.cloud.google.com/logs/metrics).
2. Click the 3-dot icon in the right most column for the desired metric and select **Create alert from metric**. A new page opens.
3. Fill out the alert policy configuration and click Save. Choose the alerting threshold and configuration that makes sense for the user's organization. For example, a threshold of zero(0) for the most recent value will ensure that a notification is triggered for every owner change in the project.
4. Configure the desired notifications channels in the section Notifications.
5. Enter `Alert name` and click **Save**.

### From Command Line

Create a required log metric:
   - Use the command: gcloud beta logging metrics create.
   - Reference for [Command Usage](https://cloud.google.com/sdk/gcloud/reference/beta/logging/metrics/create).

Create a alert policy for the log metric:
   - Use the command: gcloud alpha monitoring policies create.
   - Reference for [Command Usage](https://cloud.google.com/sdk/gcloud/reference/alpha/monitoring/policies/create).
