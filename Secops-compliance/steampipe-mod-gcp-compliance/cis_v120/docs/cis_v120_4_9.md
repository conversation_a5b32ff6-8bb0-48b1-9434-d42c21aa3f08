## Description

Compute instances should not be configured to have external IP addresses.

To reduce your attack surface, Compute instances should not have public IP addresses. Instead, instances should be configured behind load balancers, to minimize the instance's exposure to the internet.

## Remediation

To be able turn on Shielded VM on an instance, your instance must use an image with Shielded VM support.

### From Console

1. Login to [VM instances](https://console.cloud.google.com/compute/instances).
2. Click on the instance name to see its VM instance details page.
3. Click **EDIT**
4. For each `Network interface`, ensure that `External IP` is set to `None`.
5. Click Done and then click Save.

### From Command Line


1. Describe the instance properties:

```bash
gcloud compute instances describe INSTANCE_NAME --zone=ZONE
```

2. Identify the access config name that contains the external IP address. This access config appears in the following format:

```
networkInterfaces:
- accessConfigs:
- kind: compute#accessConfig
  name: External NAT
  natIP: **************
  type: ONE_TO_ONE_NAT
```

3. Delete the access config.

```bash
gcloud compute instances update INSTANCE_NAME --shielded-vm-secure-boot
```

4. Restart the instance:

```bash
gcloud compute instances delete-access-config INSTANCE_NAME --zone=ZONE --access-config-name "ACCESS_CONFIG_NAME"
```

**Note:** In the above example, the ACCESS_CONFIG_NAME is External NAT. The name of your access config might be different.
