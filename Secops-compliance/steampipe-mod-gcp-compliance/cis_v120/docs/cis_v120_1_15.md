## Description

It is recommended to rotate API keys every 90 days.

Security risks involved in using API-Keys appear below:

- API keys are simple encrypted strings
- API keys do not identify the user or the application making the API request
- API keys are typically accessible to clients, making it easy to discover and steal an API key

In light of these potential risks, Google recommends using the standard authentication flow instead of API keys. However, there are limited cases where API keys are more appropriate. For example, if there is a mobile application that needs to use the Google Cloud Translation API, but doesn't otherwise need a backend server, API keys are the simplest way to authenticate to that API.

Once a key is stolen, it has no expiration, meaning it may be used indefinitely unless the project owner revokes or regenerates the key. Rotating API keys will reduce the window of opportunity for an access key that is associated with a compromised or terminated account to be used.

## Remediation

API keys should be rotated to ensure that data cannot be accessed with an old key that might have been lost, cracked, or stolen.

### From Console

To find the listed API Keys with creation date

1. Login to [APIs & Services\Credentials](https://console.cloud.google.com/apis/credentials)
2. In the section `API Keys`, Click the `API Key Name`. The API Key properties display on a new page.
3. The page will display the `Creation date`
4. For every `API Key` exceeded the threshold `90` days, ensure below
5. Click **REGENERATE KEY** to rotate API key from the top section of the page.
6. Click Save.
