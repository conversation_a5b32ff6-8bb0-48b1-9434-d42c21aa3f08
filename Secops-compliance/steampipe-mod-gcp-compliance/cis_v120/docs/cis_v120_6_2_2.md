## Description

The log_error_verbosity flag controls the verbosity/details of messages logged. Valid values are *TERSE*, *DEFAULT*, and *VERBOSE*.

*TERSE* excludes the logging of *DETAIL*, *HINT*, *QUERY*, and *CONTEXT* error information.

*VERBOSE* output includes the *SQLSTATE* error code, source code file name, function name, and line number that generated the error.

Ensure an appropriate value is set to *DEFAULT* or stricter. Default value for *log_error_verbosity* flag is *DEFAULT*.

Auditing helps in troubleshooting operational problems and also permits forensic analysis. If *log_error_verbosity* is not set to the correct value, too many details or too few details may be logged. This flag should be configured with a value of *DEFAULT* or stricter. This recommendation is applicable to PostgreSQL database instances.

## Remediation

Perform the following action to check `log_error_verbosity` flag is set to *DEFAULT* or stricter:

### From Console:

1. Login to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance to open its instance Overview page.
3. Go to **Configuration** card.
4. Under Database flags, check the value of `log_error_verbosity` flag is set to `DEFAULT` or stricter.

Perform the following action to add `log_error_verbosity` flag is set to *DEFAULT* or stricter:

1. Login to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance for which you want to enable the database flag.
3. Click **EDIT**.
4. Under Flags section, click **ADD FLAG**.
5. choose the `flag log_error_verbosity` from the drop-down menu and set appropriate value.
6. Click **SAVE** to save your changes.

### From Command Line:

1. List all Cloud SQL database Instances

```bash
gcloud sql instances list
```
2. Configure the *log_error_verbosity* database flag for every Cloud SQL PostgreSQL database instance using the below command.

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags log_error_verbosity=<TERSE|DEFAULT|VERBOSE>
```

**Note**
- This command will overwrite all previously set database flags. To keep those and add new ones, include the values for all flags to be set on the instance. Any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
- Configuring the above flag does not require **restarting** the Cloud PostgreSQL instance.