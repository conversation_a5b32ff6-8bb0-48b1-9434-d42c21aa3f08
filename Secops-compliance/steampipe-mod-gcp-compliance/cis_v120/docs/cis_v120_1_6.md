## Description

It is recommended to assign the Service Account User (iam.serviceAccountUser) and Service Account Token Creator (iam.serviceAccountTokenCreator) roles to a user for a specific service account rather than assigning the role to a user at project level.

A service account is a special Google account that belongs to an application or a virtual machine (VM), instead of to an individual end-user. Application/VM-Instance uses the service account to call the service's Google API so that users aren't directly involved. In
addition to being an identity, a service account is a resource that has IAM policies attached to it. These policies determine who can use the service account.

Based on business needs, there could be multiple user-managed service accounts configured for a project. Granting the `iam.serviceAccountUser` or `iam.serviceAserviceAccountTokenCreatorccountUser` roles to a user for a project gives the user access to all service accounts in the project, including service accounts that may be created in the future. This can result in elevation of privileges by using service accounts and corresponding *Compute Engine instances*

In order to implement `least privileges` best practices, IAM users should not be assigned the Service Account User or Service Account Token Creator roles at the project level.
Instead, these roles should be assigned to a user for a specific service account, giving that user access to the service account. The `Service Account User` allows a user to bind a
service account to a long-running job service, whereas the `Service Account Token Creator role` allows a user to directly impersonate (or assert) the identity of a service account.

## Remediation

### From Console

1. Login to IAM page in the [GCP Console](https://console.cloud.google.com/iam-admin/iam.)
2. Click on the filter table text bar. Type `Role: Service Account User`
3. Click the `pencil` at the end of the member account row, click on **Delete** bin icon to remove role `Service Account User` for every user listed as a result of a filter.
4. Click on the filter table text bar. Type `Role: Service Account Token Creator`
5. Click the `pencil` at the end of the member account row, click on **Delete** bin icon to remove role `Service Account Token Creator` role for every user listed as a result of a filter.

### From Command Line

1. Get the associated IAM policy
  ```bash
  gcloud projects get-iam-policy PROJECT_ID --format json > iam.json
  ```
2. Using a text editor, remove the bindings with the *roles/iam.serviceAccountUser* or *roles/iam.serviceAccountTokenCreator*.
3. Update the project's IAM policy
  ```bash
  gcloud projects set-iam-policy PROJECT_ID iam.json
  ```
