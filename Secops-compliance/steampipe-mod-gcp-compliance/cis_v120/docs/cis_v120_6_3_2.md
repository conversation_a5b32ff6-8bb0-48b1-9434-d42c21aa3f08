## Description

It is recommended to set `cross db ownership chaining` database flag for Cloud SQL SQL Server instance to `off`.

Use the cross db ownership for chaining option to configure cross-database ownership chaining for an instance of Microsoft SQL Server. This server option allows you to control cross-database ownership chaining at the database level or to allow cross-database ownership chaining for all databases.

Enabling cross db ownership is not recommended unless all of the databases hosted by the instance of SQL Server must participate in `crossdatabase ownership chaining` and you are aware of the security implications of this setting.

## Remediation

This recommendation is applicable to SQL Server database instances.

### From Console

1. Login in to [Cloud SQL Instances](https://console.cloud.google.com/sql/instances)
2. Select the SQL Server instance for which you want to enable to database flag.
3. Under **Configuration** section
4. Scroll down to the `Database flags` section. Click on **Edit Configurations**
5. In the edit page, navigate to **Flags and Parameters** section
6. To set a flag that has not been set on the instance before, click Add item, choose the flag `cross db ownership chaining` from the drop-down menu, and set its value to `off`.
7. Click Save to save your changes.
8. Confirm your changes under Flags on the Overview page.

**Note:** Any changes to the database flag values, may require your instance to be restarted

### From Command Line

1. List all Cloud SQL database Instances

```bash
gcloud sql instances list
```

2. Configure the required database flag for every Cloud SQL SQL Server database instance using the below command.

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags "cross db ownership chaining=off"
```

**Note:** This command will overwrite all database flags previously set. To keep those and add new ones, include the values for all flags you want set on the
instance; any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals
sign ("=").
