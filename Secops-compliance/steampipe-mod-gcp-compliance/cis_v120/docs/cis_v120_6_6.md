## Description

It is recommended to configure Second Generation SQL instance to use private IPs instead of public IPs.

To lower the organization's attack surface, Cloud SQL databases should not have public IPs. Private IPs provide improved network security and lower latency for your application.

## Remediation

This recommendation is applicable for PostgreSQL, MySQL generation 1, MySQL generation 2 and SQL Server 2017 instances.

### From Console

1. <PERSON>gin in to [Cloud SQL Instances](https://console.cloud.google.com/sql/instances)
2. Select the SQL Server instance to open details page.
3. Select the `Connections` tab.
4. Deselect the **Public IP** checkbox.
5. Click Save to update the instance.

### From Command Line

1. For every instance remove its public IP and assign a private IP instead:

```bash
gcloud beta sql instances patch INSTANCE_NAME --network=VPC_NETWOR_NAME --noassign-ip
```

2. Confirm the changes using the following command:

```sql
gcloud sql instances describe INSTANCE_NAME
```

**Note:** By default, Cloud SQL instances have a public IP.
