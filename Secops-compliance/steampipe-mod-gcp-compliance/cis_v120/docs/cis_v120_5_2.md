## Description

It is recommended that uniform bucket-level access is enabled on Cloud Storage buckets. Cloud Storage buckets do not have uniform bucket-level access enabled as default.

Uniform bucket-level access can no longer be disabled if it has been active on a bucket for 90 consecutive days.

It is recommended to use uniform bucket-level access to unify and simplify how you grant access to your Cloud Storage resources.

Cloud Storage offers two systems for granting users permission to access your buckets and objects: Cloud Identity and Access Management (Cloud IAM) and Access Control Lists (ACLs). These systems act in parallel - in order for a user to access a Cloud Storage resource, only one of the systems needs to grant the user permission. Cloud IAM is used throughout Google Cloud and allows you to grant a variety of permissions at the bucket and project levels. ACLs are used only by Cloud Storage and have limited permission options, but they allow you to grant permissions on a per-object basis.

In order to support a uniform permissioning system, cloud storage has uniform bucket- level access. Using this feature disables ACLs for all Cloud Storage resources: access to Cloud Storage resources then is granted exclusively through Cloud IAM. Enabling uniform bucket-level access guarantees that if a Storage bucket is not publicly accessible, no object in the bucket is publicly accessible either.

When uniform bucket-level access is enabled, it will revoke access from users who gain their access solely through object ACLs.
Certain Google Cloud services, such as Stackdriver, Cloud Audit Logs, and Datastore, cannot export to Cloud Storage buckets that have uniform bucket-level access enabled.

## Remediation

### From Console

1. Login to GCP console and navigate to [Storage Browser](https://console.cloud.google.com/storage/browser).
2. In the list of buckets, choose the name of the desired bucket.
3. Go to **CONFIGURATION** tab.
3. Edit **Access control** and select `Uniform`.
4. Click **Save**.

### From Command Line

Use the on option in a uniformbucketlevelaccess set command:

```bash
gsutil uniformbucketlevelaccess set on gs://BUCKET_NAME/
```

