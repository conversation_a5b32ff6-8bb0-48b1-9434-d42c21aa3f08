## Description

It is recommended that IAM policy on Cloud Storage bucket does not allows anonymous or public access. Storage buckets are not publicly shared by default.

Allowing anonymous or public access grants permissions to anyone to access bucket content. Such access might not be desired if you are storing any sensitive data. Hence, ensure that anonymous or public access to a bucket is not allowed.

## Remediation

### From Console

1. <PERSON>gin to GCP console and navigate to [Storage Browser](https://console.cloud.google.com/storage/browser).
2. Click on the bucket name to go to **PERMISSIONS** tab.
3. Click **REMOVE PUBLIC PERMISSIONS**. Bucket will be `Not public`.

### From Command Line

Remove allUsers and allAuthenticatedUsers access.

```bash
gsutil iam ch -d allUsers gs://BUCKET_NAME
gsutil iam ch -d allAuthenticatedUsers gs://BUCKET_NAME
```

