## Description

PostgreSQL logs only the IP address of the connecting hosts. The *log_hostname* flag controls the logging of *hostnames* in addition to the IP addresses logged. The performance hit is dependent on the configuration of the environment and the host name resolution setup. This parameter can only be set in the *postgresql.conf* file or on the server command line. Default value for *log_hostname* flag is off.

Logging *hostnames* can incur overhead on server performance as for each statement logged, DNS resolution will be required to convert IP address to hostname. Depending on the setup, this may be non-negligible. Additionally, the IP addresses that are logged can be resolved to their DNS names later when reviewing the logs excluding the cases where dynamic *hostnames* are used. This recommendation is applicable to PostgreSQL database instances.

## Remediation

### From Console:

1. Login to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance for which the database flag needs to be enabled.
3. Click **EDIT**.
4. Scroll down to the *Flags* section.
5. Click **ADD FLAG**, choose the `log_hostname` from the drop-down menu, and set to an appropriate value.
6. Click **SAVE**.
7. Confirm the changes under Flags on the Overview page.

### From Command Line:

1. List all Cloud SQL database instances using the following command

```bash
gcloud sql instances list
```
2. Configure the *log_hostname* database flag for every Cloud SQL PostgreSQL database instance using the below command

```bash
cloud sql instances patch INSTANCE_NAME --database-flags log_hostname=<off|on>
```

**Note**
- This command will overwrite all previously set database flags. To keep those and add new ones, include the values for all flags to be set on the instance. Any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
- Configuring the above flag does not require **restarting** the Cloud PostgreSQL instance.