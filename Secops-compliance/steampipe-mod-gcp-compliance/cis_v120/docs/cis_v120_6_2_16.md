## Description

The *log_min_duration_statement* flag defines the minimum amount of execution time of a statement in milliseconds where the total duration of the statement is logged. Ensure that *log_min_duration_statement* flag is set to a value *-1*.

Default value for *log_min_duration_statement* flag is *-1*.

Logging SQL statements may include sensitive information that should not be recorded in logs.

## Remediation

### From Console:

1. <PERSON>gin to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance for which the database flag needs to be enabled.
3. Click **EDIT**.
4. Scroll down to the *Flags* section.
5. Click **ADD FLAG**, choose the `log_min_duration_statement` from the drop-down menu, and set to `-1`.
6. Click **SAVE**.
7. Confirm the changes under Flags on the Overview page.

### From Command Line:

1. List all Cloud SQL database instances using the following command

```bash
gcloud sql instances list
```
2. Configure the *log_min_duration_statement* database flag for every Cloud SQL PostgreSQL database instance using the below command

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags log_min_duration_statement=-1
```

**Note**
- This command will overwrite all previously set database flags. To keep those and add new ones, include the values for all flags to be set on the instance. Any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
- Configuring the above flag does not require **restarting** the Cloud PostgreSQL instance.