## Description

`Customer-Supplied Encryption Keys (CSEK)` are a feature in Google Cloud Storage and Google Compute Engine. If you supply your own encryption keys, Google uses your key to protect the Google-generated keys used to encrypt and decrypt your data. By default, Google Compute Engine encrypts all data at rest. Compute Engine handles and manages this encryption for you without any additional actions on your part. However, if you wanted to control and manage this encryption yourself, you can provide your own encryption keys.

If you provide your own encryption keys, Compute Engine uses your key to protect the Google-generated keys used to encrypt and decrypt your data. Only users who can provide the correct key can use resources protected by a customer-supplied encryption key.

## Remediation

At least business critical VMs should have VM disks encrypted with `CSEK`.

### From Console

1. Login to [Compute Engine Disks](https://console.cloud.google.com/compute/disks).
2. Click **CREATE DISK**.
3. Set Encryption type to Customer supplied,
4. Provide the Key in the box.
5. Select Wrapped key.
6. Click Create.

### From Command Line

1. In the gcloud compute tool, encrypt a disk using the `--csek-key-file` flag during instance creation. If you are using an RSA-wrapped key, use the gcloud beta component:

```bash
gcloud (beta) compute instances create INSTANCE_NAME --csek-key-file <example-file.json>
```

2. To encrypt a standalone persistent disk:

```bash
gcloud (beta) compute disks create DISK_NAME --csek-key-file <examplefile.json>
```
