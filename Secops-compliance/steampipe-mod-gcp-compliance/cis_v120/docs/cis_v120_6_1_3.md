## Description

It is recommended to set the *local_infile* database flag for a Cloud SQL MySQL instance to off. Default value for `local_infile` is on.

The local_infile flag controls the server-side LOCAL capability for LOAD DATA statements. Depending on the local_infile setting, the server refuses or permits local data loading by clients that have LOCAL enabled on the client side.

To explicitly cause the server to refuse LOAD DATA LOCAL statements (regardless of how client programs and libraries are configured at build time or runtime), start mysqld with local_infile disabled. local_infile can also be set at runtime.

Due to security issues associated with the local_infile flag, it is recommended to disable it. This recommendation is applicable to MySQL database instances.

## Remediation

### From Console:

1. Login to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the Mysql instance for which you want to enable to database flag.
3. Click **EDIT**.
4. Scroll down to the **Flags** section.
5. To set a flag that has not been set on the instance before, click **ADD FLAG** , choose the flag `local_infile` from the drop-down menu, and set its value to `off`.
6. Click **DONE**.
7. Click **SAVE** to save your changes.
8. Confirm your changes under `Flags` on the Overview page .


### From Command Line:

1. List all Cloud SQL database Instances

```bash
gcloud sql instances list
```
2. Configure the `local_infile` database flag for every Cloud SQL Mysql database instance using the below command

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags local_infile=off
```

**Note** This command will overwrite all database flags previously set. To keep those and add new ones, include the values for all flags you want set on the instance; any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
