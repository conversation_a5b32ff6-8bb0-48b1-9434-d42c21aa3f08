## Description

API keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to restrict API keys to use (call) only APIs required by an application.

Security risks involved in using API-Keys appear below:

- API keys are simple encrypted strings
- API keys do not identify the user or the application making the API request
- API keys are typically accessible to clients, making it easy to discover and steal an API key

In light of these potential risks, Google recommends using the standard authentication flow instead of API keys. However, there are limited cases where API keys are more appropriate. For example, if there is a mobile application that needs to use the Google Cloud Translation API, but doesn't otherwise need a backend server, API keys are the simplest way to authenticate to that API.

In order to reduce attack surfaces by providing least privileges, API-Keys can be restricted to use (call) only APIs required by an application.

## Remediation

### From Console

To find the listed API Keys with specified access to `API restrictions`

1. Login to [APIs & Services\Credentials](https://console.cloud.google.com/apis/credentials)
2. In the section `API Keys`, Click the `API Key Name`. The API Key properties display on a new page.
3. For every `API Key`, ensure below

- `Application restrictions` is not set to **None**
- `API restrictions` is not set to **Don't restrict key** or any **Google Cloud APIs**

**Note**: Google Cloud APIs represents the API collection of all cloud services/APIs offered by Google cloud. By default, Application Restrictions are set to None. More information on [API Keys](https://cloud.google.com/docs/authentication/api-keys)
