## Description

The *log_min_messages* flag defines the minimum message severity level that is considered as an error statement. Messages for error statements are logged with the SQL statement. Valid values include *DEBUG5*, *DEBUG4*, *DEBUG3*, *DEBUG2*, *DEBUG1*, *INFO*, *NOTICE*, *WARNING*, *ERROR*, *LOG*, *FATAL*, and *PANIC*. Each severity level includes the subsequent levels mentioned above.

ERROR is considered the best practice setting. Changes should only be made in accordance with the organization's logging policy.

To effectively turn off logging failing statements, set this parameter to *PANIC*. Default value for *log_min_error_statement* flag is *ERROR*.

Auditing helps in troubleshooting operational problems and also permits forensic analysis. If *log_min_error_statement* is not set to the correct value, messages may not be classified as error messages appropriately. Considering general log messages as error messages would make it difficult to find actual errors, while considering only stricter severity levels as error messages may skip actual errors to log their SQL statements.

The *log_min_messages* flag should be set in accordance with the organization's logging policy.


## Remediation

Perform the following action to check `log_min_messages` flag is set accordance with the organization's logging policy:

### From Console:

1. <PERSON><PERSON> to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance to open its instance Overview page.
3. Go to **Configuration** card.
4. Under Database flags, check the value of `log_min_messages` flag is set with appropriate value accordance with the organization's logging policy.

Perform the following action to add `log_min_messages` flag to set accordance with the organization's logging policy:

1. Login to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance for which you want to enable the database flag.
3. Click **EDIT**.
4. Under Flags section, click **ADD FLAG**.
5. choose the `log_min_messages` from the drop-down menu and set appropriate value accordance with the organization's logging policy.
6. Click **SAVE** to save your changes.

### From Command Line:

1. List all Cloud SQL database Instances

```bash
gcloud sql instances list
```
2. Configure the *log_min_messages* database flag for every Cloud SQL PostgreSQL database instance using the below command.

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags log_min_messages=<DEBUG5|DEBUG4|DEBUG3|DEBUG2|DEBUG1|INFO|NOTICE|WARNING|ERRO R|LOG|FATAL|PANIC>
```

**Note**
- This command will overwrite all previously set database flags. To keep those and add new ones, include the values for all flags to be set on the instance. Any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
- Configuring the above flag does not require **restarting** the Cloud PostgreSQL instance.