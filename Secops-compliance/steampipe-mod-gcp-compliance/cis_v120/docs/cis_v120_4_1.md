## Description

It is recommended to configure your instance to not use the `default Compute Engine service account` because it has the Editor role on the project.

The default Compute Engine service account has the Editor role on the project, which allows read and write access to most Google Cloud Services. To defend against privilege escalations if your VM is compromised and prevent an attacker from gaining access to all of your project, it is recommended to not use the default Compute Engine service account. Instead, you should create a new service account and assigning only the permissions
needed by your instance.

The default Compute Engine service account is named *[PROJECT_NUMBER]- <EMAIL>*.

**Note**: VMs created by GKE should be excluded. These VMs have names that start with gke- and are labeled goog-gke-node.

## Remediation

### From Console

1. Login to GCP VM [Console](https://console.cloud.google.com/compute/instances).
2. Click on the instance name to go to its VM instance details page.
3. Under the section `Service Account`, ensure that the default Compute Engine service account is not used. This account is named [PROJECT_NUMBER]-
<EMAIL>.
4. Click **STOP** to stop the instance and then click **EDIT** button in top section of the page.
5. Under the section `Service Account`, select a service account other than the default
Compute Engine service account. You may first need to create a [new service account](https://cloud.google.com/compute/docs/access/create-enable-service-accounts-for-instances).
6. Click Save and then click **START**.

### From Command Line

1. Stop the instance:

```bash
gcloud compute instances stop INSTANCE_NAME
```

2. Update the instance:

```bash
gcloud compute instances set-service-account INSTANCE_NAME --serviceaccount=SERVICE_ACCOUNT
```

3. Restart the instance:

```bash
gcloud compute instances start INSTANCE_NAME
```
