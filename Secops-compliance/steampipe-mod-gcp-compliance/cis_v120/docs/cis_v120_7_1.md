## Description

It is recommended that the IAM policy on BigQuery datasets does not allow anonymous and/or public access.

Granting permissions to allUsers or allAuthenticatedUsers allows anyone to access the dataset. Such access might not be desirable if sensitive data is being stored in the dataset. Therefore, ensure that anonymous and/or public access to a dataset is not allowed.

## Remediation

### From Console

1. <PERSON>gin to [BigQuery](https://console.cloud.google.com/bigquery).
2. Select the dataset from `Resources`.
3. Click **SHARE DATASET** near the right side of the window.
4. Review each attached role.
5. Click the delete icon for each member `allUsers` or `allAuthenticatedUsers`. On the popup click Remove.

### From Command Line

1. Retrieve the data set information:

```bash
bq show --format=prettyjson PROJECT_ID:DATASET_NAME > PATH_TO_FILE
```

2. In the access section of the JSON file, update the dataset information to remove all roles containing `allUsers` or `allAuthenticatedUsers`.

3. Update the dataset:

```bash
bq update --source PATH_TO_FILE PROJECT_ID:DATASET_NAME
```
