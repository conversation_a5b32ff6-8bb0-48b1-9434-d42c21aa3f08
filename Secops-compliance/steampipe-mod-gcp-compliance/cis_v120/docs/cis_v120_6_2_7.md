## Description

The value of *log_statement* flag determined the SQL statements that are logged. Valid values are *none*, *ddl*, *mod*, and *all*.

The value *ddl* logs all data definition statements. The value mod logs all ddl statements, plus data-modifying statements.

The statements are logged after a basic parsing is done and statement type is determined, thus this does not logs statements with errors. When using extended query protocol, logging occurs after an Execute message is received and values of the Bind parameters are included.

A value of *ddl* is recommended unless otherwise directed by your organization's logging policy.


## Remediation

Perform the following action to check `log_statement` flag is set to *DEFAULT* or stricter:

### From Console:

1. Login to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance to open its instance Overview page.
3. Go to **Configuration** card.
4. Under Database flags, check the value of `log_statement` flag is set with appropriate value.

Perform the following action to add `log_statement` flag is set to a appropriate value:

1. <PERSON>gin to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance for which you want to enable the database flag.
3. Click **EDIT**.
4. Under Flags section, click **ADD FLAG**.
5. choose the `log_statement` from the drop-down menu and set appropriate value.
6. Click **SAVE** to save your changes.

### From Command Line:

1. List all Cloud SQL database Instances

```bash
gcloud sql instances list
```
2. Configure the *log_statement* database flag for every Cloud SQL PostgreSQL database instance using the below command.

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags log_statement=<ddl|mod|all|none>
```

**Note**
- This command will overwrite all previously set database flags. To keep those and add new ones, include the values for all flags to be set on the instance. Any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
- Configuring the above flag does not require **restarting** the Cloud PostgreSQL instance.