## Description

Cloud Domain Name System (DNS) is a fast, reliable and cost-effective domain name system that powers millions of domains on the internet. Domain Name System Security Extensions (DNSSEC) in Cloud DNS enables domain owners to take easy steps to protect their domains against DNS hijacking and man-in-the-middle and other attacks.

`Domain Name System Security Extensions` (DNSSEC) adds security to the DNS protocol by enabling DNS responses to be validated. Having a trustworthy DNS that translates a domain name like www.example.com into its associated IP address is an increasingly important building block of today’s web-based applications. Attackers can hijack this process of domain/IP lookup and redirect users to a malicious site through DNS hijacking and man-in the-middle attacks. DNSSEC helps mitigate the risk of such attacks by cryptographically signing DNS records. As a result, it prevents attackers from issuing fake DNS responses that may misdirect browsers to nefarious websites.

## Remediation

### From Console

1. Login to [Cloud DNS](https://console.cloud.google.com/net-services/dns/zones)
2. For each `Zone type` as `Public` check & update **DNSSEC** to `On` from listing arrow.

### From Command Line

Use the below command to enable `DNSSEC` for Cloud DNS Zone Name.

```baash
gcloud dns managed-zones update ZONE_NAME --dnssec-state on
```
