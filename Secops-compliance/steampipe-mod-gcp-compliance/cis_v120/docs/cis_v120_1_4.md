## Description

User managed service accounts should not have user-managed keys.

Anyone who has access to the keys will be able to access resources through the service account. GCP-managed keys are used by Cloud Platform services such as App Engine and Compute Engine. These keys cannot be downloaded. Google will keep the keys and automatically rotate them on an approximately weekly basis. User-managed keys are created, downloadable, and managed by users. They expire 10 years from creation. By default, there are no user-managed keys created for user-managed service accounts.

For user-managed keys, the user has to take ownership of key management activities which include:
- Key storage
- Key distribution
- Key revocation
- Key rotation
- Protecting the keys from unauthorized users
- Key recovery

Even with key owner precautions, keys can be easily leaked by common development malpractices like checking keys into the source code or leaving them in the Downloads directory, or accidentally leaving them on support blogs/channels.

It is recommended to prevent user-managed service account keys

## Remediation

### From Console

1. Login to IAM page in the [GCP Console](https://console.cloud.google.com/iam-admin/iam)
2. In the left navigation pane, click `Service accounts`.
3. Check if their corresponding keys are listed.
4. Click the service account represented in the `Email` column.
5. Click dots in the `Actions` column to navigate `Manage Keys`.
6. Delete the keys.

### From Command Line

To delete a user managed Service Account Key,
  ```bash
  gcloud iam service-accounts keys delete --iam-account=<user-managed-serviceaccount-EMAIL> <KEY-ID>
  ```

