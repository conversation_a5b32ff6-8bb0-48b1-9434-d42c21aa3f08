## Description

Members (users/service-accounts) with a role assignment to primitive role roles/owner are project owners. The project owner has all the privileges on the project. Granting the owner role to a member (user/service-account) will allow that member to modify the identity and access management (IAM) policy. Therefore, grant the owner role only if the member has a legitimate purpose to manage the IAM policy.

In order to prevent unnecessary project ownership assignments to users/service-accounts and further misuses of projects and resources, all roles/owner assignments should be monitored.

Project ownership has the highest level of privileges on a project. To avoid misuse of project resources, the project ownership assignment or change actions should be monitored and alerted to concerned recipients.

## Remediation

### From Console

Create a required log metric:

1. Login to GCP console and navigate to [Logs-based Metrics](https://console.cloud.google.com/logs/metrics).
2. Click **CREATE METRIC**.
3. Select `Metric Type` as `Counter`, enter `Log metric name` and `Units` to 1.
4. Clear any text in the `Build filter` field and add below text
```
(protoPayload.serviceName="cloudresourcemanager.googleapis.com")
AND (ProjectOwnership OR projectOwnerInvitee)
OR (protoPayload.serviceData.policyDelta.bindingDeltas.action="REMOVE"
AND protoPayload.serviceData.policyDelta.bindingDeltas.role="roles/owner") OR (protoPayload.serviceData.policyDelta.bindingDeltas.action="ADD"
AND protoPayload.serviceData.policyDelta.bindingDeltas.role="roles/owner")
```
5. Click **CREATE METRIC**. The logs display based on the filter text entered by the user.

Create a required alert policy for the log metric::

1. Identify the newly created metric under the section [User-defined Metrics](https://console.cloud.google.com/logs/metrics).
2. Click the 3-dot icon in the right most column for the desired metric and select **Create alert from metric**. A new page opens.
3. Fill out the alert policy configuration and click Save. Choose the alerting threshold and configuration that makes sense for the user's organization. For example, a threshold of zero(0) for the most recent value will ensure that a notification is triggered for every owner change in the project.
4. Configure the desired notifications channels in the section Notifications.
5. Enter `Alert name` and click **Save**.

### From Command Line

Create a required log metric:
   - Use the command: gcloud beta logging metrics create.
   - Reference for [Command Usage](https://cloud.google.com/sdk/gcloud/reference/beta/logging/metrics/create).

Create a alert policy for the log metric:
   - Use the command: gcloud alpha monitoring policies create.
   - Reference for [Command Usage](https://cloud.google.com/sdk/gcloud/reference/alpha/monitoring/policies/create).

**Note**:

1. Project Ownership assignment to a service account does not send any invites. SetIAMPolicy to role/owneris directly performed on service accounts.
2. Project ownership assignments for a user cannot be done using the gcloud utility as assigning project ownership to a user requires sending, and the user accepting, an invitation.
