## Description

Enabling the *log_disconnections* setting logs the end of each session, including the session duration. Default value for *log_disconnections* flag is off.

PostgreSQL does not log session details such as duration and session end by default. Enabling the *log_disconnections* setting will create log entries at the end of each session which can be useful in troubleshooting issues and determine any unusual activity across a time period.

The *log_disconnections* and *log_connections* work hand in hand and generally, the pair would be enabled/disabled together.

## Remediation

### From Console:

1. <PERSON>gin to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance for which the database flag needs to be enabled.
3. Click **EDIT**.
4. Scroll down to the *Flags* section.
5. Click **ADD FLAG**, choose the `log_disconnections` from the drop-down menu, and set its to `on`.
6. Click **SAVE**.
7. Confirm the changes under Flags on the Overview page.

### From Command Line:

1. List all Cloud SQL database instances using the following command

```bash
gcloud sql instances list
```
2. Configure the *log_disconnections* database flag for every Cloud SQL PostgreSQL database instance using the below command

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags log_disconnections=on
```

**Note**
- This command will overwrite all previously set database flags. To keep those and add new ones, include the values for all flags to be set on the instance. Any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
- Configuring the above flag does not require **restarting** the Cloud PostgreSQL instance.