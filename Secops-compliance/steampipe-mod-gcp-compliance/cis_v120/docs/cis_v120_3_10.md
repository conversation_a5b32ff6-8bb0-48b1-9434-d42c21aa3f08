## Description

Access to VMs should be restricted by firewall rules that allow only `IAP traffic` by ensuring only connections proxied by the `IAP` are allowed. To ensure that load balancing works correctly health checks should also be allowed.

IAP ensure that access to VMs is controlled by authenticating incoming requests. However if the VM is still accessible from IP addresses other than the IAP it may still be possible to send unauthenticated requests to the instance. Care must be taken to ensure that load balancer health checks are not blocked as this would stop the load balancer from correctly knowing the health of the VM and load balancing correctly.

## Remediation

### From Console

To find whether the IAP settings are enabled in the Firewall rule or not:

1. Login to [VPC Network](https://console.cloud.google.com/networking/networks/).
2. Navigate to **Firewall** from left side panel.
3. Verify that the only rules correspond to the following values:
  - Targets: All instances in the network
  - Source IP ranges (press Enter after you paste each value in the box):
    - ***********/22
    - **********/16
  - Protocols and ports:
    - Specified protocols and ports
    - tcp:80

To make changes to VPC network > Firewall rules

1. Select the checkbox next to the following rules:
    - default-allow-http
    - default-allow-https
    - default-allow-internal
2. Click Delete.
3. Click Create firewall rule and set the following values:

    - Name: `allow-iap-traffic`
    - Targets: `All instances in the network`
    - Source IP ranges (press Enter after you paste each value in the box):
      - ***********/22
      - **********/16

    - Protocols and ports:
      - Specified protocols and ports
      - tcp:80

4. When you're finished updating values, click Create
