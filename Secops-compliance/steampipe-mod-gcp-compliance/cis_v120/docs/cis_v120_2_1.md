## Description

It is recommended that Cloud Audit Logging is configured to track all admin activities and read, write access to user data.
Admin Activity logs are always enabled. They cannot be disabled. Data Access audit logs are disabled by default because they can be quite large.

Cloud Audit Logging maintains two audit logs for each project, folder, and organization: Admin Activity and Data Access.

1. Admin Activity logs contain log entries for API calls or other administrative actions that modify the configuration or metadata of resources. Admin Activity audit logs are enabled for all services and cannot be configured.
2. Data Access audit logs record API calls that create, modify, or read user-provided data. These are disabled by default and should be enabled.

There are three kinds of Data Access audit log information:
   - Admin read: Records operations that read metadata or configuration information. Admin activity audit logs record writes of metadata and configuration information that cannot be disabled.
   - Data read: Records operations that read user-provided data.
   - Data write: Records operations that write user-provided data.

It is recommended to have an effective default audit config configured in such a way that:

1. Logtype is set to DATA_READ (to log user activity tracking) and DATA_WRITES (to log changes/tampering to user data).
2. Audit config is enabled for all the services supported by the Data Access audit logs feature.
3. Logs should be captured for all users, i.e., there are no exempted users in any of the audit config sections. This will ensure overriding the audit config will not contradict the requirement.

## Remediation

### From Console

1. Login to GCP console and navigate to [IAM & Admin](https://console.cloud.google.com/iam-admin/audit).
2. Click on **Default Audit Config** at the top of the page.
3. In the **LOG TYPE** tab, select the check boxes `Admin Read`, `Data Read`, and `Data Write`, and click **Save**.
4. In the **EXEMPTED USERS** tab, remove all users if any. Click **Save**.

For more details, you can refer [Configuring Data Access audit logs guide](https://cloud.google.com/logging/docs/audit/configure-data-access)

### From Command Line

1. To read the project's IAM policy and store it in a file run a command:

```bash
gcloud projects get-iam-policy PROJECT_ID > /tmp/project_policy.yaml
```
Alternatively, the policy can be set at the organization or folder level. If setting the policy at the organization level, it is not necessary to also set it for each folder or project.

```bash
gcloud organizations get-iam-policy ORGANIZATION_ID > /tmp/org_policy.yaml gcloud resource-manager folders get-iam-policy FOLDER_ID > /tmp/folder_policy.yaml
```
2. Edit policy in /tmp/policy.yaml, adding or changing only the audit logs configuration to:

```bash
auditConfigs:
- auditLogConfigs:
  - logType: DATA_WRITE
  - logType: DATA_READ
  service: allServices
```
**Note**: `exemptedMembers`: is not set as audit logging should be enabled for all the users

3. To write new IAM policy run command:

```bash
gcloud organizations set-iam-policy ORGANIZATION_ID /tmp/org_policy.yaml gcloud resource-manager folders set-iam-policy FOLDER_ID /tmp/folder_policy.yaml
gcloud projects set-iam-policy PROJECT_ID /tmp/project_policy.yaml
```
If the preceding command reports a conflict with another change, then repeat these steps, starting with the first step.