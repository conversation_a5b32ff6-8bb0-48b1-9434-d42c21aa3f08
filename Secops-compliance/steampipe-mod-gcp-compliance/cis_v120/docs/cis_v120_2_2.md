## Description

It is recommended to create a sink that will export copies of all the log entries. This can help aggregate logs from multiple projects and export them to a Security Information and Event Management (SIEM).

Log entries are held in Cloud Logging. To aggregate logs, export them to a SIEM. To keep them longer, it is recommended to set up a log sink. Exporting involves writing a filter that selects the log entries to export, and choosing a destination in Cloud Storage, BigQuery, or Cloud Pub/Sub. The filter and destination are held in an object called a sink. To ensure all log entries are exported to sinks, ensure that there is no filter configured for a sink. Sinks can be created in projects, organizations, folders, and billing accounts.

## Remediation

### From Console

1. Login to GCP console and navigate to [Logging](https://console.cloud.google.com/logs/viewer).
2. Click the down arrow symbol on `Filter Bar` at the rightmost corner and select `Convert to Advanced Filter`.
3. This step converts `Filter Bar` to `Advanced Filter Bar`.
4. Clear any text from the `Advanced Filter` field. This ensures that the `log-filter` is set to empty and captures all the logs.
5. Click `Submit Filter` and the result should display all logs.
6. Click `Create Sink`, which opens a menu on the right.
7. Fill out the fields and click `Create Sink`.

### From Command Line

1. To create a sink to export all log entries in a Google Cloud Storage bucket:

```bash
gcloud logging sinks create <sink-name> storage.googleapis.com/DESTINATION_BUCKET_NAME
```
2. Sinks can be created for a folder or organization, which will include all projects.

```bash
gcloud logging sinks create <sink-name> storage.googleapis.com/DESTINATION_BUCKET_NAME --include-children -- folder=FOLDER_ID | --organization=ORGANIZATION_ID
```

**Note**:
- A sink created by the command-line above will export logs in storage buckets. However, sinks can be configured to export logs into BigQuery, or Cloud Pub/Sub, or `Custom Destination`.
- While creating a sink, the sink option --log-filter is not used to ensure the sink exports all log entries.
- A sink can be created at a folder or organization level that collects the logs of all the projects underneath bypassing the option `--include-children` in the gcloud command.
- By default, there are no sinks configured.
