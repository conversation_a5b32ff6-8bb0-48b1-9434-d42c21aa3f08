## Description

Enabling the *log_duration* setting causes the duration of each completed statement to be logged. This does not logs the text of the query and thus behaves different from the *log_min_duration_statement* flag. This parameter cannot be changed after session start. Default value for *log_duration* flag is off.

Monitoring the time taken to execute the queries can be crucial in identifying any resource hogging queries and assessing the performance of the server. Further steps such as load balancing and use of optimized queries can be taken to ensure the performance and stability of the server. This recommendation is applicable to PostgreSQL database instances.


## Remediation

Perform the following action to check `log_duration` flag is set to `on`:

### From Console:

1. <PERSON>gin to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance to open its instance Overview page.
3. Go to **Configuration** card.
4. Under Database flags, check the value of `log_duration` flag is set to `on`.

Perform the following action to add `log_duration` flag is set to `on`:

1. <PERSON>gin to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the PostgreSQL instance for which you want to enable the database flag.
3. Click **EDIT**.
4. Under Flags section, click **ADD FLAG**.
5. choose the `log_duration` from the drop-down menu and set appropriate value to `on`.
6. Click **SAVE** to save your changes.

### From Command Line:

1. List all Cloud SQL database Instances

```bash
gcloud sql instances list
```
2. Configure the *log_duration* database flag for every Cloud SQL PostgreSQL database instance using the below command.

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags log_duration=on
```

**Note**
- This command will overwrite all previously set database flags. To keep those and add new ones, include the values for all flags to be set on the instance. Any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
- Configuring the above flag does not require **restarting** the Cloud PostgreSQL instance.