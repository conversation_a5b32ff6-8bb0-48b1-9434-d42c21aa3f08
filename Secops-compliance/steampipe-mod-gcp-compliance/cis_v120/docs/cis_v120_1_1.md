## Description

Use corporate login credentials instead of personal accounts, such as Gmail accounts.

It is recommended fully-managed corporate Google accounts be used for increased visibility, auditing, and controlling access to Cloud Platform resources. Email accounts based outside of the user's organization, such as personal accounts, should not be used for business purposes.

## Remediation

To find if the GCP project is associated with the corporate organization domain:

1. List the accounts that have been granted access to that project
    ```bash
    gcloud projects get-iam-policy PROJECT_ID
    ```
2. Also list the accounts added on each folder
    ```bash
    gcloud resource-manager folders get-iam-policy FOLDER_ID
    ```
3. And list your organization's IAM policy
    ```bash
    gcloud organizations get-iam-policy ORGANIZATION_ID
    ```

**Note** No email accounts outside the organization domain should be granted permissions in the IAM policies.

Follow the documentation and setup corporate login accounts.

To ensure that no email addresses outside the organization can be granted IAM permissions to its Google Cloud projects, folders or organization, turn on the Organization `Policy for Domain Restricted Sharing`. [Learn more at](https://cloud.google.com/resource-manager/docs/organization-policy/restricting-domains)
