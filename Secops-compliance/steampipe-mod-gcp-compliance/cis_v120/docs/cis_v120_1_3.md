## Description

Setup Security Key Enforcement for Google Cloud Platform admin accounts.

Google Cloud Platform users with Organization Administrator roles have the highest level of privilege in the organization. These accounts should be protected with the strongest form of two-factor authentication: Security Key Enforcement. Ensure that admins use Security Keys to log in instead of weaker second factors like SMS or one-time passwords (OTP). Security Keys are actual physical keys used to access Google Organization Administrator Accounts. They send an encrypted signature rather than a code, ensuring that logins cannot be phished.

## Remediation

1. Identify users with Organization Administrator privileges
  ```bash
  gcloud organizations get-iam-policy ORGANIZATION_ID
  ```

2. Look for members granted the role "roles/resourcemanager.organizationAdmin".
3. Manually verify that Security Key Enforcement has been enabled for each account. Setup Security Key Enforcement for each account. [Learn more](https://cloud.google.com/security-key/)
