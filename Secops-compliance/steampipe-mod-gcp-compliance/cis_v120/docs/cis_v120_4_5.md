## Description

Interacting with a serial port is often referred to as the serial console, which is similar to using a terminal window, in that input and output is entirely in text mode and there is no graphical interface or mouse support. If you enable the interactive serial console on an instance, clients can attempt to connect to that instance from any IP address. Therefore interactive serial console support should be disabled.

A virtual machine instance has four virtual serial ports. Interacting with a serial port is similar to using a terminal window, in that input and output is entirely in text mode and there is no graphical interface or mouse support.

The interactive serial console does not support IP-based access restrictions such as IP whitelists. If you enable the interactive serial console on an instance, clients can attempt to connect to that instance from any IP address. This allows anybody to connect to that instance if they know the correct SSH key, username, project ID, zone, and instance name.

## Remediation

Interactive serial console support should be disabled.

### From Console

1. Login to GCP VM [Console](https://console.cloud.google.com/compute/instances).
2. Select the instance and click **Edit**.
3. Unselect Enable connecting to serial ports below `Remote access` block.
4. Click Save

### From Command Line

1. Use the below command to disable

```bash
gcloud compute instances add-metadata INSTANCE_NAME --zone=ZONE --metadata=serial-port-enable=false
```
