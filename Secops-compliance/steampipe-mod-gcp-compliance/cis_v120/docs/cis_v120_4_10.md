## Description

In order to maintain the highest level of security all connections to an application should be secure by default.

Insecure HTTP connections maybe subject to eavesdropping which can expose sensitive data.

## Remediation

Add a line to the app.yaml file controlling the application which enforces secure connections. For example

```
handlers:
- url: /.*
  **secure: always**
  redirect_http_response_code: 301
  script: auto
```

For more details see [here](https://cloud.google.com/appengine/docs/standard/python3/config/appref)
