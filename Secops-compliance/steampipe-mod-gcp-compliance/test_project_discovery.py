#!/usr/bin/env python3
"""Test script to verify project discovery is working correctly"""

import os
import sys
from google.cloud import resourcemanager_v3
from google.auth import default

def test_project_discovery():
    """Test project discovery with current credentials"""
    print("Testing GCP project discovery...")
    
    # Get credentials
    try:
        credentials, project_id = default()
        print(f"✓ Authentication successful. Current project: {project_id}")
    except Exception as e:
        print(f"✗ Authentication failed: {e}")
        return
    
    # Initialize clients
    projects_client = resourcemanager_v3.ProjectsClient(credentials=credentials)
    
    # Test 1: Search projects API
    print("\nTest 1: Using search_projects API...")
    try:
        search_request = resourcemanager_v3.SearchProjectsRequest()
        search_result = projects_client.search_projects(request=search_request)
        
        projects = []
        for project in search_result:
            projects.append({
                "id": project.project_id,
                "name": project.display_name,
                "state": project.state.name if hasattr(project, 'state') else 'UNKNOWN'
            })
        
        print(f"✓ Found {len(projects)} projects via search API")
        for i, p in enumerate(projects[:5]):
            print(f"  {i+1}. {p['id']} - {p['name']} (State: {p['state']})")
        if len(projects) > 5:
            print(f"  ... and {len(projects) - 5} more")
            
    except Exception as e:
        print(f"✗ Search projects failed: {e}")
    
    # Test 2: Organization-based listing
    print("\nTest 2: Attempting organization-based listing...")
    try:
        # Try to get organization ID
        project = projects_client.get_project(name=f"projects/{project_id}")
        parent = project.parent
        
        org_id = None
        if parent and parent.startswith("organizations/"):
            org_id = parent.split('/')[1]
            print(f"✓ Found organization ID: {org_id}")
            
            # List projects in organization
            org_projects = []
            folders_client = resourcemanager_v3.FoldersClient(credentials=credentials)
            
            def process_container(container):
                try:
                    for project in projects_client.list_projects(
                        request={"parent": container, "show_deleted": False}
                    ):
                        org_projects.append({
                            "id": project.project_id,
                            "name": project.display_name,
                            "state": project.state.name if hasattr(project, 'state') else 'UNKNOWN'
                        })
                except Exception as e:
                    print(f"  Warning: Error listing projects in {container}: {e}")
                
                # Process folders
                try:
                    for folder in folders_client.list_folders(request={"parent": container}):
                        process_container(folder.name)
                except Exception as e:
                    print(f"  Warning: Error listing folders in {container}: {e}")
            
            process_container(f"organizations/{org_id}")
            
            print(f"✓ Found {len(org_projects)} projects in organization")
            for i, p in enumerate(org_projects[:5]):
                print(f"  {i+1}. {p['id']} - {p['name']} (State: {p['state']})")
            if len(org_projects) > 5:
                print(f"  ... and {len(org_projects) - 5} more")
        else:
            print("✗ No organization found for current project")
            
    except Exception as e:
        print(f"✗ Organization-based listing failed: {e}")
    
    print("\nTest complete.")

if __name__ == "__main__":
    test_project_discovery()