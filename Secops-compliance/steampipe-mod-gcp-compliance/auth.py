from flask import Blueprint, request, jsonify, abort
from googleapiclient.discovery import build
from google.auth import default
from google.oauth2 import service_account
from google.cloud import secretmanager
from functools import wraps
import os
import json
from google.auth.credentials import Credentials
import logging
import subprocess
import traceback


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create a blueprint
auth = Blueprint('auth', __name__)

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # Path to parent directory of 'server'
AUTHENTICATION_FOLDER = os.path.join(BASE_DIR, 'authentication')
SERVICE_ACCOUNT_FILE = os.path.join(AUTHENTICATION_FOLDER, 'service-account.json')

def get_service_credentials():
    try:
        # Get authentication type from environment variables
        auth_type = os.getenv('AUTH_TYPE', 'sa').lower()  # Default to service account if not specified
        logger.info(f"Using authentication type: {auth_type}")

        if auth_type == 'adc':
            try:
                logger.info("Attempting to use Application Default Credentials")
                credentials, project_id = default()
                logger.info(f"ADC Credentials path: {os.getenv('GOOGLE_APPLICATION_CREDENTIALS')}")
                logger.info(f"Credential type: {type(credentials).__name__}")
                logger.info(f"Valid credentials: {credentials.valid}")
                logger.info(f"Expiry: {credentials.expiry}")
                return credentials, project_id
            except Exception as e:
                logger.error(f"ADC authentication failed: {str(e)}")
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return None, f"ADC authentication failed: {str(e)}"
        
        try:
            # Initialize Secret Manager client
            credentials, project_id = default()
            secret_client = secretmanager.SecretManagerServiceClient(credentials=credentials)
            
            customer_id = os.getenv('CUSTOMER_ID')
            project_id = os.getenv('PROJECT_ID')
            logger.error(f"project id of env: {project_id}")

            if not customer_id:
                error_msg = "CUSTOMER_ID environment variable is not set"
                logger.error(error_msg)
                return None, error_msg
            
            secret_id = f"sa-gcp-{customer_id}-secops".lower().replace('_', '-')
            name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"

            try:
                response = secret_client.access_secret_version(request={"name": name})
                secret_content = response.payload.data.decode("UTF-8")
            except Exception as e:
                logger.error(f"Failed to access secret: {str(e)}")
                logger.error(f"Secret path attempted: {name}")
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return None, f"Failed to access secret: {str(e)}"

            try:
                service_account_json = json.loads(secret_content)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in secret content: {str(e)}")
                return None, f"Invalid JSON in secret content: {str(e)}"

            try:
                os.makedirs(AUTHENTICATION_FOLDER, mode=0o755, exist_ok=True)
            except Exception as e:
                logger.error(f"Failed to create authentication folder: {str(e)}")
                logger.error(f"Folder path: {AUTHENTICATION_FOLDER}")
                return None, f"Failed to create authentication folder: {str(e)}"
            
            file_path = os.path.join(AUTHENTICATION_FOLDER, 'service-account.json')
            print(f"File path: {file_path}")
            
            try:
                with open(file_path, 'w') as f:
                    json.dump(service_account_json, f, indent=2)
            except Exception as e:
                logger.error(f"Failed to write service account file: {str(e)}")
                logger.error(f"File path: {file_path}")
                return None, f"Failed to write service account file: {str(e)}"

            project_id = service_account_json.get('project_id')
            if not project_id:
                error_msg = "project_id not found in service account JSON"
                logger.error(error_msg)
                return None, error_msg

            try:
                credentials = service_account.Credentials.from_service_account_file(file_path)
                # os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = file_path
                
                # logger.info(f"Service account Credentials path: {os.getenv('GOOGLE_APPLICATION_CREDENTIALS')}")
                logger.info(f"Credential type: {type(credentials).__name__}")
                logger.info(f"Valid credentials: {credentials.valid}")
                logger.info(f"Expiry: {credentials.expiry}")
                
                # try:
                #     subprocess.run([
                #         "gcloud", "auth", "activate-service-account",
                #         "--key-file", file_path
                #     ], check=True, capture_output=True, text=True)
                # except subprocess.CalledProcessError as e:
                #     logger.error(f"Failed to activate service account: {str(e)}")
                #     logger.error(f"Command output: {e.output}")
                #     return None, f"Failed to activate service account: {str(e)}"

                return credentials, project_id

            except Exception as e:
                logger.error(f"Failed to create credentials from service account file: {str(e)}")
                logger.error(f"File path: {file_path}")
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return None, f"Failed to create credentials from service account file: {str(e)}"

        except Exception as e:
            logger.error(f"Service account authentication failed: {str(e)}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"Service account authentication failed: {str(e)}"
            
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Authentication error: {str(e)}"

