#!/usr/bin/env python3
"""
Test script to check for circular dependencies and missing references in the Powerpipe mod
"""
import os
import re
from collections import defaultdict
import glob

def find_pp_files():
    """Find all .pp files in the current directory"""
    return glob.glob("**/*.pp", recursive=True)

def extract_dependencies(file_path):
    """Extract benchmarks, controls, queries, and locals referenced in a file"""
    with open(file_path, 'r') as f:
        content = f.read()
    
    dependencies = {
        'defines': {
            'benchmarks': set(),
            'controls': set(),
            'queries': set(),
            'locals': set()
        },
        'references': {
            'benchmarks': set(),
            'controls': set(),
            'queries': set(),
            'locals': set()
        }
    }
    
    # Find definitions
    for match in re.finditer(r'benchmark\s+"([^"]+)"', content):
        dependencies['defines']['benchmarks'].add(match.group(1))
    
    for match in re.finditer(r'control\s+"([^"]+)"', content):
        dependencies['defines']['controls'].add(match.group(1))
        
    for match in re.finditer(r'query\s+"([^"]+)"', content):
        dependencies['defines']['queries'].add(match.group(1))
    
    # Find locals definitions
    for match in re.finditer(r'locals\s*{([^}]+)}', content, re.DOTALL):
        locals_block = match.group(1)
        for local_match in re.finditer(r'(\w+)\s*=', locals_block):
            dependencies['defines']['locals'].add(f"local.{local_match.group(1)}")
    
    # Find references
    for match in re.finditer(r'benchmark\.(\w+)', content):
        if match.group(1) not in ['title', 'description', 'documentation', 'children', 'tags']:
            dependencies['references']['benchmarks'].add(match.group(1))
    
    for match in re.finditer(r'control\.(\w+)', content):
        if match.group(1) not in ['title', 'description', 'query', 'documentation', 'tags']:
            dependencies['references']['controls'].add(match.group(1))
            
    for match in re.finditer(r'query\.(\w+)', content):
        dependencies['references']['queries'].add(match.group(1))
    
    for match in re.finditer(r'local\.(\w+)', content):
        dependencies['references']['locals'].add(f"local.{match.group(1)}")
    
    return dependencies

def main():
    """Main function to check dependencies"""
    pp_files = find_pp_files()
    print(f"Found {len(pp_files)} .pp files")
    
    all_definitions = {
        'benchmarks': set(),
        'controls': set(),
        'queries': set(),
        'locals': set()
    }
    
    all_references = {
        'benchmarks': set(),
        'controls': set(),
        'queries': set(),
        'locals': set()
    }
    
    file_deps = {}
    
    # Collect all definitions and references
    for file_path in pp_files:
        deps = extract_dependencies(file_path)
        file_deps[file_path] = deps
        
        for type_name in ['benchmarks', 'controls', 'queries', 'locals']:
            all_definitions[type_name].update(deps['defines'][type_name])
            all_references[type_name].update(deps['references'][type_name])
    
    # Find missing definitions
    print("\n=== Missing Definitions ===")
    issues_found = False
    
    for type_name in ['benchmarks', 'controls', 'queries', 'locals']:
        missing = all_references[type_name] - all_definitions[type_name]
        if missing:
            issues_found = True
            print(f"\nMissing {type_name}:")
            for item in sorted(missing):
                print(f"  - {item}")
                # Find which files reference this missing item
                referencing_files = []
                for file_path, deps in file_deps.items():
                    if item in deps['references'][type_name]:
                        referencing_files.append(file_path)
                if referencing_files:
                    print(f"    Referenced in: {', '.join(referencing_files[:3])}{'...' if len(referencing_files) > 3 else ''}")
    
    # Check for circular dependencies
    print("\n=== Checking for Circular Dependencies ===")
    # This is a simplified check - just looking for files that reference each other
    
    if not issues_found:
        print("\nNo missing definitions found!")
    
    # Special check for common_dimensions_sql and tag_dimensions_sql
    print("\n=== Checking for specific locals ===")
    for local_name in ['local.common_dimensions_sql', 'local.tag_dimensions_sql']:
        if local_name in all_definitions['locals']:
            print(f"✓ {local_name} is defined")
        else:
            print(f"✗ {local_name} is NOT defined")

if __name__ == "__main__":
    main()