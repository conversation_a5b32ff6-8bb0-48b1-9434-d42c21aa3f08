#!/usr/bin/env python3
"""
Validate that all referenced queries and controls exist in the mod
"""
import os
import re
from collections import defaultdict

def extract_all_definitions():
    """Extract all defined queries, controls, and benchmarks"""
    definitions = {
        'queries': set(),
        'controls': set(),
        'benchmarks': set(),
        'locals': set()
    }
    
    references = {
        'queries': set(),
        'controls': set(),
        'benchmarks': set(),
        'locals': set()
    }
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pp'):
                filepath = os.path.join(root, file)
                with open(filepath, 'r') as f:
                    content = f.read()
                
                # Extract query definitions
                for match in re.finditer(r'query\s+"([^"]+)"', content):
                    definitions['queries'].add(match.group(1))
                
                # Extract control definitions
                for match in re.finditer(r'control\s+"([^"]+)"', content):
                    definitions['controls'].add(match.group(1))
                
                # Extract benchmark definitions
                for match in re.finditer(r'benchmark\s+"([^"]+)"', content):
                    definitions['benchmarks'].add(match.group(1))
                
                # Extract query references in controls
                for match in re.finditer(r'query\s*=\s*query\.(\w+)', content):
                    references['queries'].add(match.group(1))
                
                # Extract control/benchmark references in benchmark children
                for match in re.finditer(r'control\.(\w+)', content):
                    if match.group(1) not in ['title', 'description', 'query', 'documentation', 'tags', 'severity']:
                        references['controls'].add(match.group(1))
                
                for match in re.finditer(r'benchmark\.(\w+)', content):
                    if match.group(1) not in ['title', 'description', 'documentation', 'children', 'tags']:
                        references['benchmarks'].add(match.group(1))
    
    return definitions, references

def main():
    definitions, references = extract_all_definitions()
    
    print(f"=== Summary ===")
    print(f"Defined queries: {len(definitions['queries'])}")
    print(f"Defined controls: {len(definitions['controls'])}")
    print(f"Defined benchmarks: {len(definitions['benchmarks'])}")
    print(f"\nReferenced queries: {len(references['queries'])}")
    print(f"Referenced controls: {len(references['controls'])}")
    print(f"Referenced benchmarks: {len(references['benchmarks'])}")
    
    # Check for missing items
    missing_queries = references['queries'] - definitions['queries']
    missing_controls = references['controls'] - definitions['controls']
    missing_benchmarks = references['benchmarks'] - definitions['benchmarks']
    
    issues_found = False
    
    if missing_queries:
        issues_found = True
        print(f"\n=== Missing Queries ({len(missing_queries)}) ===")
        for q in sorted(missing_queries):
            print(f"  - query.{q}")
    
    if missing_controls:
        issues_found = True
        print(f"\n=== Missing Controls ({len(missing_controls)}) ===")
        for c in sorted(missing_controls):
            print(f"  - control.{c}")
    
    if missing_benchmarks:
        issues_found = True
        print(f"\n=== Missing Benchmarks ({len(missing_benchmarks)}) ===")
        for b in sorted(missing_benchmarks):
            print(f"  - benchmark.{b}")
    
    if not issues_found:
        print("\n✓ All references are properly defined!")
    else:
        print(f"\n✗ Found {len(missing_queries) + len(missing_controls) + len(missing_benchmarks)} missing references!")
        
        # Try to find specific issues with cft_scorecard_v1
        print("\n=== Checking cft_scorecard_v1 specifically ===")
        cft_file = "./cft_scorecard_v1/cft_scorecard.pp"
        if os.path.exists(cft_file):
            with open(cft_file, 'r') as f:
                content = f.read()
            
            # Extract all control references
            controls_in_cft = []
            for match in re.finditer(r'control\.(\w+)', content):
                control_name = match.group(1)
                if control_name not in ['title', 'description', 'query', 'documentation', 'tags', 'severity']:
                    controls_in_cft.append(control_name)
                    if control_name in missing_controls:
                        print(f"  ✗ Missing: control.{control_name}")
                    else:
                        print(f"  ✓ Found: control.{control_name}")

if __name__ == "__main__":
    main()