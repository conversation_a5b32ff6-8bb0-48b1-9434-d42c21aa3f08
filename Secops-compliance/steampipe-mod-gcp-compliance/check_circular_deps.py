#!/usr/bin/env python3
"""
Check for circular dependencies in benchmark and control references
"""
import os
import re
from collections import defaultdict, deque

def parse_benchmark_file(filepath):
    """Parse a .pp file and extract benchmark/control definitions and their children"""
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Extract benchmark definitions and their children
    benchmarks = {}
    benchmark_pattern = r'benchmark\s+"([^"]+)"\s*{([^}]+)}'
    for match in re.finditer(benchmark_pattern, content, re.DOTALL):
        name = match.group(1)
        block = match.group(2)
        
        # Extract children
        children_match = re.search(r'children\s*=\s*\[([^\]]+)\]', block)
        children = []
        if children_match:
            children_text = children_match.group(1)
            # Extract control and benchmark references
            for child in re.findall(r'(control|benchmark)\.(\w+)', children_text):
                children.append(f"{child[0]}.{child[1]}")
        
        benchmarks[f"benchmark.{name}"] = children
    
    # Extract control definitions
    controls = {}
    control_pattern = r'control\s+"([^"]+)"\s*{'
    for match in re.finditer(control_pattern, content):
        name = match.group(1)
        controls[f"control.{name}"] = []
    
    return benchmarks, controls

def check_circular_deps():
    """Check for circular dependencies in all .pp files"""
    all_benchmarks = {}
    all_controls = {}
    
    # Parse all .pp files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pp'):
                filepath = os.path.join(root, file)
                benchmarks, controls = parse_benchmark_file(filepath)
                all_benchmarks.update(benchmarks)
                all_controls.update(controls)
    
    print(f"Found {len(all_benchmarks)} benchmarks and {len(all_controls)} controls")
    
    # Build dependency graph
    graph = defaultdict(list)
    for parent, children in all_benchmarks.items():
        for child in children:
            graph[parent].append(child)
    
    # Check for cycles using DFS
    def has_cycle(node, visited, rec_stack, path):
        visited.add(node)
        rec_stack.add(node)
        path.append(node)
        
        for neighbor in graph.get(node, []):
            if neighbor not in visited:
                if has_cycle(neighbor, visited, rec_stack, path):
                    return True
            elif neighbor in rec_stack:
                # Found a cycle
                cycle_start = path.index(neighbor)
                cycle = path[cycle_start:] + [neighbor]
                print(f"\nFound circular dependency:")
                print(" -> ".join(cycle))
                return True
        
        path.pop()
        rec_stack.remove(node)
        return False
    
    # Check each benchmark for cycles
    visited = set()
    found_cycles = False
    for benchmark in all_benchmarks:
        if benchmark not in visited:
            rec_stack = set()
            path = []
            if has_cycle(benchmark, visited, rec_stack, path):
                found_cycles = True
    
    if not found_cycles:
        print("\nNo circular dependencies found!")
    
    # Check for missing references
    print("\n=== Missing References ===")
    all_defined = set(all_benchmarks.keys()) | set(all_controls.keys())
    all_referenced = set()
    
    for children in all_benchmarks.values():
        all_referenced.update(children)
    
    missing = all_referenced - all_defined
    if missing:
        print("\nMissing definitions:")
        for item in sorted(missing):
            print(f"  - {item}")
            # Find which benchmarks reference this
            for parent, children in all_benchmarks.items():
                if item in children:
                    print(f"    Referenced by: {parent}")

if __name__ == "__main__":
    check_circular_deps()