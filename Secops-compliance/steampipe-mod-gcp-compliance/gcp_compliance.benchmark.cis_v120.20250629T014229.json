{"group_id": "root_result_group", "title": "CIS v1.2.0", "description": "", "tags": {}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 83}}, "groups": [{"group_id": "gcp_compliance.benchmark.cis_v120", "title": "CIS v1.2.0", "description": "The CIS Google Cloud Platform Foundations Security Benchmark covers foundational elements of Google Cloud Platform.", "tags": {"benchmark": "cis", "category": "Compliance", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 83}}, "groups": [{"group_id": "gcp_compliance.benchmark.cis_v120_1", "title": "1 Identity and Access Management", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "1", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 15}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_1", "description": "Use corporate login credentials instead of personal accounts, such as Gmail accounts.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.1", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.1 Ensure that corporate login credentials are used", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_2", "description": "Setup multi-factor authentication for Google Cloud Platform accounts.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.2", "cis_level": "1", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.2 Ensure that multi-factor authentication is enabled for all non-service accounts", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_3", "description": "Setup Security Key Enforcement for Google Cloud Platform admin accounts.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.3", "cis_level": "2", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.3 Ensure that Security Key Enforcement is enabled for all admin accounts", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_4", "description": "User managed service accounts should not have user-managed keys.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.4", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.4 Ensure that there are only GCP-managed service account keys for each service account", "run_status": 8, "run_error": "relation \"gcp_service_account_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_5", "description": "A service account is a special Google account that belongs to an application or a VM, instead of to an individual end-user. The application uses the service account to call the service's Google API so that users aren't directly involved. It's recommended not to use admin access for ServiceAccount.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.5", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.5 Ensure that Service Account has no Admin privileges", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_6", "description": "It is recommended to assign the Service Account User (iam.serviceAccountUser) and Service Account Token Creator (iam.serviceAccountTokenCreator) roles to a user for a specific service account rather than assigning the role to a user at project level.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.6", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.6 Ensure that IAM users are not assigned the Service Account User or Service Account Token Creator roles at project level", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_7", "description": "Service Account keys consist of a key ID (Private_key_Id) and Private key, which are used to sign programmatic requests users make to Google cloud services accessible to that particular service account. It is recommended that all Service Account keys are regularly rotated.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.7", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.7 Ensure user-managed/external keys for service accounts are rotated every 90 days or less", "run_status": 8, "run_error": "relation \"gcp_service_account_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_8", "description": "It is recommended that the principle of 'Separation of Duties' is enforced while assigning service-account related roles to users.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.8", "cis_level": "2", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.8 Ensure that Separation of duties is enforced while assigning service account related roles to users", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_9", "description": "It is recommended that the IAM policy on Cloud KMS cryptokeys should restrict anonymous and/or public access.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.9", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/KMS"}, "title": "1.9 Ensure that Cloud KMS cryptokeys are not anonymously or publicly accessible", "run_status": 8, "run_error": "relation \"gcp_kms_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_10", "description": "Google Cloud Key Management Service stores cryptographic keys in a hierarchical structure designed for useful and elegant access control management. The format for the rotation schedule depends on the client library that is used. For the gcloud command-line tool, the next rotation time must be in ISO or RFC3339 format, and the rotation period must be in the form INTEGER[UNIT], where units can be one of seconds (s), minutes (m), hours (h) or days (d).", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.10", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/KMS"}, "title": "1.10 Ensure KMS encryption keys are rotated within a period of 90 days", "run_status": 8, "run_error": "relation \"gcp_kms_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_11", "description": "It is recommended that the principle of 'Separation of Duties' is enforced while assigning KMS related roles to users.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.11", "cis_level": "2", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/KMS"}, "title": "1.11 Ensure that Separation of duties is enforced while assigning KMS related roles to users", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_12", "description": "Keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to use standard authentication flow instead.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.12", "cis_level": "2", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.12 Ensure API keys are not created for a project", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_13", "description": "Unrestricted keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to restrict API key usage to trusted hosts, HTTP referrers and apps.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.13", "cis_level": "1", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.13 Ensure API keys are restricted to use by only specified Hosts and Apps", "run_status": 8, "run_error": "relation \"gcp_apikeys_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_14", "description": "API keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to restrict API keys to use (call) only APIs required by an application.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.14", "cis_level": "1", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.14 Ensure API keys are restricted to only APIs that application needs access", "run_status": 8, "run_error": "relation \"gcp_apikeys_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_1_15", "description": "It is recommended to rotate API keys every 90 days.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.15", "cis_level": "1", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.15 Ensure API keys are rotated every 90 days", "run_status": 8, "run_error": "relation \"gcp_apikeys_key\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v120_2", "title": "2 Logging and Monitoring", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "2", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 12}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_1", "description": "It is recommended that Cloud Audit Logging is configured to track all admin activities and read, write access to user data.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.1", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.1 Ensure that Cloud Audit Logging is configured properly across all services and all users from a project", "run_status": 8, "run_error": "relation \"gcp_audit_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_2", "description": "It is recommended to create a sink that will export copies of all the log entries. This can help aggregate logs from multiple projects and export them to a Security Information and Event Management (SIEM).", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.2", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.2 Ensure that sinks are configured for all log entries", "run_status": 8, "run_error": "relation \"gcp_logging_sink\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_3", "description": "Enabling retention policies on log buckets will protect logs stored in cloud storage buckets from being overwritten or accidentally deleted. It is recommended to set up retention policies and configure Bucket Lock on all storage buckets that are used as log sinks.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.3", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.3 Ensure that retention policies on log buckets are configured using Bucket Lock", "run_status": 8, "run_error": "relation \"gcp_logging_sink\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_4", "description": "In order to prevent unnecessary project ownership assignments to users/service-accounts and further misuses of projects and resources, all roles/Owner assignments should be monitored. Members (users/Service-Accounts) with a role assignment to primitive role roles/Owner are project owners.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.4", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.4 Ensure log metric filter and alerts exist for project ownership assignments/changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_5", "description": "Cloud audit logging records information includes the identity of the API caller, the time of the API call, the source IP address of the API caller, the request parameters, and the response elements returned by GCP services. Cloud audit logging provides a history of GCP API calls for an account, including API calls made via the console, SDKs, command-line tools, and other GCP services.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.5", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.5 Ensure that the log metric filter and alerts exist for Audit Configuration changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_6", "description": "It is recommended that a metric filter and alarm be established for changes to Identity and Access Management (IAM) role creation, deletion and updating activities.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.6", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.6 Ensure that the log metric filter and alerts exist for Custom Role changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_7", "description": "It is recommended that a metric filter and alarm be established for Virtual Private Cloud (VPC) Network Firewall rule changes.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.7", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.7 Ensure that the log metric filter and alerts exist for VPC Network Firewall rule changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_8", "description": "It is recommended that a metric filter and alarm be established for Virtual Private Cloud (VPC) network route changes.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.8", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.8 Ensure that the log metric filter and alerts exist for VPC network route changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_9", "description": "It is recommended that a metric filter and alarm be established for Virtual Private Cloud (VPC) network changes.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.9", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.9 Ensure that the log metric filter and alerts exist for VPC network changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_10", "description": "It is recommended that a metric filter and alarm be established for Cloud Storage Bucket IAM changes.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.10", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.10 Ensure that the log metric filter and alerts exist for Cloud Storage IAM permission changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_11", "description": "It is recommended that a metric filter and alarm be established for SQL instance configuration changes.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.11", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.11 Ensure that the log metric filter and alerts exist for SQL instance configuration changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_2_12", "description": "Cloud DNS logging records the queries from the name servers within your VPC to Stackdriver. Logged queries can come from Compute Engine VMs, GKE containers, or other GCP resources provisioned within the VPC.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.12", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/DNS"}, "title": "2.12 Ensure that Cloud DNS logging is enabled for all VPC networks", "run_status": 8, "run_error": "relation \"gcp_dns_policy\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v120_3", "title": "3 Networking", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "3", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 10}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_3_1", "description": "To prevent use of default network, a project should not have a default network.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.1", "cis_level": "2", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.1 Ensure that the default network does not exist in a project", "run_status": 8, "run_error": "relation \"gcp_compute_network\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_3_2", "description": "In order to prevent use of legacy networks, a project should not have a legacy network configured.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.2", "cis_level": "1", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.2 Ensure legacy networks do not exist for a project", "run_status": 8, "run_error": "relation \"gcp_compute_network\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_3_3", "description": "Cloud Domain Name System (DNS) is a fast, reliable and cost-effective domain name system that powers millions of domains on the internet. Domain Name System Security Extensions (DNSSEC) in Cloud DNS enables domain owners to take easy steps to protect their domains against DNS hijacking and man-in-the-middle and other attacks.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.3", "cis_level": "1", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/DNS"}, "title": "3.3 Ensure that DNSSEC is enabled for Cloud DNS", "run_status": 8, "run_error": "relation \"gcp_dns_managed_zone\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_3_4", "description": "DNSSEC algorithm numbers in this registry may be used in CERT RRs. Zone signing (DNSSEC) and transaction security mechanisms (SIG(0) and TSIG) make use of particular subsets of these algorithms. The algorithm used for key signing should be a recommended one and it should be strong.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.4", "cis_level": "1", "cis_section_id": "3", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/DNS"}, "title": "3.4 Ensure that RSASHA1 is not used for the key-signing key in Cloud DNS DNSSEC", "run_status": 8, "run_error": "relation \"gcp_dns_managed_zone\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_3_5", "description": "DNSSEC algorithm numbers in this registry may be used in CERT RRs. Zone signing (DNSSEC) and transaction security mechanisms (SIG(0) and TSIG) make use of particular subsets of these algorithms. The algorithm used for key signing should be a recommended one and it should be strong.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.5", "cis_level": "1", "cis_section_id": "3", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/DNS"}, "title": "3.5 Ensure that RSASHA1 is not used for the zone-signing key in Cloud DNS DNSSEC", "run_status": 8, "run_error": "relation \"gcp_dns_managed_zone\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_3_6", "description": "GCP Firewall Rules are specific to a VPC Network. Each rule either allows or denies traffic when its conditions are met. Its conditions allow the user to specify the type of traffic, such as ports and protocols, and the source or destination of the traffic, including IP addresses, subnets, and instances.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.6", "cis_level": "2", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.6 Ensure that SSH access is restricted from the internet", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_3_7", "description": "GCP Firewall Rules are specific to a VPC Network. Each rule either allows or denies traffic when its conditions are met. Its conditions allow users to specify the type of traffic, such as ports and protocols, and the source or destination of the traffic, including IP addresses, subnets, and instances.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.7", "cis_level": "2", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.7 Ensure that RDP access is restricted from the Internet", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_3_8", "description": "Flow Logs is a feature that enables users to capture information about the IP traffic going to and from network interfaces in the organization's VPC Subnets. Once a flow log is created, the user can view and retrieve its data in Stackdriver Logging. It is recommended that Flow Logs be enabled for every business-critical VPC subnet.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.8", "cis_level": "1", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.8 Ensure that VPC Flow Logs is enabled for every subnet in a VPC Network", "run_status": 8, "run_error": "relation \"gcp_compute_subnetwork\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_3_9", "description": "Secure Sockets Layer (SSL) policies determine what port Transport Layer Security (TLS) features clients are permitted to use when connecting to load balancers.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.9", "cis_level": "1", "cis_section_id": "3", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.9 Ensure no HTTPS or SSL proxy load balancers permit SSL policies with weak cipher suites", "run_status": 8, "run_error": "relation \"gcp_compute_target_ssl_proxy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_3_10", "description": "Access to VMs should be restricted by firewall rules that allow only IAP traffic by ensuring only connections proxied by the IAP are allowed. To ensure that load balancing works correctly health checks should also be allowed.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.10", "cis_level": "2", "cis_section_id": "3", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.10 Ensure Firewall Rules for instances behind Identity Aware Proxy (IAP) only allow the traffic from Google Cloud Loadbalancer (GCLB) Health Check and Proxy Addresses", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v120_4", "title": "4 Virtual Machines", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "4", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 11}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_4_1", "description": "It is recommended to configure your instance to not use the default Compute Engine service account because it has the Editor role on the project.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.1", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.1 Ensure that instances are not configured to use the default service account", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_4_2", "description": "To support principle of least privileges and prevent potential privilege escalation it is recommended that instances are not assigned to default service account Compute Engine default service account with Scope Allow full access to all Cloud APIs.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.2", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.2 Ensure that instances are not configured to use the default service account with full access to all Cloud APIs", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_4_3", "description": "It is recommended to use Instance specific SSH key(s) instead of using common/shared project-wide SSH key(s) to access Instances.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.3", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.3 Ensure 'Block Project-wide SSH keys' is enabled for VM instances", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_4_4", "description": "Enabling OS login binds SSH certificates to IAM users and facilitates effective SSH certificate management.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.4", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.4 Ensure oslogin is enabled for a Project", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_4_5", "description": "Interacting with a serial port is often referred to as the serial console, which is similar to using a terminal window, in that input and output is entirely in text mode and there is no graphical interface or mouse support.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.5", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.5 Ensure 'Enable connecting to serial ports' is not enabled for VM Instance", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_4_6", "description": "Compute Engine instance cannot forward a packet unless the source IP address of the packet matches the IP address of the instance. Similarly, GCP won't deliver a packet whose destination IP address is different than the IP address of the instance receiving the packet. However, both capabilities are required if you want to use instances to help route packets.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.6", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.6 Ensure that IP forwarding is not enabled on Instances", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_4_7", "description": "Customer-Supplied Encryption Keys (CSEK) are a feature in Google Cloud Storage and Google Compute Engine. If you supply your own encryption keys, Google uses your key to protect the Google-generated keys used to encrypt and decrypt your data. By default, Google Compute Engine encrypts all data at rest. Compute Engine handles and manages this encryption for you without any additional actions on your part. However, if you wanted to control and manage this encryption yourself, you can provide your own encryption keys.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.7", "cis_level": "2", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.7 Ensure VM disks for critical VMs are encrypted with Customer-Supplied Encryption Keys (CSEK)", "run_status": 8, "run_error": "relation \"gcp_compute_disk\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_4_8", "description": "To defend against against advanced threats and ensure that the boot loader and firmware on your VMs are signed and untampered, it is recommended that Compute instances are launched with Shielded VM enabled.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.8", "cis_level": "2", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.8 Ensure Compute instances are launched with Shielded VM enabled", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_4_9", "description": "Compute instances should not be configured to have external IP addresses.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.9", "cis_level": "2", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.9 Ensure that Compute instances do not have public IP addresses", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_4_10", "description": "In order to maintain the highest level of security all connections to an application should be secure by default.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.10", "cis_level": "2", "cis_section_id": "4", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/AppEngine"}, "title": "4.10 Ensure that App Engine applications enforce HTTPS connections", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_4_11", "description": "Google Cloud encrypts data at-rest and in-transit, but customer data must be decrypted for processing. Confidential Computing is a breakthrough technology which encrypts data in-use—while it is being processed. Confidential Computing environments keep data encrypted in memory and elsewhere outside the central processing unit (CPU).", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.11", "cis_level": "2", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.11 Ensure that Compute instances have Confidential Computing enabled", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v120_5", "title": "5 Storage", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "5", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Storage", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 2}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_5_1", "description": "It is recommended that IAM policy on Cloud Storage bucket does not allows anonymous or public access.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "5.1", "cis_level": "1", "cis_section_id": "5", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Storage"}, "title": "5.1 Ensure that Cloud Storage bucket is not anonymously or publicly accessible", "run_status": 8, "run_error": "relation \"gcp_storage_bucket\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_5_2", "description": "It is recommended that uniform bucket-level access is enabled on Cloud Storage buckets.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "5.2", "cis_level": "2", "cis_section_id": "5", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/Storage"}, "title": "5.2 Ensure that Cloud Storage buckets have uniform bucket-level access enabled", "run_status": 8, "run_error": "relation \"gcp_storage_bucket\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v120_6", "title": "6 Cloud SQL Database Services", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "6", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 30}}, "groups": [{"group_id": "gcp_compliance.benchmark.cis_v120_6_1", "title": "6.1 MySQL Database", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "6.1", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 3}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_1_1", "description": "It is recommended to set a password for the administrative user (root by default) to prevent unauthorized access to the SQL database instances. This recommendation is applicable only for MySQL Instances. PostgreSQL does not offer any setting for No Password from the cloud console.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.1.1", "cis_level": "1", "cis_section_id": "6.1", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.1.1 Ensure that a MySQL database instance does not allow anyone to connect with administrative privileges", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_1_2", "description": "It is recommended to set skip_show_database database flag for Cloud SQL Mysql instance to on.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.1.2", "cis_level": "1", "cis_section_id": "6.1", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.1.2 Ensure 'skip_show_database' database flag for Cloud SQL Mysql instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_1_3", "description": "It is recommended to set the local_infile database flag for a Cloud SQL MySQL instance to off.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.1.3", "cis_level": "1", "cis_section_id": "6.1", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.1.3 Ensure that the 'local_infile' database flag for a Cloud SQL Mysql instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v120_6_2", "title": "6.2 PostgreSQL Database", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "6.2", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 16}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_1", "description": "Ensure that the log_checkpoints database flag for the Cloud SQL PostgreSQL instance is set to on.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.1", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.1 Ensure that the 'log_checkpoints' database flag for Cloud SQL PostgreSQL instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_2", "description": "The log_error_verbosity flag controls the verbosity/details of messages logged. Valid values are: 'TERSE', 'DEFAULT', and 'VERBOSE'.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.2", "cis_level": "2", "cis_section_id": "6.2", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.2 Ensure 'log_error_verbosity' database flag for Cloud SQL PostgreSQL instance is set to 'DEFAULT' or stricter", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_3", "description": "Enabling the log_connections setting causes each attempted connection to the server to be logged, along with successful completion of client authentication. This parameter cannot be changed after the session starts.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.3", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.3 Ensure that the 'log_connections' database flag for Cloud SQL PostgreSQL instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_4", "description": "Enabling the log_disconnections setting logs the end of each session, including the session duration.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.4", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.4 Ensure that the 'log_disconnections' database flag for Cloud SQL PostgreSQL instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_5", "description": "Enabling the log_duration setting causes the duration of each completed statement to be logged. This does not logs the text of the query and thus behaves different from the log_min_duration_statement flag. This parameter cannot be changed after session start.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.5", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.5 Ensure 'log_duration' database flag for Cloud SQL PostgreSQL instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_6", "description": "Enabling the log_lock_waits flag for a PostgreSQL instance creates a log for any session waits that take longer than the alloted deadlock_timeout time to acquire a lock.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.6", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.6 Ensure that the 'log_lock_waits' database flag for Cloud SQL PostgreSQL instance is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_7", "description": "The value of log_statement flag determined the SQL statements that are logged. Valid values are: 'none', 'ddl', 'mod', and 'all'.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.7", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.7 Ensure 'log_statement' database flag for Cloud SQL PostgreSQL instance is set appropriately", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_8", "description": "PostgreSQL logs only the IP address of the connecting hosts. The log_hostname flag controls the logging of hostnames in addition to the IP addresses logged. The performance hit is dependent on the configuration of the environment and the host name resolution setup. This parameter can only be set in the postgresql.conf file or on the server command line.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.8", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.8 Ensure 'log_hostname' database flag for Cloud SQL PostgreSQL instance is set appropriately", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_9", "description": "The PostgreSQL planner/optimizer is responsible to parse and verify the syntax of each query received by the server. If the syntax is correct a parse tree is built up else an error is generated. The log_parser_stats flag controls the inclusion of parser performance statistics in the PostgreSQL logs for each query.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.9", "cis_level": "2", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.9 Ensure 'log_parser_stats' database flag for Cloud SQL PostgreSQL instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_10", "description": "The same SQL query can be executed in multiple ways and still produce different results. The PostgreSQL planner/optimizer is responsible to create an optimal execution plan for each query. The log_planner_stats flag controls the inclusion of PostgreSQL planner performance statistics in the PostgreSQL logs for each query.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.10", "cis_level": "2", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.10 Ensure 'log_planner_stats' database flag for Cloud SQL PostgreSQL instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_11", "description": "The PostgreSQL executor is responsible to execute the plan handed over by the PostgreSQL planner. The executor processes the plan recursively to extract the required set of rows. The log_executor_stats flag controls the inclusion of PostgreSQL executor performance statistics in the PostgreSQL logs for each query.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.11", "cis_level": "2", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.11 Ensure 'log_executor_stats' database flag for Cloud SQL PostgreSQL instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_12", "description": "The log_statement_stats flag controls the inclusion of end to end performance statistics of a SQL query in the PostgreSQL logs for each query. This cannot be enabled with other module statistics (log_parser_stats, log_planner_stats, log_executor_stats).", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.12", "cis_level": "2", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.2.12 Ensure 'log_statement_stats' database flag for Cloud SQL PostgreSQL instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_13", "description": "The log_min_messages flag defines the minimum message severity level that is considered as an error statement. Messages for error statements are logged with the SQL statement. Valid values include DEBUG5, DEBUG4, DEBUG3, DEBUG2, DEBUG1, INFO, NOTICE, WARNING, ERROR, LOG, FATAL, and PANIC. Each severity level includes the subsequent levels mentioned above.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.13", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "manual", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.2.13 Ensure that the 'log_min_messages' database flag for Cloud SQL PostgreSQL instance is set appropriately", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_14", "description": "The log_min_error_statement flag defines the minimum message severity level that are considered as an error statement. Messages for error statements are logged with the SQL statement. Valid values include DEBUG5, DEBUG4, DEBUG3, DEBUG2, DEBUG1, INFO, NOTICE, WARNING, ERROR, LOG, FATAL, and PANIC. Each severity level includes the subsequent levels mentioned above. Ensure a value of ERROR or stricter is set.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.14", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.2.14 Ensure 'log_min_error_statement' database flag for Cloud SQL PostgreSQL instance is set to 'Error' or stricter", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_15", "description": "PostgreSQL can create a temporary file for actions such as sorting, hashing and temporary query results when these operations exceed work_mem. The log_temp_files flag controls logging names and the file size when it is deleted. Configuring log_temp_files to 0 causes all temporary file information to be logged, while positive values log only files whose size is greater than or equal to the specified number of kilobytes. A value of -1 disables temporary file information logging.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.15", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.2.15 Ensure that the 'log_temp_files' database flag for Cloud SQL PostgreSQL instance is set to '0'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_2_16", "description": "The log_min_duration_statement flag defines the minimum amount of execution time of a statement in milliseconds where the total duration of the statement is logged. Ensure that log_min_duration_statement is disabled, i.e., a value of -1 is set.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.16", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.2.16 Ensure that the 'log_min_duration_statement' database flag for Cloud SQL PostgreSQL instance is set to '-1' (disabled)", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v120_6_3", "title": "6.3 SQL Server", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "6.3", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/SQL", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 7}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_3_1", "description": "It is recommended to set external scripts enabled database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.1", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.1 Ensure 'external scripts enabled' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_3_2", "description": "It is recommended to set cross db ownership chaining database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.2", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.2 Ensure that the 'cross db ownership chaining' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_3_3", "description": "It is recommended to set user connections database flag for Cloud SQL SQL Server instance according organization-defined value.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.3", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.3 Ensure 'user connections' database flag for Cloud SQL SQL Server instance is set as appropriate", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_3_4", "description": "It is recommended that, user options database flag for Cloud SQL SQL Server instance should not be configured.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.4", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.4 Ensure 'user options' database flag for Cloud SQL SQL Server instance is not configured", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_3_5", "description": "It is recommended to set remote access database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.5", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.5 Ensure 'remote access' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_3_6", "description": "It is recommended to set 3625 (trace flag) database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.6", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.6 Ensure '3625 (trace flag)' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_3_7", "description": "It is recommended to set contained database authentication database flag for Cloud SQL on the SQL Server instance is set to off.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.7", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.7 Ensure that the 'contained database authentication' database flag for Cloud SQL on the SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}]}], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_4", "description": "It is recommended to enforce all incoming connections to SQL database instance to use SSL.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.4", "cis_level": "1", "cis_section_id": "6", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.4 Ensure that the Cloud SQL database instance requires all incoming connections to use SSL", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_5", "description": "Database Server should accept connections only from trusted Network(s)/IP(s) and restrict access from the world.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.5", "cis_level": "1", "cis_section_id": "6", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.5 Ensure that Cloud SQL database instances are not open to the world", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_6", "description": "It is recommended to configure Second Generation Sql instance to use private IPs instead of public IPs.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.6", "cis_level": "2", "cis_section_id": "6", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.6 Ensure that Cloud SQL database instances do not have public IPs", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_6_7", "description": "It is recommended to have all SQL database instances set to enable automated backups.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.7", "cis_level": "1", "cis_section_id": "6", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP"}, "title": "6.7 Ensure that Cloud SQL database instances are configured with automated backups", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v120_7", "title": "7 BigQuery", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "7", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/BigQuery", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 3}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_7_1", "description": "It is recommended that the IAM policy on BigQuery datasets does not allow anonymous and/or public access.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "7.1", "cis_level": "1", "cis_section_id": "7", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/BigQuery"}, "title": "7.1 Ensure that BigQuery datasets are not anonymously or publicly accessible", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_7_2", "description": "BigQuery by default encrypts the data as rest by employing Envelope Encryption using Google managed cryptographic keys. The data is encrypted using the data encryption keys and data encryption keys themselves are further encrypted using key encryption keys. This is seamless and do not require any additional input from the user. However, if you want to have greater control, Customer-managed encryption keys (CMEK) can be used as encryption key management solution for BigQuery Data Sets. If CMEK is used, the CMEK is used to encrypt the data encryption keys instead of using google-managed encryption keys.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "7.2", "cis_level": "2", "cis_section_id": "7", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/BigQuery"}, "title": "7.2 Ensure that all BigQuery Tables are encrypted with Customer-managed encryption key (CMEK)", "run_status": 8, "run_error": "relation \"gcp_bigquery_table\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v120_7_3", "description": "BigQuery by default encrypts the data as rest by employing Envelope Encryption using Google managed cryptographic keys. The data is encrypted using the data encryption keys and data encryption keys themselves are further encrypted using key encryption keys. This is seamless and do not require any additional input from the user. However, if you want to have greater control, Customer-managed encryption keys (CMEK) can be used as encryption key management solution for BigQuery Data Sets.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "7.3", "cis_level": "2", "cis_section_id": "7", "cis_type": "automated", "cis_version": "v1.2.0", "plugin": "gcp", "service": "GCP/BigQuery"}, "title": "7.3 Ensure that a Default Customer-managed encryption key (CMEK) is specified for all BigQuery Data Sets", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}]}], "controls": null}], "controls": null}