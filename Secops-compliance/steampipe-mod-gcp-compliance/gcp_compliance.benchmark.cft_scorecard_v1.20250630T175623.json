{"group_id": "root_result_group", "title": "CFT Scorecard v1", "description": "", "tags": {}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 26}}, "groups": [{"group_id": "gcp_compliance.benchmark.cft_scorecard_v1", "title": "CFT Scorecard v1", "description": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 26}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.allow_only_private_cluster", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Verify all GKE clusters are Private Clusters", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.denylist_public_users", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "plugin": "gcp", "service": "GCP/IAM", "severity": "high"}, "title": "Prevent public users from having access to resources via IAM", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.disable_gke_dashboard", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Kubernetes web UI/Dashboard is disabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.disable_gke_default_service_account", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure default Service account is not used for Project access in Kubernetes Engine clusters", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.disable_gke_legacy_abac", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Legacy Authorization is set to Disabled on Kubernetes Engine Clusters", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.disable_gke_legacy_endpoints", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Check that legacy metadata endpoints are disabled on Kubernetes clusters(disabled by default since GKE 1.12+)", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.dnssec_prevent_rsasha1_ksk", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/DNS", "severity": "high", "soc_2_2017": "true"}, "title": "Ensure that RSASHA1 is not used for key-signing key in Cloud DNS", "run_status": 8, "run_error": "relation \"gcp_dns_managed_zone\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.dnssec_prevent_rsasha1_zsk", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/DNS", "severity": "high"}, "title": "Ensure that RSASHA1 is not used for zone-signing key in Cloud DNS", "run_status": 8, "run_error": "relation \"gcp_dns_managed_zone\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_alias_ip_ranges", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Kubernetes Cluster is created with Alias IP ranges enabled", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_auto_repair", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure automatic node repair is enabled on all node pools in a GKE cluster", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_auto_upgrade", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Automatic node upgrades is enabled on Kubernetes Engine Clusters nodes", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_gke_master_authorized_networks", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Master authorized networks is set to Enabled on Kubernetes Engine Clusters", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_network_flow_logs", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high", "soc_2_2017": "true"}, "title": "Ensure VPC Flow logs is enabled for every subnet in VPC Network", "run_status": 8, "run_error": "relation \"gcp_compute_subnetwork\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.enable_network_private_google_access", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high"}, "title": "Ensure Private Google Access is enabled for all subnetworks in VPC", "run_status": 8, "run_error": "relation \"gcp_compute_subnetwork\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.gke_container_optimized_os", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Ensure Container-Optimized OS (cos) is used for Kubernetes engine clusters", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.gke_restrict_pod_traffic", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/Kubernetes", "severity": "high"}, "title": "Check that GKE clusters have a Network Policy installed", "run_status": 8, "run_error": "relation \"gcp_kubernetes_cluster\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.prevent_public_ip_cloudsql", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/SQL", "severity": "high"}, "title": "Prevent a public IP from being assigned to a Cloud SQL instance", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.require_bq_table_iam", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/BigQuery", "severity": "high", "soc_2_2017": "true"}, "title": "Check if BigQuery datasets are publicly readable", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.require_bucket_policy_only", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "hipaa": "true", "nist_800_53_rev_5": "true", "nist_csf_v10": "true", "plugin": "gcp", "service": "GCP/Storage", "severity": "high", "soc_2_2017": "true"}, "title": "Check if Cloud Storage buckets have Bucket Only Policy turned on", "run_status": 8, "run_error": "relation \"gcp_storage_bucket\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.require_ssl_sql", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/SQL", "severity": "high"}, "title": "Check if Cloud SQL instances have SSL turned on", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.restrict_firewall_rule_rdp_world_open", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "nist_800_53_rev_5": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high", "soc_2_2017": "true"}, "title": "Check for open firewall rules allowing RDP from the internet", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.restrict_firewall_rule_ssh_world_open", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "nist_800_53_rev_5": "true", "pci_dss_v321": "true", "plugin": "gcp", "service": "GCP/Compute", "severity": "high", "soc_2_2017": "true"}, "title": "Check for open firewall rules allowing SSH from the internet", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.restrict_gmail_bigquery_dataset", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "plugin": "gcp", "service": "GCP/BigQuery", "severity": "high"}, "title": "Enforce corporate domain by banning gmail.com addresses access to BigQuery datasets", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.restrict_googlegroups_bigquery_dataset", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "plugin": "gcp", "service": "GCP/BigQuery", "severity": "high"}, "title": "Enforce corporate domain by banning googlegroups.com addresses access to BigQuery datasets", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.service_versions", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "plugin": "gcp", "service": "GCP/AppEngine", "severity": "high"}, "title": "Limit the number of App Engine application versions simultaneously running or installed", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.sql_world_readable", "description": "", "severity": "", "tags": {"category": "Compliance", "cft_scorecard_v1": "true", "forseti_security_v226": "true", "nist_800_53_rev_5": "true", "plugin": "gcp", "service": "GCP/SQL", "severity": "high", "soc_2_2017": "true"}, "title": "Check if Cloud SQL instances are world readable", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}]}], "controls": null}