connection "gcp_prj_d_kms_lrrj" {
  plugin  = "gcp"
  project = "prj-d-kms-lrrj"
}

connection "gcp_prj_d_monitoring_12t5" {
  plugin  = "gcp"
  project = "prj-d-monitoring-12t5"
}

connection "gcp_prj_m_shared_base_341h" {
  plugin  = "gcp"
  project = "prj-m-shared-base-341h"
}

connection "gcp_prj_n_shared_base_0rvn" {
  plugin  = "gcp"
  project = "prj-n-shared-base-0rvn"
}

connection "gcp_prj_i_shared_base_5bz7" {
  plugin  = "gcp"
  project = "prj-i-shared-base-5bz7"
}

connection "gcp_prj_net_dns_v5u5" {
  plugin  = "gcp"
  project = "prj-net-dns-v5u5"
}

connection "gcp_prj_d_shared_base_ihza" {
  plugin  = "gcp"
  project = "prj-d-shared-base-ihza"
}

connection "gcp_prj_p_shared_base_75ol" {
  plugin  = "gcp"
  project = "prj-p-shared-base-75ol"
}

connection "gcp_prj_net_hub_base_e5g1" {
  plugin  = "gcp"
  project = "prj-net-hub-base-e5g1"
}

connection "gcp_prj_net_interconnect_eyhb" {
  plugin  = "gcp"
  project = "prj-net-interconnect-eyhb"
}

connection "gcp_prj_c_logging_sbwl" {
  plugin  = "gcp"
  project = "prj-c-logging-sbwl"
}

connection "gcp_prj_c_scc_06sn" {
  plugin  = "gcp"
  project = "prj-c-scc-06sn"
}

connection "gcp_prj_c_kms_096t" {
  plugin  = "gcp"
  project = "prj-c-kms-096t"
}

connection "gcp_prj_c_secrets_0wy6" {
  plugin  = "gcp"
  project = "prj-c-secrets-0wy6"
}

connection "gcp_prj_c_billing_logs_6bhf" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-6bhf"
}

connection "gcp_prj_b_cicd_local_60ca" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-60ca"
}

connection "gcp_prj_b_seed_e898_a7f1" {
  plugin  = "gcp"
  project = "prj-b-seed-e898-a7f1"
}

connection "gcp_prj_b_seed_b4a0_c756" {
  plugin  = "gcp"
  project = "prj-b-seed-b4a0-c756"
}

connection "gcp_prj_b_cicd_local_b8de" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-b8de"
}

connection "gcp_prj_b_seed_e007_c830" {
  plugin  = "gcp"
  project = "prj-b-seed-e007-c830"
}

connection "gcp_prj_b_cicd_local_e04e" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-e04e"
}

connection "gcp_prj_b_seed_83d0_b872" {
  plugin  = "gcp"
  project = "prj-b-seed-83d0-b872"
}

connection "gcp_prj_b_cicd_local_ecb9" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-ecb9"
}

connection "gcp_prj_i_monitoring_qs6c" {
  plugin  = "gcp"
  project = "prj-i-monitoring-qs6c"
}

connection "gcp_prj_i_secrets_j7bi" {
  plugin  = "gcp"
  project = "prj-i-secrets-j7bi"
}

connection "gcp_prj_i_kms_id8k" {
  plugin  = "gcp"
  project = "prj-i-kms-id8k"
}

connection "gcp_prj_m_secrets_352b" {
  plugin  = "gcp"
  project = "prj-m-secrets-352b"
}

connection "gcp_prj_m_monitoring_snf8" {
  plugin  = "gcp"
  project = "prj-m-monitoring-snf8"
}

connection "gcp_prj_m_kms_85sz" {
  plugin  = "gcp"
  project = "prj-m-kms-85sz"
}

connection "gcp_prj_p_kms_2wmn" {
  plugin  = "gcp"
  project = "prj-p-kms-2wmn"
}

connection "gcp_prj_p_monitoring_s1p1" {
  plugin  = "gcp"
  project = "prj-p-monitoring-s1p1"
}

connection "gcp_prj_p_secrets_alpz" {
  plugin  = "gcp"
  project = "prj-p-secrets-alpz"
}

connection "gcp_prj_n_monitoring_5pnw" {
  plugin  = "gcp"
  project = "prj-n-monitoring-5pnw"
}

connection "gcp_prj_n_secrets_eoo3" {
  plugin  = "gcp"
  project = "prj-n-secrets-eoo3"
}

connection "gcp_prj_n_kms_a7ci" {
  plugin  = "gcp"
  project = "prj-n-kms-a7ci"
}

connection "gcp_prj_d_secrets_830d" {
  plugin  = "gcp"
  project = "prj-d-secrets-830d"
}

connection "gcp_prj_d_kms_cpu1" {
  plugin  = "gcp"
  project = "prj-d-kms-cpu1"
}

connection "gcp_prj_d_monitoring_rjjs" {
  plugin  = "gcp"
  project = "prj-d-monitoring-rjjs"
}

connection "gcp_prj_c_secrets_xl0x" {
  plugin  = "gcp"
  project = "prj-c-secrets-xl0x"
}

connection "gcp_prj_c_scc_zwpy" {
  plugin  = "gcp"
  project = "prj-c-scc-zwpy"
}

connection "gcp_prj_c_logging_jyzz" {
  plugin  = "gcp"
  project = "prj-c-logging-jyzz"
}

connection "gcp_prj_c_kms_c9vw" {
  plugin  = "gcp"
  project = "prj-c-kms-c9vw"
}

connection "gcp_prj_c_billing_logs_jbhd" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-jbhd"
}

connection "gcp_prj_n_shared_base_5nrv" {
  plugin  = "gcp"
  project = "prj-n-shared-base-5nrv"
}

connection "gcp_prj_net_interconnect_ojkr" {
  plugin  = "gcp"
  project = "prj-net-interconnect-ojkr"
}

connection "gcp_prj_i_shared_base_zmnt" {
  plugin  = "gcp"
  project = "prj-i-shared-base-zmnt"
}

connection "gcp_prj_net_dns_sv5a" {
  plugin  = "gcp"
  project = "prj-net-dns-sv5a"
}

connection "gcp_prj_p_shared_base_ohke" {
  plugin  = "gcp"
  project = "prj-p-shared-base-ohke"
}

connection "gcp_prj_net_hub_base_ts71" {
  plugin  = "gcp"
  project = "prj-net-hub-base-ts71"
}

connection "gcp_prj_m_shared_base_m0m0" {
  plugin  = "gcp"
  project = "prj-m-shared-base-m0m0"
}

connection "gcp_prj_d_shared_base_jzqb" {
  plugin  = "gcp"
  project = "prj-d-shared-base-jzqb"
}

connection "gcp_prj_b_seed_53da_b542" {
  plugin  = "gcp"
  project = "prj-b-seed-53da-b542"
}

connection "gcp_prj_b_cicd_local_3b40" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-3b40"
}

connection "gcp_prj_b_seed_7c39_66c1" {
  plugin  = "gcp"
  project = "prj-b-seed-7c39-66c1"
}

connection "gcp_prj_b_cicd_local_3525" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-3525"
}

connection "gcp_prj_b_seed_46ef_0c2d" {
  plugin  = "gcp"
  project = "prj-b-seed-46ef-0c2d"
}

connection "gcp_prj_b_cicd_local_310e" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-310e"
}

connection "gcp_prj_b_cicd_local_7c8b" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-7c8b"
}

connection "gcp_prj_i_bu1_b_idn_s1_0s77" {
  plugin  = "gcp"
  project = "prj-i-bu1-b-idn-s1-0s77"
}

connection "gcp_prj_i_bu2_b_idn_s2_hv2m" {
  plugin  = "gcp"
  project = "prj-i-bu2-b-idn-s2-hv2m"
}

connection "gcp_prj_m_bu1_b_mgt_s1_6hp3" {
  plugin  = "gcp"
  project = "prj-m-bu1-b-mgt-s1-6hp3"
}

connection "gcp_prj_d_bu1_b_pub_s1_7ebr" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-pub-s1-7ebr"
}

connection "gcp_prj_d_bu2_b_app_s2_kn11" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-app-s2-kn11"
}

connection "gcp_prj_d_bu2_b_dat_s2_o0st" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-dat-s2-o0st"
}

connection "gcp_prj_d_bu1_b_dat_s1_codu" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-dat-s1-codu"
}

connection "gcp_prj_d_bu1_b_app_s1_v0kk" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-app-s1-v0kk"
}

connection "gcp_prj_d_bu2_b_pub_s2_n3cp" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-pub-s2-n3cp"
}

connection "gcp_prj_n_bu2_b_pub_s2_emb5" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-pub-s2-emb5"
}

connection "gcp_prj_n_bu1_b_dat_s1_qbtc" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-dat-s1-qbtc"
}

connection "gcp_prj_n_bu2_b_dat_s2_bfxl" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-dat-s2-bfxl"
}

connection "gcp_prj_n_bu2_b_app_s2_y11s" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-app-s2-y11s"
}

connection "gcp_prj_n_bu1_b_pub_s1_prt5" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-pub-s1-prt5"
}

connection "gcp_prj_n_bu1_b_app_s1_3fix" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-app-s1-3fix"
}

connection "gcp_prj_p_bu2_b_dat_s2_y905" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-dat-s2-y905"
}

connection "gcp_prj_p_bu1_b_app_s1_wmre" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-app-s1-wmre"
}

connection "gcp_prj_p_bu2_b_app_s2_9rtg" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-app-s2-9rtg"
}

connection "gcp_prj_p_bu2_b_pub_s2_l6m1" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-pub-s2-l6m1"
}

connection "gcp_prj_p_bu1_b_pub_s1_5hb7" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-pub-s1-5hb7"
}

connection "gcp_prj_p_bu1_b_dat_s1_py2q" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-dat-s1-py2q"
}

connection "gcp_prj_i_secrets_qarf" {
  plugin  = "gcp"
  project = "prj-i-secrets-qarf"
}

connection "gcp_prj_i_monitoring_8tar" {
  plugin  = "gcp"
  project = "prj-i-monitoring-8tar"
}

connection "gcp_prj_i_kms_smfh" {
  plugin  = "gcp"
  project = "prj-i-kms-smfh"
}

connection "gcp_prj_m_secrets_h6hm" {
  plugin  = "gcp"
  project = "prj-m-secrets-h6hm"
}

connection "gcp_prj_m_monitoring_kpr8" {
  plugin  = "gcp"
  project = "prj-m-monitoring-kpr8"
}

connection "gcp_prj_m_kms_15of" {
  plugin  = "gcp"
  project = "prj-m-kms-15of"
}

connection "gcp_prj_p_kms_05cn" {
  plugin  = "gcp"
  project = "prj-p-kms-05cn"
}

connection "gcp_prj_p_secrets_dn0a" {
  plugin  = "gcp"
  project = "prj-p-secrets-dn0a"
}

connection "gcp_prj_p_monitoring_8fta" {
  plugin  = "gcp"
  project = "prj-p-monitoring-8fta"
}

connection "gcp_prj_n_kms_ek1b" {
  plugin  = "gcp"
  project = "prj-n-kms-ek1b"
}

connection "gcp_prj_n_monitoring_ikix" {
  plugin  = "gcp"
  project = "prj-n-monitoring-ikix"
}

connection "gcp_prj_n_secrets_if51" {
  plugin  = "gcp"
  project = "prj-n-secrets-if51"
}

connection "gcp_prj_d_secrets_hgu9" {
  plugin  = "gcp"
  project = "prj-d-secrets-hgu9"
}

connection "gcp_prj_d_monitoring_d6rs" {
  plugin  = "gcp"
  project = "prj-d-monitoring-d6rs"
}

connection "gcp_prj_d_kms_bxli" {
  plugin  = "gcp"
  project = "prj-d-kms-bxli"
}

connection "gcp_prj_c_secrets_b2gp" {
  plugin  = "gcp"
  project = "prj-c-secrets-b2gp"
}

connection "gcp_prj_c_logging_a60r" {
  plugin  = "gcp"
  project = "prj-c-logging-a60r"
}

connection "gcp_prj_c_billing_logs_v2tx" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-v2tx"
}

connection "gcp_prj_c_kms_ziyb" {
  plugin  = "gcp"
  project = "prj-c-kms-ziyb"
}

connection "gcp_prj_c_scc_3vo9" {
  plugin  = "gcp"
  project = "prj-c-scc-3vo9"
}

connection "gcp_prj_net_interconnect_m64q" {
  plugin  = "gcp"
  project = "prj-net-interconnect-m64q"
}

connection "gcp_prj_p_shared_base_0kbo" {
  plugin  = "gcp"
  project = "prj-p-shared-base-0kbo"
}

connection "gcp_prj_m_shared_base_ue4h" {
  plugin  = "gcp"
  project = "prj-m-shared-base-ue4h"
}

connection "gcp_prj_net_dns_rta7" {
  plugin  = "gcp"
  project = "prj-net-dns-rta7"
}

connection "gcp_prj_d_shared_base_w9md" {
  plugin  = "gcp"
  project = "prj-d-shared-base-w9md"
}

connection "gcp_prj_net_hub_base_reyw" {
  plugin  = "gcp"
  project = "prj-net-hub-base-reyw"
}

connection "gcp_prj_n_shared_base_j2a0" {
  plugin  = "gcp"
  project = "prj-n-shared-base-j2a0"
}

connection "gcp_prj_i_shared_base_vtix" {
  plugin  = "gcp"
  project = "prj-i-shared-base-vtix"
}

connection "gcp_prj_b_seed_001c_6ad5" {
  plugin  = "gcp"
  project = "prj-b-seed-001c-6ad5"
}

connection "gcp_prj_b_cicd_local_d2e9" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-d2e9"
}

connection "gcp_prj_c_secrets_nx2r" {
  plugin  = "gcp"
  project = "prj-c-secrets-nx2r"
}

connection "gcp_prj_c_kms_y1dm" {
  plugin  = "gcp"
  project = "prj-c-kms-y1dm"
}

connection "gcp_prj_c_logging_pdh5" {
  plugin  = "gcp"
  project = "prj-c-logging-pdh5"
}

connection "gcp_prj_c_billing_logs_0mts" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-0mts"
}

connection "gcp_prj_c_scc_uakg" {
  plugin  = "gcp"
  project = "prj-c-scc-uakg"
}

connection "gcp_prj_m_shared_base_ws46" {
  plugin  = "gcp"
  project = "prj-m-shared-base-ws46"
}

connection "gcp_prj_d_shared_base_t8rg" {
  plugin  = "gcp"
  project = "prj-d-shared-base-t8rg"
}

connection "gcp_prj_i_shared_base_18wf" {
  plugin  = "gcp"
  project = "prj-i-shared-base-18wf"
}

connection "gcp_prj_p_shared_base_teez" {
  plugin  = "gcp"
  project = "prj-p-shared-base-teez"
}

connection "gcp_prj_net_hub_base_tyiu" {
  plugin  = "gcp"
  project = "prj-net-hub-base-tyiu"
}

connection "gcp_prj_n_shared_base_ebwx" {
  plugin  = "gcp"
  project = "prj-n-shared-base-ebwx"
}

connection "gcp_prj_net_dns_rpi4" {
  plugin  = "gcp"
  project = "prj-net-dns-rpi4"
}

connection "gcp_prj_net_interconnect_9pdx" {
  plugin  = "gcp"
  project = "prj-net-interconnect-9pdx"
}

connection "gcp_prj_b_seed_d823_e950" {
  plugin  = "gcp"
  project = "prj-b-seed-d823-e950"
}

connection "gcp_prj_b_cicd_local_5a37" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-5a37"
}

connection "gcp_prj_i_shared_base_etxq" {
  plugin  = "gcp"
  project = "prj-i-shared-base-etxq"
}

connection "gcp_prj_d_shared_base_64n0" {
  plugin  = "gcp"
  project = "prj-d-shared-base-64n0"
}

connection "gcp_prj_p_shared_base_h7wb" {
  plugin  = "gcp"
  project = "prj-p-shared-base-h7wb"
}

connection "gcp_prj_c_secrets_x593" {
  plugin  = "gcp"
  project = "prj-c-secrets-x593"
}

connection "gcp_prj_net_hub_base_21wb" {
  plugin  = "gcp"
  project = "prj-net-hub-base-21wb"
}

connection "gcp_prj_net_dns_7uc1" {
  plugin  = "gcp"
  project = "prj-net-dns-7uc1"
}

connection "gcp_prj_net_interconnect_xsq0" {
  plugin  = "gcp"
  project = "prj-net-interconnect-xsq0"
}

connection "gcp_prj_c_logging_sr5v" {
  plugin  = "gcp"
  project = "prj-c-logging-sr5v"
}

connection "gcp_prj_m_shared_base_ml5j" {
  plugin  = "gcp"
  project = "prj-m-shared-base-ml5j"
}

connection "gcp_prj_c_billing_logs_v90g" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-v90g"
}

connection "gcp_prj_c_scc_4fm0" {
  plugin  = "gcp"
  project = "prj-c-scc-4fm0"
}

connection "gcp_prj_c_kms_o7vd" {
  plugin  = "gcp"
  project = "prj-c-kms-o7vd"
}

connection "gcp_prj_b_seed_5545_ab14" {
  plugin  = "gcp"
  project = "prj-b-seed-5545-ab14"
}

connection "gcp_prj_b_cicd_local_337c" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-337c"
}

connection "gcp_prj_d_but1_b_dev_s1_olfv" {
  plugin  = "gcp"
  project = "prj-d-but1-b-dev-s1-olfv"
}

connection "gcp_prj_d_but2_b_dev_s2_7ky6" {
  plugin  = "gcp"
  project = "prj-d-but2-b-dev-s2-7ky6"
}

connection "gcp_prj_d_but2_b_dev_p2_qn8o" {
  plugin  = "gcp"
  project = "prj-d-but2-b-dev-p2-qn8o"
}

connection "gcp_prj_d_but2_b_dev_f2_yxe3" {
  plugin  = "gcp"
  project = "prj-d-but2-b-dev-f2-yxe3"
}

connection "gcp_prj_n_but1_b__np_s1_p5gn" {
  plugin  = "gcp"
  project = "prj-n-but1-b--np-s1-p5gn"
}

connection "gcp_prj_n_but2_b__np_s2_c4vb" {
  plugin  = "gcp"
  project = "prj-n-but2-b--np-s2-c4vb"
}

connection "gcp_prj_n_but2_b__np_p2_nhzl" {
  plugin  = "gcp"
  project = "prj-n-but2-b--np-p2-nhzl"
}

connection "gcp_prj_n_but1_b__np_p1_zl2t" {
  plugin  = "gcp"
  project = "prj-n-but1-b--np-p1-zl2t"
}

connection "gcp_prj_n_but1_b__np_f1_kxcx" {
  plugin  = "gcp"
  project = "prj-n-but1-b--np-f1-kxcx"
}

connection "gcp_prj_n_but2_b__np_f2_yeal" {
  plugin  = "gcp"
  project = "prj-n-but2-b--np-f2-yeal"
}

connection "gcp_prj_p_but1_b_rod_s1_t0r7" {
  plugin  = "gcp"
  project = "prj-p-but1-b-rod-s1-t0r7"
}

connection "gcp_prj_p_but2_b_rod_p2_2lre" {
  plugin  = "gcp"
  project = "prj-p-but2-b-rod-p2-2lre"
}

connection "gcp_prj_p_but2_b_rod_s2_ayz6" {
  plugin  = "gcp"
  project = "prj-p-but2-b-rod-s2-ayz6"
}

connection "gcp_prj_p_but1_b_rod_p1_yo1v" {
  plugin  = "gcp"
  project = "prj-p-but1-b-rod-p1-yo1v"
}

connection "gcp_prj_p_but1_b_rod_f1_2akd" {
  plugin  = "gcp"
  project = "prj-p-but1-b-rod-f1-2akd"
}

connection "gcp_prj_p_but2_b_rod_f2_ewdx" {
  plugin  = "gcp"
  project = "prj-p-but2-b-rod-f2-ewdx"
}

connection "gcp_prj_p_shared_base_750c" {
  plugin  = "gcp"
  project = "prj-p-shared-base-750c"
}

connection "gcp_prj_m_shared_base_xzrm" {
  plugin  = "gcp"
  project = "prj-m-shared-base-xzrm"
}

connection "gcp_prj_n_shared_base_znuf" {
  plugin  = "gcp"
  project = "prj-n-shared-base-znuf"
}

connection "gcp_prj_net_dns_dti7" {
  plugin  = "gcp"
  project = "prj-net-dns-dti7"
}

connection "gcp_prj_net_interconnect_57p1" {
  plugin  = "gcp"
  project = "prj-net-interconnect-57p1"
}

connection "gcp_prj_net_hub_base_79an" {
  plugin  = "gcp"
  project = "prj-net-hub-base-79an"
}

connection "gcp_prj_c_billing_logs_fz7x" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-fz7x"
}

connection "gcp_prj_c_scc_q9t2" {
  plugin  = "gcp"
  project = "prj-c-scc-q9t2"
}

connection "gcp_prj_c_kms_yolr" {
  plugin  = "gcp"
  project = "prj-c-kms-yolr"
}

connection "gcp_prj_c_secrets_gqna" {
  plugin  = "gcp"
  project = "prj-c-secrets-gqna"
}

connection "gcp_prj_c_logging_ixdm" {
  plugin  = "gcp"
  project = "prj-c-logging-ixdm"
}

connection "gcp_prj_b_seed_835d_1546" {
  plugin  = "gcp"
  project = "prj-b-seed-835d-1546"
}

connection "gcp_prj_b_cicd_local_006d" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-006d"
}

connection "gcp_prj_p_bu1env_kms_u7mc" {
  plugin  = "gcp"
  project = "prj-p-bu1env-kms-u7mc"
}

connection "gcp_prj_p_bu2env_kms_xc6t" {
  plugin  = "gcp"
  project = "prj-p-bu2env-kms-xc6t"
}

connection "gcp_shubhi_test_prj" {
  plugin  = "gcp"
  project = "shubhi-test-prj"
}

connection "gcp_prj_i_monitoring_nedf" {
  plugin  = "gcp"
  project = "prj-i-monitoring-nedf"
}

connection "gcp_prj_i_secrets_c4no" {
  plugin  = "gcp"
  project = "prj-i-secrets-c4no"
}

connection "gcp_prj_i_kms_8khw" {
  plugin  = "gcp"
  project = "prj-i-kms-8khw"
}

connection "gcp_prj_m_secrets_3dfn" {
  plugin  = "gcp"
  project = "prj-m-secrets-3dfn"
}

connection "gcp_prj_m_kms_e2a8" {
  plugin  = "gcp"
  project = "prj-m-kms-e2a8"
}

connection "gcp_prj_m_monitoring_tmlx" {
  plugin  = "gcp"
  project = "prj-m-monitoring-tmlx"
}

connection "gcp_prj_p_monitoring_s62l" {
  plugin  = "gcp"
  project = "prj-p-monitoring-s62l"
}

connection "gcp_prj_p_secrets_gt8c" {
  plugin  = "gcp"
  project = "prj-p-secrets-gt8c"
}

connection "gcp_prj_p_kms_jr2u" {
  plugin  = "gcp"
  project = "prj-p-kms-jr2u"
}

connection "gcp_prj_n_kms_rrbu" {
  plugin  = "gcp"
  project = "prj-n-kms-rrbu"
}

connection "gcp_prj_n_monitoring_2lpd" {
  plugin  = "gcp"
  project = "prj-n-monitoring-2lpd"
}

connection "gcp_prj_n_secrets_9kqy" {
  plugin  = "gcp"
  project = "prj-n-secrets-9kqy"
}

connection "gcp_prj_d_monitoring_88it" {
  plugin  = "gcp"
  project = "prj-d-monitoring-88it"
}

connection "gcp_prj_d_secrets_1x0r" {
  plugin  = "gcp"
  project = "prj-d-secrets-1x0r"
}

connection "gcp_prj_d_kms_z6ob" {
  plugin  = "gcp"
  project = "prj-d-kms-z6ob"
}

connection "gcp_prj_d_shared_base_x6jn" {
  plugin  = "gcp"
  project = "prj-d-shared-base-x6jn"
}

connection "gcp_prj_i_shared_base_r53u" {
  plugin  = "gcp"
  project = "prj-i-shared-base-r53u"
}

connection "gcp_prj_p_shared_base_zppo" {
  plugin  = "gcp"
  project = "prj-p-shared-base-zppo"
}

connection "gcp_prj_m_shared_base_rzg1" {
  plugin  = "gcp"
  project = "prj-m-shared-base-rzg1"
}

connection "gcp_prj_n_shared_base_oe7u" {
  plugin  = "gcp"
  project = "prj-n-shared-base-oe7u"
}

connection "gcp_prj_net_interconnect_aonv" {
  plugin  = "gcp"
  project = "prj-net-interconnect-aonv"
}

connection "gcp_prj_net_dns_4ljr" {
  plugin  = "gcp"
  project = "prj-net-dns-4ljr"
}

connection "gcp_prj_net_hub_base_xbqa" {
  plugin  = "gcp"
  project = "prj-net-hub-base-xbqa"
}

connection "gcp_prj_c_scc_hzzp" {
  plugin  = "gcp"
  project = "prj-c-scc-hzzp"
}

connection "gcp_prj_c_billing_logs_8tsz" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-8tsz"
}

connection "gcp_prj_c_logging_ds9u" {
  plugin  = "gcp"
  project = "prj-c-logging-ds9u"
}

connection "gcp_prj_c_kms_enjc" {
  plugin  = "gcp"
  project = "prj-c-kms-enjc"
}

connection "gcp_prj_b_seed_ddc9_4b84" {
  plugin  = "gcp"
  project = "prj-b-seed-ddc9-4b84"
}

connection "gcp_prj_b_cicd_local_2c16" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-2c16"
}

connection "gcp_prj_i_bu1_b_idn_s1_f4zt" {
  plugin  = "gcp"
  project = "prj-i-bu1-b-idn-s1-f4zt"
}

connection "gcp_prj_i_bu2_b_idn_s2_1p4g" {
  plugin  = "gcp"
  project = "prj-i-bu2-b-idn-s2-1p4g"
}

connection "gcp_prj_m_bu1_b_mgt_s1_u5z4" {
  plugin  = "gcp"
  project = "prj-m-bu1-b-mgt-s1-u5z4"
}

connection "gcp_prj_d_bu1_b_dat_s1_opyk" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-dat-s1-opyk"
}

connection "gcp_prj_d_bu1_b_app_s1_3qz3" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-app-s1-3qz3"
}

connection "gcp_prj_d_bu1_b_pub_s1_ng3l" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-pub-s1-ng3l"
}

connection "gcp_prj_d_bu2_b_app_s2_scuy" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-app-s2-scuy"
}

connection "gcp_prj_d_bu2_b_pub_s2_ecm4" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-pub-s2-ecm4"
}

connection "gcp_prj_d_bu2_b_dat_s2_acpp" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-dat-s2-acpp"
}

connection "gcp_prj_n_bu1_b_dat_s1_gsoo" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-dat-s1-gsoo"
}

connection "gcp_prj_n_bu1_b_app_s1_ed86" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-app-s1-ed86"
}

connection "gcp_prj_n_bu2_b_dat_s2_5qs6" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-dat-s2-5qs6"
}

connection "gcp_prj_n_bu2_b_pub_s2_rg1i" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-pub-s2-rg1i"
}

connection "gcp_prj_n_bu1_b_pub_s1_eokf" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-pub-s1-eokf"
}

connection "gcp_prj_n_bu2_b_app_s2_y42x" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-app-s2-y42x"
}

connection "gcp_prj_p_bu2_b_pub_s2_fyt7" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-pub-s2-fyt7"
}

connection "gcp_prj_p_bu1_b_app_s1_1ezr" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-app-s1-1ezr"
}

connection "gcp_prj_p_bu1_b_dat_s1_q8si" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-dat-s1-q8si"
}

connection "gcp_prj_p_bu2_b_dat_s2_1kcx" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-dat-s2-1kcx"
}

connection "gcp_prj_p_bu1_b_pub_s1_f8tj" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-pub-s1-f8tj"
}

connection "gcp_prj_p_bu2_b_app_s2_jrxg" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-app-s2-jrxg"
}

connection "gcp_prj_i_kms_w6ei" {
  plugin  = "gcp"
  project = "prj-i-kms-w6ei"
}

connection "gcp_prj_i_monitoring_4oby" {
  plugin  = "gcp"
  project = "prj-i-monitoring-4oby"
}

connection "gcp_prj_i_secrets_9a17" {
  plugin  = "gcp"
  project = "prj-i-secrets-9a17"
}

connection "gcp_prj_m_kms_9101" {
  plugin  = "gcp"
  project = "prj-m-kms-9101"
}

connection "gcp_prj_m_monitoring_5azq" {
  plugin  = "gcp"
  project = "prj-m-monitoring-5azq"
}

connection "gcp_prj_m_secrets_rygd" {
  plugin  = "gcp"
  project = "prj-m-secrets-rygd"
}

connection "gcp_prj_p_monitoring_ftqd" {
  plugin  = "gcp"
  project = "prj-p-monitoring-ftqd"
}

connection "gcp_prj_p_secrets_5an9" {
  plugin  = "gcp"
  project = "prj-p-secrets-5an9"
}

connection "gcp_prj_p_kms_mv3y" {
  plugin  = "gcp"
  project = "prj-p-kms-mv3y"
}

connection "gcp_prj_n_secrets_re9a" {
  plugin  = "gcp"
  project = "prj-n-secrets-re9a"
}

connection "gcp_prj_n_monitoring_lmkv" {
  plugin  = "gcp"
  project = "prj-n-monitoring-lmkv"
}

connection "gcp_prj_n_kms_8rwi" {
  plugin  = "gcp"
  project = "prj-n-kms-8rwi"
}

connection "gcp_prj_d_monitoring_n9jv" {
  plugin  = "gcp"
  project = "prj-d-monitoring-n9jv"
}

connection "gcp_prj_d_kms_3k9a" {
  plugin  = "gcp"
  project = "prj-d-kms-3k9a"
}

connection "gcp_prj_d_secrets_67yy" {
  plugin  = "gcp"
  project = "prj-d-secrets-67yy"
}

connection "gcp_prj_net_dns_xl09" {
  plugin  = "gcp"
  project = "prj-net-dns-xl09"
}

connection "gcp_prj_n_shared_base_q37n" {
  plugin  = "gcp"
  project = "prj-n-shared-base-q37n"
}

connection "gcp_prj_p_shared_base_tp4m" {
  plugin  = "gcp"
  project = "prj-p-shared-base-tp4m"
}

connection "gcp_prj_c_kms_a0bj" {
  plugin  = "gcp"
  project = "prj-c-kms-a0bj"
}

connection "gcp_prj_c_secrets_308g" {
  plugin  = "gcp"
  project = "prj-c-secrets-308g"
}

connection "gcp_prj_d_shared_base_ui45" {
  plugin  = "gcp"
  project = "prj-d-shared-base-ui45"
}

connection "gcp_prj_m_shared_base_l23e" {
  plugin  = "gcp"
  project = "prj-m-shared-base-l23e"
}

connection "gcp_prj_net_hub_base_yhyv" {
  plugin  = "gcp"
  project = "prj-net-hub-base-yhyv"
}

connection "gcp_prj_c_scc_d6ki" {
  plugin  = "gcp"
  project = "prj-c-scc-d6ki"
}

connection "gcp_prj_net_interconnect_cz6f" {
  plugin  = "gcp"
  project = "prj-net-interconnect-cz6f"
}

connection "gcp_prj_c_logging_yozr" {
  plugin  = "gcp"
  project = "prj-c-logging-yozr"
}

connection "gcp_prj_i_shared_base_5p6b" {
  plugin  = "gcp"
  project = "prj-i-shared-base-5p6b"
}

connection "gcp_prj_c_billing_logs_igh9" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-igh9"
}

connection "gcp_prj_b_seed_94c1_2c37" {
  plugin  = "gcp"
  project = "prj-b-seed-94c1-2c37"
}

connection "gcp_prj_c_secrets_2dx8" {
  plugin  = "gcp"
  project = "prj-c-secrets-2dx8"
}

connection "gcp_prj_c_kms_3wcy" {
  plugin  = "gcp"
  project = "prj-c-kms-3wcy"
}

connection "gcp_prj_c_billing_logs_j72d" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-j72d"
}

connection "gcp_prj_d_shared_base_joz8" {
  plugin  = "gcp"
  project = "prj-d-shared-base-joz8"
}

connection "gcp_prj_net_hub_base_asq0" {
  plugin  = "gcp"
  project = "prj-net-hub-base-asq0"
}

connection "gcp_prj_net_dns_vdv8" {
  plugin  = "gcp"
  project = "prj-net-dns-vdv8"
}

connection "gcp_prj_net_interconnect_xt1o" {
  plugin  = "gcp"
  project = "prj-net-interconnect-xt1o"
}

connection "gcp_prj_n_shared_base_rp8u" {
  plugin  = "gcp"
  project = "prj-n-shared-base-rp8u"
}

connection "gcp_prj_p_shared_base_7u2z" {
  plugin  = "gcp"
  project = "prj-p-shared-base-7u2z"
}

connection "gcp_prj_c_scc_ixsr" {
  plugin  = "gcp"
  project = "prj-c-scc-ixsr"
}

connection "gcp_prj_c_logging_qe62" {
  plugin  = "gcp"
  project = "prj-c-logging-qe62"
}

connection "gcp_prj_b_cicd_local_72ff" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-72ff"
}

connection "gcp_pbmm_lz_setup_20250612" {
  plugin  = "gcp"
  project = "pbmm-lz-setup-20250612"
}

connection "gcp_aashish_test_prj" {
  plugin  = "gcp"
  project = "aashish-test-prj"
}

connection "gcp_prj_opol_core_networks" {
  plugin  = "gcp"
  project = "prj-opol-core-networks"
}

connection "gcp_prj_ottpol_bootstrap_test" {
  plugin  = "gcp"
  project = "prj-ottpol-bootstrap-test"
}

connection "gcp_prj_c_sec_sentinal_np_psi" {
  plugin  = "gcp"
  project = "prj-c-sec-sentinal-np-psi"
}

connection "gcp_prj_c_host_networking_np_psi" {
  plugin  = "gcp"
  project = "prj-c-host-networking-np-psi"
}

connection "gcp_prj_c_core_networking_np_psi" {
  plugin  = "gcp"
  project = "prj-c-core-networking-np-psi"
}

connection "gcp_prj_c_sec_sentinal_p_psi" {
  plugin  = "gcp"
  project = "prj-c-sec-sentinal-p-psi"
}

connection "gcp_prj_c_host_networking_p_psi" {
  plugin  = "gcp"
  project = "prj-c-host-networking-p-psi"
}

connection "gcp_prj_c_core_networking_p_psi" {
  plugin  = "gcp"
  project = "prj-c-core-networking-p-psi"
}

connection "gcp_prj_c_api_gateway_np_psi" {
  plugin  = "gcp"
  project = "prj-c-api-gateway-np-psi"
}

connection "gcp_prj_c_observability_np_psi" {
  plugin  = "gcp"
  project = "prj-c-observability-np-psi"
}

connection "gcp_prj_c_tools_np_psi" {
  plugin  = "gcp"
  project = "prj-c-tools-np-psi"
}

connection "gcp_prj_c_api_gateway_p_psi" {
  plugin  = "gcp"
  project = "prj-c-api-gateway-p-psi"
}

connection "gcp_prj_c_observability_p_psi" {
  plugin  = "gcp"
  project = "prj-c-observability-p-psi"
}

connection "gcp_prj_c_tools_p_psi" {
  plugin  = "gcp"
  project = "prj-c-tools-p-psi"
}

connection "gcp_prj_c_billing_psi" {
  plugin  = "gcp"
  project = "prj-c-billing-psi"
}

connection "gcp_prj_c_logging_psi" {
  plugin  = "gcp"
  project = "prj-c-logging-psi"
}

connection "gcp_test_prj_for_psi_wif" {
  plugin  = "gcp"
  project = "test-prj-for-psi-wif"
}

connection "gcp_prj_foundation_psi" {
  plugin  = "gcp"
  project = "prj-foundation-psi"
}

connection "gcp_opensas_production" {
  plugin  = "gcp"
  project = "opensas-production"
}

connection "gcp_prj_c_scc_mdzj" {
  plugin  = "gcp"
  project = "prj-c-scc-mdzj"
}

connection "gcp_gen_lang_client_0721815396" {
  plugin  = "gcp"
  project = "gen-lang-client-0721815396"
}

connection "gcp_test_harjot_prj" {
  plugin  = "gcp"
  project = "test-harjot-prj"
}

connection "gcp_bansari_test" {
  plugin  = "gcp"
  project = "bansari-test"
}

connection "gcp_demologinproject_443517" {
  plugin  = "gcp"
  project = "demologinproject-443517"
}

connection "gcp_ansh_test_prj_443118" {
  plugin  = "gcp"
  project = "ansh-test-prj-443118"
}

connection "gcp_neosec_ai_solutions" {
  plugin  = "gcp"
  project = "neosec-ai-solutions"
}

connection "gcp_egg_timer_83d78" {
  plugin  = "gcp"
  project = "egg-timer-83d78"
}

connection "gcp_kubeshot_autogpt" {
  plugin  = "gcp"
  project = "kubeshot-autogpt"
}

connection "gcp_vratant_test_prj" {
  plugin  = "gcp"
  project = "vratant-test-prj"
}

connection "gcp_ansh_test_prj" {
  plugin  = "gcp"
  project = "ansh-test-prj"
}

connection "gcp_test_ai_project_435023" {
  plugin  = "gcp"
  project = "test-ai-project-435023"
}

connection "gcp_newagent_fvne" {
  plugin  = "gcp"
  project = "newagent-fvne"
}

connection "gcp_kubeshot_rag" {
  plugin  = "gcp"
  project = "kubeshot-rag"
}

connection "gcp_scrapper_419215" {
  plugin  = "gcp"
  project = "scrapper-419215"
}

connection "gcp_bucket_rsync" {
  plugin  = "gcp"
  project = "bucket-rsync"
}

connection "gcp_backstage_412018" {
  plugin  = "gcp"
  project = "backstage-412018"
}

connection "gcp_sys_16473596295331132258055880" {
  plugin  = "gcp"
  project = "sys-16473596295331132258055880"
}

connection "gcp_sys_77432550242619391028652600" {
  plugin  = "gcp"
  project = "sys-77432550242619391028652600"
}

connection "gcp_sys_16577995987289835251666163" {
  plugin  = "gcp"
  project = "sys-16577995987289835251666163"
}

connection "gcp_sys_51815433874226388266640321" {
  plugin  = "gcp"
  project = "sys-51815433874226388266640321"
}

connection "gcp_sys_10584336580509349754235258" {
  plugin  = "gcp"
  project = "sys-10584336580509349754235258"
}

connection "gcp_sys_57733743513041139174379021" {
  plugin  = "gcp"
  project = "sys-57733743513041139174379021"
}

connection "gcp_sys_47687987468820982859752416" {
  plugin  = "gcp"
  project = "sys-47687987468820982859752416"
}

connection "gcp_sys_49349059182320262405653747" {
  plugin  = "gcp"
  project = "sys-49349059182320262405653747"
}

connection "gcp_sys_18228428281390614574756768" {
  plugin  = "gcp"
  project = "sys-18228428281390614574756768"
}

connection "gcp_sys_83396747445816645808979860" {
  plugin  = "gcp"
  project = "sys-83396747445816645808979860"
}

connection "gcp_sys_25365120900523877734101376" {
  plugin  = "gcp"
  project = "sys-25365120900523877734101376"
}

connection "gcp_kubeshot_devops" {
  plugin  = "gcp"
  project = "kubeshot-devops"
}

connection "gcp_kubeshot_admin" {
  plugin  = "gcp"
  project = "kubeshot-admin"
}

connection "gcp" {
  plugin = "gcp"
  type        = "aggregator"
  connections = ["gcp_*"]
}