## Description

Keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to use standard authentication flow instead.

Security risks involved in using API-Keys appear below:

- API keys are simple encrypted strings
- API keys do not identify the user or the application making the API request
- API keys are typically accessible to clients, making it easy to discover and steal an API key

To avoid the security risk in using API keys, it is recommended to use standard authentication flow instead.

## Remediation

### From Console

To find the listed API Keys

1. Login to [APIs & Services\Credentials](https://console.cloud.google.com/apis/credentials)
2. Navigate to the section called `API Keys` to check if the Keys are listed.
3. To delete API Keys: Click the Delete Bin Icon in front of every API Key Name.

**Note** Deleting an API key will break dependent applications (if any).
