## Description

Google Cloud Functions allow you to host serverless code that is executed when an event is triggered, without the requiring the management a host operating system. These functions can also store environment variables to be used by the code that may contain authentication or other information that needs to remain confidential.

It is recommended to use the Secret Manager, because environment variables are stored unencrypted, and accessible for all users who have access to the code.

## Remediation

Enable the Secret Manager API for the GCP Project.

### From Console

1. Within the project you wish to enable, select the Navigation hamburger menu in the top left. Hover over 'APIs & Services' to under the heading 'Serverless', then select 'Enabled APIs & Services' in the menu that opens up.
2. Click the button '+ Enable APIS and Services'
3. In the Search bar, search for 'Secret Manager API' and select it.
4. Click the blue box that says 'Enable'.