## Description

Flow Logs is a feature that enables users to capture information about the IP traffic going to and from network interfaces in the organization's VPC Subnets. Once a flow log is created, the user can view and retrieve its data in `Stackdriver` Logging. It is recommended that `Flow Logs` be enabled for every business-critical VPC subnet.

VPC networks and subnetworks not reserved for internal HTTP(S) load balancing provide logically isolated and secure network partitions where GCP resources can be launched. When Flow Logs are enabled for a subnet, VMs within that subnet start reporting on all Transmission Control Protocol (TCP) and User Datagram Protocol (UDP) flows. Each VM samples the TCP and UDP flows it sees, inbound and outbound, whether the flow is to or from another VM, a host in the on-premises datacenter, a Google service, or a host on the Internet. If two GCP VMs are communicating, and both are in subnets that have VPC Flow Logs enabled, both VMs report the flows.

Flow Logs provide visibility into network traffic for each VM inside the subnet and can be used to detect anomalous traffic or provide insight during security workflows.

## Remediation

### From Console

1. Login to [VPC Network](https://console.cloud.google.com/networking/networks/list).
2. Click the name of a `subnet`, The Subnet details page displays.
3. Click the `EDIT` button.
4. Set `Flow Logs` to **On**.
5. Expand the `Configure Logs` section.
6. Set `Aggregation Interval` to `5 SEC`.
7. Check the box beside `Include metadata`.
8. Set `Sample rate` to `100`.
9. Click Save.

### From Command Line

To set Private Google access for a network subnet, run the following command:

```bash
gcloud compute networks subnets update [SUBNET_NAME] --region [REGION] --enable-flow-logs --logging-aggregation-interval=interval-5-sec --logging-flow-sampling=1 --logging-metadata=include-all
```
