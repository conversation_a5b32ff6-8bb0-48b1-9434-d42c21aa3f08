## Description

Service Account keys consist of a key ID (Private_key_Id) and Private key, which are used to
sign programmatic requests users make to Google cloud services accessible to that particular service account. It is recommended that all Service Account keys are regularly
rotated.

Rotating Service Account keys will reduce the window of opportunity for an access key that is associated with a compromised or terminated account to be used. Service Account keys should be rotated to ensure that data cannot be accessed with an old key that might have been lost, cracked, or stolen.

Each service account is associated with a key pair managed by Google Cloud Platform (GCP). It is used for service-to-service authentication within GCP. Google rotates the keys daily.

## Remediation

Delete any external (user-managed) Service Account Key older than 90 days.

### From Console

1. Go to GCP console at [APIs & Services\Credentials](https://console.cloud.google.com/apis/credentials)
2. Navigate to section `Service Accounts`, select every external (user-managed) Service Account.
3. Check the key creation date is greater than or equal to the past 90 days.
4. Click three dots in `Actions` column.
5. Select **Menage Keys** and click Icon **Bin Icon** to delete `Service Account` key
6. Create a new external (user-managed) Service Account Key for a Service Account. See details [here](https://cloud.google.com/iam/docs/creating-managing-service-account-keys)
