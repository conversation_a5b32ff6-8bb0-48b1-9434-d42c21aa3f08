## Description

Compute Engine instance cannot forward a packet unless the source IP address of the packet matches the IP address of the instance. Similarly, GCP won't deliver a packet whose destination IP address is different than the IP address of the instance receiving the packet. However, both capabilities are required if you want to use instances to help route packets.

Forwarding of data packets should be disabled to prevent data loss or information disclosure.

Compute Engine instance cannot forward a packet unless the source IP address of the packet matches the IP address of the instance. Similarly, GCP won't deliver a packet whose destination IP address is different than the IP address of the instance receiving the packet. However, both capabilities are required if you want to use instances to help route packets. To enable this source and destination IP check, disable the canIpForward field, which allows an instance to send and receive packets with non-matching destination or source IPs.

## Remediation

You only edit the `canIpForward` setting at instance creation time. Therefore, you need to delete the instance and create a new one where canIpForward is set to false.

### From Console

1. Login to GCP VM [Console](https://console.cloud.google.com/compute/instances).
2. Select the instance you want to remediate and click **Delete**.
3. On the 'VM Instances' page, click **CREATE INSTANCE**.
4. Create a new instance with the desired configuration. By default, the instance is configured to not allow IP forwarding.

**Note:** By default, instances are not configured to allow IP forwarding.
