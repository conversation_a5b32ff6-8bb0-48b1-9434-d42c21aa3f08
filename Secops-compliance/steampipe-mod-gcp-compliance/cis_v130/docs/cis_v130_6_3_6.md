## Description

It is recommended to set `3625 (trace flag)` database flag for Cloud SQL SQL Server instance to `off`.

Trace flags are frequently used to diagnose performance issues or to debug stored procedures or complex computer systems, but they may also be recommended by
Microsoft Support to address behavior that is negatively impacting a specific workload.

All documented trace flags and those recommended by Microsoft Support are fully supported in a production environment when used as directed. `3625(trace log) `Limits the amount of information returned to users who are not members of the sysadmin fixed server role, by masking the parameters of some error messages using '******'. This can help prevent disclosure of sensitive information, hence this is recommended to disable this flag.

## Remediation

This recommendation is applicable to SQL Server database instances.

### From Console

1. Go to the Cloud SQL Instances page in the Google Cloud Console by visiting https://console.cloud.google.com/sql/instances.
2. Select the SQL Server instance for which you want to enable to database flag.
3. Click `Edit`.
4. Scroll down to the `Flags` section.
5. To set a flag that has not been set on the instance before, click `Add item`, choose the flag `3625` from the drop-down menu, and set its value to `off`.
6. Click `Save` to save your changes.
7. Confirm your changes under `Flags` on the Overview page.

**Note:** Any changes to the database flag values, may require your instance to be restarted

### From Command Line

1. List all Cloud SQL database Instances

```bash
gcloud sql instances list
```

2. Configure the required database flag for every Cloud SQL SQL Server database instance using the below command.

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags "3625=off"
```

**Note:** This command will overwrite all database flags previously set. To keep those and add new ones, include the values for all flags you want set on the instance; any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
