## Description

Unrestricted keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to restrict API key usage to trusted hosts, HTTP referrers and apps.

Security risks involved in using API-Keys appear below:

- API keys are simple encrypted strings
- API keys do not identify the user or the application making the API request
- API keys are typically accessible to clients, making it easy to discover and steal an API key

In light of these potential risks, Google recommends using the standard authentication flow instead of API keys. However, there are limited cases where API keys are more appropriate. For example, if there is a mobile application that needs to use the Google Cloud Translation API, but doesn't otherwise need a backend server, API keys are the simplest way to authenticate to that API.

## Remediation

### From Console

To find the listed API Keys with specified `Hosts` access and `Apps`

1. Login to [APIs & Services\Credentials](https://console.cloud.google.com/apis/credentials)
2. In the section `API Keys`, Click the `API Key Name`. The API Key properties display on a new page.
3. For every `API Key`, ensure below

- `Application restrictions` is not set to **None**.
- `API restrictions` is not set to **Don't restrict key**

Alternatively you can set values as example:

- Ensure `Application restrictions` is set to `HTTP referrers` and the referrer is not set to wild-cards `(* or *.[TLD] or *.[TLD]/*)` allowing access to any/wide HTTP referrer(s)

Or,

- Ensure `Application restrictions` is set to IP addresses and referrer *is not set* to any host `(0.0.0.0 or 0.0.0.0/0 or ::0)`

**Note** By default, Application Restrictions are set to None.
