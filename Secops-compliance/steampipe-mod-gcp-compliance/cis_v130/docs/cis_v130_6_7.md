## Description

It is recommended to have all SQL database instances set to enable automated backups.

Backups provide a way to restore a Cloud SQL instance to recover lost data or recover from a problem with that instance. Automated backups need to be set for any instance that contains data that should be protected from loss or damage.

## Remediation

This recommendation is applicable for SQL Server, PostgreSQL, MySQL generation 1 and MySQL generation 2 instances.

### From Console

1. <PERSON>gin in to [Cloud SQL Instances](https://console.cloud.google.com/sql/instances)
2. Select the instance where the backups need to be configured.
3. Click Edit.
4. In the `Backups` section, check `Enable automated backups`, and choose a backup window.
5. Click Save

### From Command Line

1. List all Cloud SQL database instances using the following command:

```bash
gcloud sql instances list
```

2. Enable Automated backups for every Cloud SQL database instance using the below command:

```sql
gcloud sql instances patch INSTANCE_NAME --backup-start-time [HH:MM]
```

**Note:** By default, automated backups are not configured for Cloud SQL instances. Data backup is not possible on any Cloud SQL instance unless Automated Backup is configured.
