## Description

It is recommended that the IAM policy on Cloud KMS `cryptokeys` should restrict anonymous and/or public access.

Granting permissions to `allUsers` or `allAuthenticatedUsers` allows anyone to access the dataset. Such access might not be desirable if sensitive data is stored at the location. In this case, ensure that anonymous and/or public access to a Cloud KMS `cryptokey` is not allowed.

## Remediation

### From Command Line

1. List all Cloud KMS Cryptokeys.
  ```bash
  gcloud kms keys list --keyring=[key_ring_name] --location=global --
  format=json | jq '.[].name'
  ```
2. Remove IAM policy binding for a KMS key to remove access to `allUsers` and `allAuthenticatedUsers` using the below command.
  ```bash
  gcloud kms keys remove-iam-policy-binding [key_name] --keyring=[key_ring_name] --location=global --member='allAuthenticatedUsers' --

  role='[role]' gcloud kms keys remove-iam-policy-binding [key_name] -- keyring=[key_ring_name] --location=global --member='allUsers' --role='[role]'
  ```

**Note**: By default Cloud KMS does not allow access to `allUsers` or `allAuthenticatedUsers`.
