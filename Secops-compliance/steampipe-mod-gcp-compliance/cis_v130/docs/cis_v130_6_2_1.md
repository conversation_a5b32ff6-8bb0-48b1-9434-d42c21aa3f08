## Description

The log_error_verbosity flag controls the verbosity/details of messages logged. Valid values are:

* TERSE
* DEFAULT
* VERBOSE

TERSE excludes the logging of DETAIL, HINT, QUERY, and CONTEXT error information.

VERBOSE output includes the SQLSTATE error code, source code file name, function name, and line number that generated the error.

Ensure an appropriate value is set to 'DEFAULT' or stricter.

Auditing helps in troubleshooting operational problems and also permits forensic analysis. If log_error_verbosity is not set to the correct value, too many details or too few details may be logged. This flag should be configured with a value of 'DEFAULT' or stricter. This recommendation is applicable to PostgreSQL database instances.

## Remediation

### From Console:

1. Go to the Cloud SQL Instances page in the Google Cloud Console by visiting https://console.cloud.google.com/sql/instances.
2. Select the PostgreSQL instance for which you want to enable the database flag.
3. Click `Edit`.
4. Scroll down to the `Flags` section.
5. To set a flag that has not been set on the instance before, click `Add item`, choose the flag `log_error_verbosity` from the drop-down menu and set appropriate value.
6. Click `Save` to save your changes.
7. Confirm your changes under `Flags` on the Overview page.

### From Command Line:

1. Configure the log_error_verbosity database flag for every Cloud SQL PosgreSQL database instance using the below command.

```bash
gcloud sql instances patch <INSTANCE_NAME> --database-flags log_error_verbosity=<TERSE|DEFAULT|VERBOSE>
```

**Note**: This command will overwrite all database flags previously set. To keep those and add new ones, include the values for all flags you want set on the instance; any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").