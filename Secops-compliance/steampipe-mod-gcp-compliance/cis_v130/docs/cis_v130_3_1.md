## Description

To prevent use of `default` network, a project should not have a default network.

The `default` network has a preconfigured network configuration and automatically generates the following insecure firewall rules:

- default-allow-internal: Allows ingress connections for all protocols and ports among instances in the network.
- default-allow-ssh: Allows ingress connections on TCP port 22(SSH) from any source to any instance in the network.
- default-allow-rdp: Allows ingress connections on TCP port 3389(RDP) from any source to any instance in the network.
- default-allow-icmp: Allows ingress ICMP traffic from any source to any instance in the network.

These automatically created firewall rules do not get audit logged and cannot be configured to enable firewall rule logging.

Additionally, the default network is an auto mode network, which means that its subnets use the same predefined range of IP addresses, and as a result, it's not possible to use Cloud VPN or VPC Network Peering with the default network. The organization should create a new network based on the requirement and `delete` the `default` network.

## Remediation

### From Console

1. Login to the [VPC networks](https://console.cloud.google.com/networking/networks/list)
2. Click the network named **default**.
3. On the network detail page, click EDIT.
4. Click *DELETE VPC NETWORK* from the top section of the page.
5. If needed, create a new network to replace the default network.

### From Command Line

1. Delete the default network:

```bash
gcloud compute networks delete default
```

2. If needed create new network:

```bash
gcloud compute networks create `NETWORK_NAME`
```
