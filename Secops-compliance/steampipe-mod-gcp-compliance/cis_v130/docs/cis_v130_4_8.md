## Description

To defend against against advanced threats and ensure that the boot loader and firmware on your VMs are signed and `untampered`, it is recommended that Compute instances are launched with Shielded VM enabled.

Shielded VMs are virtual machines (VMs) on Google Cloud Platform hardened by a set of security controls that help defend against rootkits and bootkits.

Integrity monitoring helps you understand and make decisions about the state of your VM instances and the Shielded VM vTPM enables Measured Boot by performing the measurements needed to create a known good boot baseline, called the integrity policy baseline. The integrity policy baseline is used for comparison with measurements from subsequent VM boots to determine if anything has changed.

## Remediation

To be able turn on Shielded VM on an instance, your instance must use an image with Shielded VM support.

### From Console

1. Login to [VM instances](https://console.cloud.google.com/compute/instances).
2. Click on the instance name to see its VM instance details page.
3. Click **STOP** to stop the instance.
4. When the instance has stopped, click **EDIT**.
5. In the `Shielded VM` section, select Turn on `vTPM` and Turn on `Integrity Monitoring`.
6. Optionally, if you do not use any custom or unsigned drivers on the instance, also select Turn on Secure Boot.
7. Click the Save button to modify the instance and then click **START** to restart it.

### From Command Line

You can only enable Shielded VM options on instances that have Shielded VM support. For a list of Shielded VM public images, run the gcloud compute images list command with the following flags:

1. Stop the instance:

```bash
gcloud compute instances stop INSTANCE_NAME
```

2. Update the instance:

```bash
gcloud compute instances update INSTANCE_NAME --shielded-vtpm --shielded-vmintegrity-monitoring
```

3. Optionally, if you do not use any custom or unsigned drivers on the instance, also turn on secure boot.

```bash
gcloud compute instances update INSTANCE_NAME --shielded-vm-secure-boot
```

4. Restart the instance:

```bash
gcloud compute instances start INSTANCE_NAME
```
