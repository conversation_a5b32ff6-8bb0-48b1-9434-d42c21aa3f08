## Description

Enabling OS login binds SSH certificates to IAM users and facilitates effective SSH certificate management.

Enabling osLogin ensures that SSH keys used to connect to instances are mapped with IAM users. Revoking access to IAM user will revoke all the SSH keys associated with that particular user. It facilitates centralized and automated SSH key pair management which is useful in handling cases like response to compromised SSH key pairs and/or revocation of external/third-party/Vendor users.

**Note:** By default, parameter `enable-oslogin`is not set, which is equivalent to setting it to `FALSE`.

## Remediation

### From Console

1. Login to GCP VM [Console](https://console.cloud.google.com/compute/instances).
2. Select the instance and click **Edit**.
3. Navigate to **Metadata** section in the left panel.
4. Add a metadata entry where the key is `enable-oslogin` and the value is `TRUE`.
5. Click Save to apply the changes.
6. Ensure that no instance in the project overrides the project setting.
7. See more detail [here](https://cloud.google.com/compute/docs/instances/managing-instance-access#enable_oslogin)

### From Command Line

1. Configure `oslogin` on the project

```bash
gcloud compute project-info add-metadata --metadata enable-oslogin=TRUE
```

2. Remove instance metadata that overrides the project setting.

```bash
gcloud compute instances remove-metadata INSTANCE_NAME --keys=enable-oslogin
```
