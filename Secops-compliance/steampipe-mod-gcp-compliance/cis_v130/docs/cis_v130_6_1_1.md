## Description

It is recommended to set a password for the administrative *user (root by default) to prevent unauthorized access to the SQL database instances.
This recommendation is applicable only for *MySQL Instances*. PostgreSQL does not offer any setting for No Password from the cloud console.

At the time of MySQL Instance creation, not providing an administrative password allows anyone to connect to the SQL database instance with administrative privileges. The root password should be set to ensure only authorized users have these privileges.

## Remediation

### From Console:

1. <PERSON><PERSON> to GCP console and navigate to [Cloud SQL Instances](https://console.cloud.google.com/sql/).
2. Select the instance to open its Overview page.
3. From left navigation pane, select `Users`.
4. Click the `3-dots` icon for the user to be updated.
5. Select `Change password`, specify a `new password`, and click **OK**.

### From Command Line:

1. Set a password to a MySql instance:

```bash
gcloud sql users set-password [USER_NAME] [HOST] --instance=[INSTANCE_NAME] - -password=[PASSWORD]
```

2. A prompt will appear, requiring the user to enter a password:

```bash
Instance Password:
```

3. With a successful password configured, the following message should be seen:
```bash
Updating Cloud SQL user...done.
```
