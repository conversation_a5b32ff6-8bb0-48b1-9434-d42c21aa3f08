## Description

Google Cloud encrypts data at-rest and in-transit, but customer data must be decrypted for processing. Confidential Computing is a breakthrough technology which encrypts data in-use while it is being processed. Confidential Computing environments keep data encrypted in memory and elsewhere outside the central processing unit (CPU).

Confidential Computing enables customers' sensitive code and other data encrypted in memory during processing. Google does not have access to the encryption keys. Confidential VM can help alleviate concerns about risk related to either dependency on Google infrastructure or Google insiders' access to customer data in the clear.

## Remediation

Confidential Computing can only be enabled when an instance is created. You must delete the current instance and create a new one.

### From Console

1. Login to [VM instances](https://console.cloud.google.com/compute/instances)
2. Click **CREATE INSTANCE**.
3. Fill out the desired configuration for your instance.
4. Under the `Confidential VM service` section, check the option Enable the Confidential Computing service on this VM instance.
5. Click **Create**.

### From Command Line

Create a new instance with Confidential Compute enabled.

```bash
gcloud beta compute instances create INSTANCE_NAME --zone ZONE --confidential-compute --maintenance-policy=TERMINATE
```
