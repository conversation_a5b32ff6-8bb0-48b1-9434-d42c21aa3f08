## Description

It is recommended to set `remote access` database flag for Cloud SQL SQL Server instance to `off`.

The remote access option controls the execution of stored procedures from local or remote servers on which instances of SQL Server are running. This default value for this option is 1. This grants permission to run local stored procedures from remote servers or remote stored procedures from the local server.

To prevent local stored procedures from being run from a remote server or remote stored procedures from being run on the local server, this must be disabled. The Remote Access option controls the execution of local stored procedures on remote servers or remote stored procedures on local server. 'Remote access' functionality can be abused to launch a *Denial-of-Service (DoS)* attack on remote servers by off-loading query processing to a target, hence this should be disabled.

This recommendation is applicable to SQL Server database instances.

### From Console

1. Go to the Cloud SQL Instances page in the Google Cloud Console by visiting https://console.cloud.google.com/sql/instances.
2. Select the SQL Server instance for which you want to enable to database flag.
3. Click `Edit`.
4. Scroll down to the `Flags` section.
5. To set a flag that has not been set on the instance before, click `Add item`, choose the flag `remote access` from the drop-down menu, and set its value to `off`.
6. Click `Save` to save your changes.
7. Confirm your changes under `Flags` on the Overview page.

**Note:** Any changes to the database flag values, may require your instance to be restarted

### From Command Line

1. List all Cloud SQL database Instances

```bash
gcloud sql instances list
```

2. Configure the required database flag for every Cloud SQL SQL Server database instance using the below command.

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags "remote access=off"
```

**Note:** This command will overwrite all database flags previously set. To keep those and add new ones, include the values for all flags you want set on the instance; any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
