## Description

It is recommended to enforce all incoming connections to SQL database instance to use `SSL`.

SQL database connections if successfully trapped (MITM); can reveal sensitive data like credentials, database queries, query outputs etc. For security, it is recommended to always use SSL encryption when connecting to your instance.

## Remediation

This recommendation is applicable for PostgreSQL, MySQL generation 1, MySQL generation 2 and SQL Server 2017 instances.

### From Console

1. Login in to [Cloud SQL Instances](https://console.cloud.google.com/sql/instances)
2. Click on an instance name to see its configuration overview.
3. In the left-side panel, select `Connections`.
4. In the `SSL encryption` section, click **Allow only SSL connections**.
5. Under `Manage server certificates` click **Create new certificate**.
6. Under `Manage client certificates` click **Create a client certificate**.
7. Follow the [instructions](https://cloud.google.com/sql/docs/postgres/configure-ssl-instance) shown to learn how to connect to your instance.

### From Command Line

1. To enforce SSL encryption for an instance run the command:

```bash
gcloud sql instances patch INSTANCE_NAME --require-ssl
```
