## Description

To support principle of least privileges and prevent potential privilege escalation it is recommended that instances are not assigned to default service account `Compute Engine default service account` with <PERSON><PERSON> `Allow full access to all Cloud APIs`.

Along with ability to optionally create, manage and use user managed custom service accounts, Google Compute Engine provides default service account `Compute Engine default service account` for an instances to access necessary cloud services. `Project Editor role` is assigned to `Compute Engine default service account` hence, This service account has almost all capabilities over all cloud services except billing. However, when Compute Engine default service account assigned to an instance it can operate in 3 scopes.

```
1. Allow default access: Allows only minimum access required to run an Instance (Least Privileges)
2. Allow full access to all Cloud APIs: Allow full access to all the cloud APIs/Services (Too much access)
3. Set access for each API: Allows Instance administrator to choose only those APIs that are needed to perform specific business functionality
expected by instance
```

When an instance is configured with Compute Engine default service account with Scope Allow full access to all Cloud APIs, based on IAM roles assigned to the user(s) accessing Instance, it may allow user to perform cloud operations/API calls that user is not supposed to perform leading to successful privilege escalation.

## Remediation

### From Console

1. Login to GCP VM [Console](https://console.cloud.google.com/compute/instances).
2. Click on the instance name to go to its VM instance details page.
3. If the instance is not stopped, click the **Stop** button. Wait for the instance to be stopped.
4. In VM instance details page, navigate to section `Service Account`, ensure that the default Compute Engine service account is not used.
5. Also ensure that, with different service account `Allow full access to all Cloud APIs` is not selected.

### From Command Line

1. List Instances from project

```bash
gcloud compute instances list
```

2. Get the details on each instance:

```bash
gcloud compute instances describe INSTANCE_NAME --zone ZONE
```

3. Ensure that the instance is not configured to allow the https://www.googleapis.com/auth/cloud-platform scope for the default Compute Engine service account in it's scope

```
serviceAccounts:
- email: [PROJECT_NUMBER]-<EMAIL>
 scopes:
 - https://www.googleapis.com/auth/cloud-platform
 ```

**Note:** Instances created by GKE should be excluded. These instances have names that start with "gke-" and are labeled "goog-gke-node"
