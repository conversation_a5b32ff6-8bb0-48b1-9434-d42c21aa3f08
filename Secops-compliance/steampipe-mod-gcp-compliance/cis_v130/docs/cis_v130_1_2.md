## Description

Setup multi-factor authentication for Google Cloud Platform accounts.

Multi-factor authentication requires more than one mechanism to authenticate a user. This secures user logins from attackers exploiting stolen or weak credentials. By default, multi-factor authentication is not set.

## Remediation

For each Google Cloud Platform project, folder, or organization
  - Identify non-service accounts.
  - Manually verify that multi-factor authentication for each account is set.

Refer [here](https://support.google.com/accounts/answer/6103523) for more details.