## Description

A service account is a special Google account that belongs to an application or a VM, instead of to an individual end-user. The application uses the service account to call the service's Google API so that users aren't directly involved. It's recommended not to use admin access
for ServiceAccount.

Service accounts represent service-level security of the Resources (application or a VM) which can be determined by the roles assigned to it. Enrolling ServiceAccount with Admin rights gives full access to an assigned application or a VM. A ServiceAccount Access holder can perform critical actions like delete, update change settings, etc. without user intervention. For this reason, it's recommended that service accounts not have Admin
rights.

## Remediation

### From Console

1. Go to [IAM & admin/IAM console](https://console.cloud.google.com/iam-admin/iam)
2. Go to the `Members`
3. Identify User-Managed user created service account with roles containing `*Admin` or `*admin` or role matching `Editor` or role matching `Owner`
4. Click the `pencil` at the end of the service account row, click on **Delete** bin icon to remove the role from the member (service account in this case)

### From Command Line

1. Get the associated IAM policy
  ```bash
  gcloud projects get-iam-policy PROJECT_ID --format json > iam.json
  ```
2. Using a text editor, Remove Role which contains _roles/*Admin_ or _roles/*admin_ or matched _roles/editor_ or matches _roles/owner_. Add a role to the bindings array that defines the group members and the role for those members.
3. Update the project's IAM policy
  ```bash
  gcloud projects set-iam-policy PROJECT_ID iam.json
  ```