## Description

It is recommended to use Instance specific SSH key(s) instead of using common/shared project-wide SSH key(s) to access Instances.

Project-wide SSH keys are stored in Compute/Project-meta-data. Project wide SSH keys can be used to login into all the instances within project. Using project-wide SSH keys eases the SSH key management but if compromised, poses the security risk which can impact all the instances within project. It is recommended to use Instance specific SSH keys which can limit the attack surface if the SSH keys are compromised.

In addition, if OS Login is enabled, SSH keys in instance metadata are ignored, and therefore blocking project-wide SSH keys is not necessary.

## Remediation

### From Console

1. Login to GCP VM [Console](https://console.cloud.google.com/compute/instances).
2. Click on the instance name to go to its VM instance details page.
3. Navigate to `SSH Keys` section to check if **Block project-wide SSH keys** is checked or not.
4. If not, to block users with `project-wide SSH` keys from connecting to this instance, select **Block project-wide SSH keys**.
5. Click Save at the bottom of the page.
6. Repeat steps for every impacted Instance.
