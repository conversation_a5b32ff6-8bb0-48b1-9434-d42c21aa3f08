## Description

It is recommended to set user connections database flag for Cloud SQL SQL Server instance according organization-defined value.

The `user connections` option specifies the maximum number of simultaneous user connections that are allowed on an instance of SQL Server. The actual number of user
connections allowed also depends on the version of SQL Server that you are using, and also the limits of your application or applications and hardware. SQL Server allows a maximum of 32,767 user connections.

Because user connections is a dynamic (self-configuring) option, SQL Server adjusts the maximum number of user connections automatically as needed, up to the maximum value allowable. For example, if only 10 users are logged in, 10 user connection objects are allocated. In most cases, you do not have to change the value for this option. The default is 0, which means that the maximum (32,767) user connections
are allowed.

## Remediation

This recommendation is applicable to SQL Server database instances.

### From Console

1. Go to the Cloud SQL Instances page in the Google Cloud Console by visiting https://console.cloud.google.com/sql/instances.
2. Select the SQL Server instance for which you want to enable to database flag.
3. Click `Edit`.
4. Scroll down to the `Flags` section.
5. To set a flag that has not been set on the instance before, click `Add item`, choose the flag `user connections` from the drop-down menu, and set its value to your organization recommended value.
6. Click `Save` to save your changes.
7. Confirm your changes under `Flags` on the Overview page.

**Note:** Any changes to the database flag values, may require your instance to be restarted

### From Command Line

1. List all Cloud SQL database Instances

```bash
gcloud sql instances list
```

2. Configure the required database flag for every Cloud SQL SQL Server database instance using the below command.

```bash
gcloud sql instances patch INSTANCE_NAME --database-flags "user connections=[0-32,767]"
```

**Note:** This command will overwrite all database flags previously set. To keep those and add new ones, include the values for all flags you want set on the instance; any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").
