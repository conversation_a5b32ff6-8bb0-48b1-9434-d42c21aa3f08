## Description

PostgreSQL logs only the IP address of the connecting hosts. The log_hostname flag controls the logging of hostnames in addition to the IP addresses logged. The performance hit is dependent on the configuration of the environment and the host name resolution setup. This parameter can only be set in the postgresql.conf file or on the server command line.

Logging hostnames allows for the association of hostname to IP address at the time of connection. This information can aid with incident response efforts particularly in an environment that utilized dynamic IP addresses. Logging hostnames may incur overhead on server performance as for each statement logged, DNS resolution will be required to convert IP address to hostname. Depending on the setup, this may be non-negligible. This recommendation is applicable to PostgreSQL database instances.

## Remediation

### From Console:

1. Go to the Cloud SQL Instances page in the Google Cloud Console by visiting https://console.cloud.google.com/sql/instances.
2. Select the PostgreSQL instance for which you want to enable the database flag.
3. Click `Edit`.
4. Scroll down to the Flags section.
5. To set a flag that has not been set on the instance before, click `Add item`, choose the flag `log_hostname` from the drop-down menu and the value to `On`.
6. Click `Save` to save your changes.
7. Confirm your changes under `Flags` on the Overview page.

### From Command Line:

1. Configure the log_hostname database flag for every Cloud SQL PosgreSQL database instance using the below command.

```bash
gcloud sql instances patch <INSTANCE_NAME> --database-flags log_hostname=on
```

**Note**: This command will overwrite all database flags previously set. To keep those and add new ones, include the values for all flags you want set on the instance; any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").