## Description

Database Server should accept connections only from trusted Network(s)/IP(s) and restrict access from the world.

To minimize attack surface on a Database server instance, only trusted/known and required IP(s) should be white-listed to connect to it.

An authorized network should not have IPs/networks configured to 0.0.0.0/0 which will allow access to the instance from anywhere in the world. Note that authorized networks apply only to instances with public IPs.

## Remediation

This recommendation is applicable for PostgreSQL, MySQL generation 1, MySQL generation 2 and SQL Server 2017 instances.

### From Console

1. Login in to [Cloud SQL Instances](https://console.cloud.google.com/sql/instances)
2. Select the SQL Server instance to open details page.
3. Under **Configuration** section
4. Click on **Edit Configurations**
5. In the edit page, navigate to **Flags and Parameters** section
6. Under `Configuration` options expand the `Connections` section.
7. Click the delete icon for the authorized network 0.0.0.0/0.
8. Click Save to update the instance

### From Command Line

Update the authorized network list by dropping off any addresses

```bash
gcloud sql instances patch INSTANCE_NAME --authorizednetworks=IP_ADDR1,IP_ADDR2,...
```

**Note:** By default, authorized networks are not configured. Remote connection to Cloud SQL database instance is not possible unless authorized networks are configured.
