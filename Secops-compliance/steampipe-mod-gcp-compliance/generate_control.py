import google.generativeai as genai
import os 

def load_system_instructions(filepath: str) -> str:
    """Loads system instructions from a text file.

    Args:
        filepath: The path to the file containing the system instructions.

    Returns:
        The system instructions as a single string.  Handles potential errors.
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:  # Use utf-8 for broad compatibility
            return f.read().strip()  # Read and remove leading/trailing whitespace
    except FileNotFoundError:
        print(f"Error: File not found at {filepath}")
        return ""  # Or raise the exception if you prefer: raise
    except Exception as e:
        print(f"Error reading file {filepath}: {e}")
        return ""  # Or raise


def have_conversation(system_instructions: str, user_query: str) -> str:
    """
    Simulates a conversation with an LLM using an API key.

    Args:
        system_instructions: Initial instructions.
        user_query: The user's prompt.

    Returns:
        The LLM's response as a string.
    """

    # Get the API key from an environment variable.  This is CRITICAL.
    api_key = "AIzaSyAhwFfxC12MPv46YQFyygGjVuvFJDib_jg"
    if not api_key:
        raise ValueError("The GOOGLE_API_KEY environment variable is not set.")

    genai.configure(api_key=api_key)  # Configure the API key
    for m in genai.list_models():
        if 'generateContent' in m.supported_generation_methods:
            print(f"Model Name: {m.name}")
            print(f"  Display Name: {m.display_name}")
            print(f"  Description: {m.description}")
            print(f"  Supported Generation Methods: {m.supported_generation_methods}")
            print("-" * 20)
    model = genai.GenerativeModel('models/gemini-2.0-pro-exp') # No need to use start_chat
    # chat = model.start_chat()  # Use start_chat for multi-turn conversations.
    # chat.send_message(system_instructions) # prime with the system instructions
    # response = chat.send_message(user_query)
    # return response.text
    # priming and user_query in a single prompt
    combined_prompt = system_instructions + "\n\n" + user_query
    response = model.generate_content(combined_prompt) # Use generate_content
    print(response)

    return response.text


def run_prompt(query):
    """Main function to load prompt, interact, and print results."""

    system_instructions = load_system_instructions("generate_prompt.txt") # Loads from prompt.py

    if not system_instructions:  # Check if instructions were loaded
        print("No system instructions loaded. Exiting.")
        return

    llm_responses = have_conversation(system_instructions, query)
    return llm_responses
