steps:
  - name: gcr.io/cloud-builders/docker
    id: qemu
    args:
      - run
      - '--privileged'
      - multiarch/qemu-user-static
      - '--reset'
      - '-p'
      - 'yes'

  - name: gcr.io/cloud-builders/docker
    id: build
    env:
      - DOCKER_BUILDKIT=1
      - DOCKER_CLI_EXPERIMENTAL=enabled
    args:
      - buildx
      - create
      - '--use'
      - '--name=mybuilder'
      - '--platform=linux/amd64'

  - name: gcr.io/cloud-builders/docker
    env:
      - DOCKER_BUILDKIT=1
      - DOCKER_CLI_EXPERIMENTAL=enabled
    args:
      - buildx
      - build
      - '--platform=linux/amd64'
      - '--build-arg'
      - STEAMPIPE_DATABASE_PASSWORD=steampipe
      - '--build-arg'
      - STEAMPIPE_DATABASE_USER=steampipe
      - '--build-arg'
      - STEAMPIPE_DATABASE_NAME=steampipe
      - '--build-arg'
      - STEAMPIPE_INIT_TIMEOUT=300
      - '--build-arg'
      - STEAMPIPE_TIMEOUT=600s
      - '--build-arg'
      - CUSTOMER_ID=local
      - '--build-arg'
      - PROJECT_ID=${PROJECT_ID}
      - '--build-arg'
      - AUTH_TYPE=sa
      - '--build-arg'
      - 'DOMAIN=${_DOMAIN}'
      - '--tag'
      - gcr.io/$PROJECT_ID/steampipe-service-gcp
      - '--load'
      - '--provenance=false'
      - '--no-cache'
      - '--progress=plain'
      - .
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - gcr.io/$PROJECT_ID/steampipe-service-gcp

substitutions:
  _DOMAIN: "default-domain.com"

options:
  machineType: E2_HIGHCPU_8
  logging: CLOUD_LOGGING_ONLY
  env:
    - DOCKER_CLI_EXPERIMENTAL=enabled
    - DOCKER_BUILDKIT=1
  dynamicSubstitutions: true