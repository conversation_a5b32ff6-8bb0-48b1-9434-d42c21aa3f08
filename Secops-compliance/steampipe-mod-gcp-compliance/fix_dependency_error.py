#!/usr/bin/env python3
"""
Fix for the Powerpipe dependency resolution error
"""
import subprocess
import os
import time

def ensure_steampipe_ready():
    """Ensure Steampipe service is properly initialized"""
    print("Checking Steampipe service status...")
    
    # Check if service is running
    result = subprocess.run(
        ["steampipe", "service", "status"],
        capture_output=True,
        text=True
    )
    
    if "running" not in result.stdout.lower():
        print("Steampipe service not running. Starting...")
        # Try to start with specific settings
        subprocess.run(
            ["steampipe", "service", "start", "--database-port", "9194"],
            capture_output=True,
            text=True
        )
        time.sleep(5)  # Give it time to start
    
    return True

def validate_mod_structure():
    """Validate the mod structure before running benchmark"""
    print("Validating mod structure...")
    
    # Check if mod.pp exists
    if not os.path.exists("mod.pp"):
        print("ERROR: mod.pp not found!")
        return False
    
    # Try to list benchmarks without database connection
    result = subprocess.run(
        ["powerpipe", "benchmark", "list"],
        capture_output=True,
        text=True
    )
    
    if result.returncode != 0:
        print(f"ERROR listing benchmarks: {result.stderr}")
        return False
    
    # Check if cft_scorecard_v1 is in the list
    if "cft_scorecard_v1" not in result.stdout:
        print("ERROR: cft_scorecard_v1 benchmark not found!")
        return False
    
    print("✓ Mod structure validated successfully")
    return True

def run_benchmark_with_retry(benchmark_name, project_id, max_retries=3):
    """Run benchmark with retry logic for dependency resolution"""
    for attempt in range(max_retries):
        print(f"\nAttempt {attempt + 1} of {max_retries}...")
        
        # Build command
        command = [
            "powerpipe",
            "benchmark",
            "run",
            benchmark_name,
            "--search-path-prefix",
            "gcp",
            "--var",
            f"project_id={project_id}",
            "--export=json",
            "--output=none"  # Suppress normal output to see errors clearly
        ]
        
        print(f"Running: {' '.join(command)}")
        
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print("✓ Benchmark executed successfully!")
            return True, result.stdout
        
        # Check for dependency resolution error
        if "Failed to resolve dependencies" in result.stderr:
            print(f"✗ Dependency resolution error on attempt {attempt + 1}")
            print("Error details:", result.stderr[:500])
            
            if attempt < max_retries - 1:
                print("Waiting before retry...")
                time.sleep(2)
        else:
            print(f"✗ Other error: {result.stderr[:500]}")
            return False, result.stderr
    
    return False, "Failed after all retries"

def suggested_fix():
    """Suggest a fix for the dependency resolution issue"""
    print("\n=== SUGGESTED FIX ===")
    print("""
The dependency resolution error typically occurs when:

1. The Steampipe/Powerpipe database connection isn't properly established
2. The mod has circular dependencies (which we've ruled out)
3. There's a timing issue with mod loading

To fix this in the app.py:

1. Add retry logic to the run_powerpipe_command function:
   - Wait for the database to be ready
   - Retry the command if dependency resolution fails
   - Add a small delay between retries

2. Ensure the Steampipe service is properly initialized before running benchmarks:
   - Check service status
   - Wait for database to be available
   - Validate mod structure

3. Consider running a simple query first to "warm up" the connection:
   - Run a basic benchmark like "my_controls" first
   - This ensures the mod is loaded before running complex benchmarks

Would you like me to create a patch for app.py with these fixes?
""")

def main():
    print("Testing Powerpipe dependency resolution...")
    
    # Validate mod structure
    if not validate_mod_structure():
        print("Mod validation failed!")
        return
    
    # Try to run the problematic benchmark
    success, output = run_benchmark_with_retry("cft_scorecard_v1", "gcp")
    
    if not success:
        print("\n✗ Failed to run benchmark")
        suggested_fix()
    else:
        print("\n✓ Benchmark ran successfully!")

if __name__ == "__main__":
    main()