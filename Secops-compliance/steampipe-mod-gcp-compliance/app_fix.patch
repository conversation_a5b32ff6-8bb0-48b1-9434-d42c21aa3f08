--- app.py.original
+++ app.py.fixed
@@ -265,6 +265,7 @@ def extract_steampipe_data(response_text: str, control_name_suggestion: str = "
 def run_powerpipe_command(command_name, project_id):
     """Execute powerpipe benchmark command with project_id and return real-time output"""
     try:
+        max_retries = 3
         # Construct the command based on whether we want to scan all projects or a specific one
         formatted_project_id = project_id.replace("-", "_")
         base_command = [
@@ -280,24 +281,52 @@ def run_powerpipe_command(command_name, project_id):
         
         log(f"Executing command: {' '.join(base_command)}")
         
-        # Create a process with pipe to capture output
-        process = subprocess.Popen(
-            base_command,
-            stdout=subprocess.PIPE,
-            stderr=subprocess.PIPE,
-            universal_newlines=True,
-            text=True,
-            cwd="/app/steampipe-mod-gcp-compliance"
-        )
+        # Retry logic for dependency resolution errors
+        for attempt in range(max_retries):
+            # Create a process with pipe to capture output
+            process = subprocess.Popen(
+                base_command,
+                stdout=subprocess.PIPE,
+                stderr=subprocess.PIPE,
+                universal_newlines=True,
+                text=True,
+                cwd="/app/steampipe-mod-gcp-compliance"
+            )
+            
+            # Read output and error streams
+            stdout, stderr = process.communicate()
+            
+            # Check for dependency resolution error
+            if process.returncode != 0 and "Failed to resolve dependencies" in stderr:
+                log(f"Dependency resolution error on attempt {attempt + 1}/{max_retries}", 
+                    error=stderr[:500], severity="WARNING")
+                
+                if attempt < max_retries - 1:
+                    # Wait before retry
+                    time.sleep(2)
+                    
+                    # Try to warm up the connection with a simple benchmark
+                    if attempt == 0:
+                        warm_up_command = ["powerpipe", "benchmark", "list"]
+                        subprocess.run(warm_up_command, capture_output=True, text=True)
+                        time.sleep(1)
+                    continue
+                else:
+                    # Final attempt failed
+                    log("Failed to resolve dependencies after all retries", severity="ERROR")
+                    return {
+                        "status": "error",
+                        "error": "Failed to resolve dependencies after 3 passes. This usually indicates a problem with the benchmark configuration. Please try again in a few moments.",
+                        "command": command_name,
+                        "technical_details": stderr[:1000]
+                    }
+            
+            # If we get here, either success or a different error
+            break
         
-        # Read output and error streams
-        stdout, stderr = process.communicate()
         # Try to find and read the exported JSON file
         json_file_path = None
         json_data = None
         
         for line in stdout.splitlines():
             if "File exported to" in line:
                 json_file_path = line.split()[-1].strip()
                 break