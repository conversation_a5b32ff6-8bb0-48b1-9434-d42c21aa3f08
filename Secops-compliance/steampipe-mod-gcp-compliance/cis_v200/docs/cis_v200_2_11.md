## Description

It is recommended that a metric filter and alarm be established for SQL instance configuration changes.

Monitoring changes to SQL instance configuration changes may reduce the time needed to detect and correct misconfigurations done on the SQL server.

Below are a few of the configurable options which may the impact security posture of an SQL instance:
   - Enable auto backups and high availability: Misconfiguration may adversely impact business continuity, disaster recovery, and high availability
   - Authorize networks: Misconfiguration may increase exposure to untrusted networks

## Remediation

### From Console

Create the prescribed Log Metric:

1. Go to `Logging/Logs-based Metrics` by visiting [https://console.cloud.google.com/logs/metrics](https://console.cloud.google.com/logs/metrics) and click "CREATE METRIC".
2. Click the down arrow symbol on the `Filter Bar` at the rightmost corner and select `Convert to Advanced Filter`.
3. Clear any text and add:

```bash
protoPayload.methodName="cloudsql.instances.update"
```

4. Click `Submit Filter`. Display logs appear based on the filter text entered by the user.
5. In the `Metric Editor` menu on right,fill out the name field.Set `Units` to `1` (default) and Type to Counter. This ensures that the log metric counts the number of log entries matching the user's advanced logs query.
6. Click `Create Metric`

#### Create the prescribed alert policy:

1. Identify the newly created metric under the section `User-defined Metrics` at [https://console.cloud.google.com/logs/metrics](https://console.cloud.google.com/logs/metrics).
2. Click the 3-dot icon in the rightmost column for the new metric and select `Create alert from Metric`. A new page appears.
3. Fill out the alert policy configuration and click `Save`. Choose the alerting threshold and configuration that makes sense for the user's organization. For example, a threshold of zero(0) for the most recent value will ensure that a notification is triggered for every owner change in the user's project:

```bash
Set `Aggregator` to `Count`
Set `Configuration`:
 - Condition: above
 - Threshold: 0
 - For: most recent value
```

4. Configure the desired notifications channels in the section `Notifications`.
5. Name the policy and click `Save`.

### From Command Line

Create the prescribed log metric:
   - Use the command: gcloud logging metrics create

Create the prescribed alert policy:
   - Use the command: gcloud alpha monitoring policies create
   - Reference for Command Usage: [https://cloud.google.com/sdk/gcloud/reference/alpha/monitoring/policies/create](https://cloud.google.com/sdk/gcloud/reference/alpha/monitoring/policies/create).
