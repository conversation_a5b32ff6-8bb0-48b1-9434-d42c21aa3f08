## Description

Instance addresses can be public IP or private IP. Public IP means that the instance is accessible through the public internet. In contrast, instances using only private IP are not accessible through the public internet, but are accessible through a Virtual Private Cloud (VPC). Limiting network access to your database will limit potential attacks. Setting databases access only to private will reduce attack surface.

## Remediation

### From Console

1. In the Google Cloud console, go to the `Cloud SQL Instances` page.
2. Open the `Overview page` of an instance by clicking the instance name.
3. Select `Connections` from the SQL navigation menu.
4. Check the `Private IP` checkbox. A drop-down list shows the available networks in your project.
5. Select the VPC network you want to use:
If you see `Private service connection required`:
   1. Click `Set up connection`.
   2. In the `Allocate an IP range` section, choose one of the following options:
   3. Select one or more existing IP ranges or create a new one from the dropdown. The dropdown includes previously allocated ranges, if there are any, or you can select Allocate a new IP range and enter a new range and name.
   4. Use an automatically allocated IP range in your network.
   Note: You can specify an address range only for a primary instance, not for a read replica or clone.
   5. Click Continue.
   6. Click Create connection.
   7. Verify that you see the Private service connection for network VPC_NETWORK_NAME has been successfully created status.
6. [Optional step for Private Services Access - review reference links to VPC documents for additional detail] If you want to allow other Google Cloud services such as BigQuery to access data in Cloud SQL and make queries against this data over a private IP connection, then select the Private path for Google Cloud services check box.
7. Click Save

### From Command Line:

1. List cloud SQL instances

```bash
gcloud sql instances list --format="json" | jq '.[] |.connectionName,.ipAddresses'
```
**Note**: the `project name` of the instance you want to set to a private IP, this will be <PROJECT_ID>
**Note**: the `instance name` of the instance you want to set to a private IP, this will be <INSTANCE_ID>

Example public instance output:

```bash
"my-project-123456:us-central1:my-instance"
 {
 "ipAddress": "0.0.0.0",
 "type": "PRIMARY"
 },
 {
 "ipAddress": "0.0.0.0",
 "type": "OUTGOING"
 }
```

1. run the following command to list the available VPCs

```bash
gcloud compute networks list --format="json" | jq '.[].name'
```
**Note** the name of the VPC to use for the instance private IP, this will be <VPC_NETWORK_NAME>

3. run the following to set instance to a private IP

```bash
gcloud beta sql instances patch <INSTANCE_ID> \--project=<PROJECT_ID> \--network=projects/<PROJECT_ID>/global networks<VPC_NETWORK_NAME> \--no-assign-ip
```

### Default Value

Public IP