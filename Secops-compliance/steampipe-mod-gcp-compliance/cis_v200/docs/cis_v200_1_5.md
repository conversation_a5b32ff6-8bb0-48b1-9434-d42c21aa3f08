## Description

A service account is a special Google account that belongs to an application or a VM, instead of to an individual end-user. The application uses the service account to call the service's Google API so that users aren't directly involved. It's recommended not to use admin access
for ServiceAccount.

Service accounts represent service-level security of the Resources (application or a VM) which can be determined by the roles assigned to it. Enrolling ServiceAccount with Admin rights gives full access to an assigned application or a VM. A ServiceAccount Access holder can perform critical actions like delete, update change settings, etc. without user intervention. For this reason, it's recommended that service accounts not have Admin
rights.

## Remediation

### From Console

1. Go to `IAM & admin/IAM` using [https://console.cloud.google.com/iam-admin/iam](https://console.cloud.google.com/iam-admin/iam)
2. Go to the `Members`
3. Identify `User-Managed user created` service account with roles containing `*Admin` or `*admin` or role matching `Editor` or role matching `Owner`
4. Click the `Delete bin` icon to remove the role from the member (service account in this case)

### From Command Line

```bash
gcloud projects get-iam-policy PROJECT_ID --format json > iam.json
```

  1. Using a text editor, Remove `Role` which contains `roles/*Admin` or `roles/*admin` or matched `=roles/editor` or matches `roles/owner`. Add a role to the bindings array that defines the group members and the role for those members.

For example, to grant the role roles/appengine.appViewer to the `ServiceAccount` which is roles/editor, you would change the example shown below as follows:

```bash
  {
    "bindings": [
    {
      "members": [
        "serviceAccount:<EMAIL>",
      ],
      "role": "roles/appengine.appViewer"
    },
    {
      "members": [
        "user:<EMAIL>"
      ],
      "role": "roles/owner"
    },
    {
      "members": [
        "serviceAccount:<EMAIL>",
        "serviceAccount:<EMAIL>"
      ],
      "role": "roles/editor"
    }
    ],
    "etag": "BwUjMhCsNvY="
  }
```

  2. Update the project's IAM policy:

```bash
gcloud projects set-iam-policy PROJECT_ID iam.json
```

### Default Value

User Managed (and not user-created) default service accounts have the `Editor (roles/editor)` role assigned to them to support GCP services they offer.
By default, there are no roles assigned to `User Managed User created` service accounts.
