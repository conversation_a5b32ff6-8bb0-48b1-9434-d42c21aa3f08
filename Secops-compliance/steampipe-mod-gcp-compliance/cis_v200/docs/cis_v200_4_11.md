## Description

Google Cloud encrypts data at-rest and in-transit, but customer data must be decrypted for processing. Confidential Computing is a breakthrough technology which encrypts data in-use while it is being processed. Confidential Computing environments keep data encrypted in memory and elsewhere outside the central processing unit (CPU).

Confidential VMs leverage the Secure Encrypted Virtualization (SEV) feature of AMD EPYC™ CPUs. Customer data will stay encrypted while it is used, indexed, queried, or trained on. Encryption keys are generated in hardware, per VM, and not exportable. 
Thanks to built-in hardware optimizations of both performance and security, there is no significant performance penalty to Confidential Computing workloads.

Confidential Computing enables customers' sensitive code and other data encrypted in memory during processing. Google does not have access to the encryption keys. Confidential VM can help alleviate concerns about risk related to either dependency on Google infrastructure or Google insiders' access to customer data in the clear.

## Remediation

Confidential Computing can only be enabled when an instance is created. You must delete the current instance and create a new one.

### From Console

1. Go to the VM instances page by visiting: [https://console.cloud.google.com/compute/instances](https://console.cloud.google.com/compute/instances).
2. Click `CREATE INSTANCE`.
3. Fill out the desired configuration for your instance.
4. Under the `Confidential VM service` section, check the option `Enable the Confidential Computing service on this VM instance`.
5. Click `Create`.

### From Command Line

Create a new instance with Confidential Compute enabled.

```bash
gcloud beta compute instances create INSTANCE_NAME --zone ZONE --confidential-compute --maintenance-policy=TERMINATE
```

### Default Value

By default, Confidential Computing is disabled for Compute instances.
