## Description

IAP authenticates the user requests to your apps via a Google single sign in. You can then manage these users with permissions to control access. It is recommended to use both IAP permissions and firewalls to restrict this access to your apps with sensitive information.

IAP ensure that access to VMs is controlled by authenticating incoming requests. Access to your apps and the VMs should be restricted by firewall rules that allow only the proxy IAP IP addresses contained in the ************/20 subnet. Otherwise, unauthenticated requests can be made to your apps. To ensure that load balancing works correctly health checks should also be allowed.

## Remediation

### From Console

1. Go to the Cloud Console [VPC network > Firewall rules](https://console.cloud.google.com/networking/firewalls/list?_ga=2.72166934.480049361.**********-**********.**********).
2. Select the checkbox next to the following rules:
    - default-allow-http
    - default-allow-https
    - default-allow-internal
3. Click Delete.
4. Click Create firewall rule and set the following values:
    - Name: allow-iap-traffic
    - Targets: All instances in the network
    - Source IP ranges (press Enter after you paste each value in the box, copy the value below the bold text including the dash):

        * `IAP Proxy Addresses` ************/20
        * `Google Health Check` ***********/22
        * `Google Health Check` **********/16

    - Protocols and ports:

        * Specified protocols and ports required for access and management of your app. For example most health check connection protocols would be covered by;
        * tcp:80 (Default HTTP Health Check port)
        * tcp:443--(Default HTTPS Health Check port)
        
  **Note: if you have custom ports used by your load balancers, you will need to list them here**

5. When you're finished updating values, click Create.

### Default Value

By default all traffic is allowed.
