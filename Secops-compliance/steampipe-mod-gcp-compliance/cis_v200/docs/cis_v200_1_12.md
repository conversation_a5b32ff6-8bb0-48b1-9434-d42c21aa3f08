## Description

API Keys should only be used for services in cases where other authentication methods are unavailable. Unused keys with their permissions in tact may still exist within a project. Keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to use standard authentication flow instead.

To avoid the security risk in using API keys, it is recommended to use standard authentication flow instead. Security risks involved in using API-Keys appear below:

- API keys are simple encrypted strings
- API keys do not identify the user or the application making the API request
- API keys are typically accessible to clients, making it easy to discover and steal an API key

## Remediation

### From Console

1. Go to `APIs & Services\Credentials` using
2. In the section `API Keys`, to delete API Keys: Click the `Delete Bin Icon` in front of every `API Key Name`.

### From Google Cloud Command Line

1. Run the following from within the project you wish to audit **`gcloud services api-keys list --filter`**
2. **Pipe the results into** `gcloud alpha services api-keys delete`

### Default Value

By default, API keys are not created for a project.
