## Description

It is recommended that IAM policy on Cloud Storage bucket does not allows anonymous or public access.

Allowing anonymous or public access grants permissions to anyone to access bucket content. Such access might not be desired if you are storing any sensitive data. Hence, ensure that anonymous or public access to a bucket is not allowed.

## Remediation

### From Console

1. Go to `Storage browser` by visiting [https://console.cloud.google.com/storage/browser](https://console.cloud.google.com/storage/browser). 
2. Click on the bucket name to go to its `Bucket details` page. 
3. Click on the `Permissions` tab. 
4. Click `Delete` button in front of `allUsers` and `allAuthenticatedUsers` to remove that particular role assignment.

### From Command Line

Remove `allUsers` and `allAuthenticatedUsers` access.

```bash
gsutil iam ch -d allUsers gs://BUCKET_NAME
gsutil iam ch -d allAuthenticatedUsers gs://BUCKET_NAME
```

### Prevention

You can prevent Storage buckets from becoming publicly accessible by setting up the `Domain restricted sharing` organization policy at: [https://console.cloud.google.com/iam-admin/orgpolicies/iam-allowedPolicyMemberDomains](https://console.cloud.google.com/iam-admin/orgpolicies/iam-allowedPolicyMemberDomains).

### Default Value

By Default, Storage buckets are not publicly shared.
