## Overview

This section covers security recommendations to follow to secure Cloud SQL database services.

The recommendations in this section on setting up database flags are also present in the [CIS Oracle MySQL Community Server 5.7 Benchmarks](https://www.cisecurity.org/benchmark/oracle_mysql) and in the [CIS PostgreSQL 12 Benchmarks](https://www.cisecurity.org/benchmark/postgresql). We, nevertheless, include them here as well, the remediation instructions are different on Cloud SQL. Settings these flags require superuser privileges and can only be configured through GCP controls.

Learn more at: [https://cloud.google.com/sql/docs/postgres/users](https://cloud.google.com/sql/docs/postgres/users) and [https://cloud.google.com/sql/docs/mysql/flags](https://cloud.google.com/sql/docs/mysql/flags).