## Description

Logging enabled on a HTTPS Load Balancer will show all network traffic and its destination.

Logging will allow you to view HTTPS network traffic to your web applications.

## Remediation

### From Console

1. From Google Cloud home open the Navigation Menu in the top left.
2. Under the `Networking` heading select `Network services`.
3. Select the HTTPS load-balancer you wish to audit.
4. Select `Edit` then `Backend Configuration`.
5. Select `Edit` on the corresponding backend service.
6. Click `Enable Logging`.
7. Set `Sample Rate` to a desired value. This is a percentage as a decimal point. 1.0 is 100%.

### From Command Line

1. Run the following command

```bash
gcloud compute backend-services update <serviceName> --region=REGION --enable-logging --logging-sample-rate=<percentageAsADecimal>
```

### Default Value

By default logging for https load balancing is disabled. When logging is enabled it sets the default sample rate as 1.0 or 100%. Ensure this value fits the need of your organization to avoid high storage costs.