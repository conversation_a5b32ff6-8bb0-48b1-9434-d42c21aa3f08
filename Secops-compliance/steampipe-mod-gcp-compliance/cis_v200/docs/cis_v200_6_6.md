## Description

It is recommended to configure Second Generation SQL instance to use private IPs instead of public IPs.

To lower the organization's attack surface, Cloud SQL databases should not have public IPs. Private IPs provide improved network security and lower latency for your application.

## Remediation

### From Console

1. Go to the Cloud SQL Instances page in the Google Cloud Console: [https://console.cloud.google.com/sql/instances](https://console.cloud.google.com/sql/instances)
2. Click the instance name to open its Instance details page.
3. Select the `Connections` tab.
4. Deselect the `Public IP` checkbox.
5. Click `Save` to update the instance.

### From Command Line

1. For every instance remove its public IP and assign a private IP instead:

```bash
gcloud beta sql instances patch INSTANCE_NAME --network=VPC_NETWOR_NAME --noassign-ip
```

2. Confirm the changes using the following command:

```sql
gcloud sql instances describe INSTANCE_NAME
```

### Prevention

To prevent new SQL instances from getting configured with public IP addresses, set up a `Restrict Public IP access on Cloud SQL instances` Organization policy at: [https://console.cloud.google.com/iam-admin/orgpolicies/sql-restrictPublicIp](https://console.cloud.google.com/iam-admin/orgpolicies/sql-restrictPublicIp).

### Default Value

By default, Cloud SQL instances have a public IP.
