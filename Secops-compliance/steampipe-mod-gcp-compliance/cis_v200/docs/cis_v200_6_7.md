## Description

It is recommended to have all SQL database instances set to enable automated backups.

Backups provide a way to restore a Cloud SQL instance to recover lost data or recover from a problem with that instance. Automated backups need to be set for any instance that contains data that should be protected from loss or damage.This recommendation is applicable for SQL Server, PostgreSql, MySql generation 1 and MySql generation 2 instances.

## Remediation

### From Console

1. Go to the Cloud SQL Instances page in the Google Cloud Console by visiting [https://console.cloud.google.com/sql/instances](https://console.cloud.google.com/sql/instances).
2. Select the instance where the backups need to be configured.
3. Click `Edit`.
4. In the `Backups` section, check `Enable automated backups`, and choose a backup window.
5. Click `Save`

### From Command Line

1. List all Cloud SQL database instances using the following command:

```bash
gcloud sql instances list
```

2. Enable `Automated backups` for every Cloud SQL database instance using the below command:

```sql
gcloud sql instances patch INSTANCE_NAME --backup-start-time [HH:MM]
```

The `backup-start-time` parameter is specified in 24-hour time, in the UTC±00 time zone, and specifies the start of a 4-hour backup window. Backups can start any time during the backup window.

### Default Value

By default, automated backups are not configured for Cloud SQL instances. Data backup is not possible on any Cloud SQL instance unless Automated Backup is configured.
