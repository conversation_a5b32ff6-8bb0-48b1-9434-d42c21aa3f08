locals {
  cloudfunction_admin_read_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "cloudfunction_admin_read_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.cloudfunction_admin_read_log,
  ]
  tags = local.cloudfunction_admin_read_log_common_tags
}

control "cloudfunction_admin_read_log" {
  title       = "Project Cloud Functions admin read logs should be properly configured."
  description = "This control checks if admin read logs for Cloud Functions is configured correctly."
  query       = query.cloudfunction_admin_read_log
}

query "cloudfunction_admin_read_log" {
  sql = <<-EOQ
    SELECT 
      service,
      CASE 
        WHEN audit_log_configs IS NULL THEN 'alarm'
        WHEN EXISTS (
          SELECT 1
          FROM jsonb_array_elements(audit_log_configs) AS config
          WHERE config ->> 'logType' = 'ADMIN_READ' AND 
                (config -> 'exemptedMembers') IS NULL
        ) THEN 'ok'
        ELSE 'alarm'
      END as status,
      CASE 
        WHEN audit_log_configs IS NULL THEN 'Admin read logging is not configured for ' || service || '.'
        WHEN EXISTS (
          SELECT 1
          FROM jsonb_array_elements(audit_log_configs) AS config
          WHERE config ->> 'logType' = 'ADMIN_READ' AND 
                (config -> 'exemptedMembers') IS NULL
        ) THEN 'Admin read logging is configured for ' || service || '.'
        ELSE 'Admin read logging is not configured for all users for ' || service || '.'
      END as reason
      ,project
    FROM 
      gcp_audit_policy
    where service = 'cloudfunctions.googleapis.com';
  EOQ
}
