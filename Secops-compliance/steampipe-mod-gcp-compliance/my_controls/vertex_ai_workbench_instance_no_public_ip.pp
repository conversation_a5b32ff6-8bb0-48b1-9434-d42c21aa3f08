locals {
  vertex_ai_workbench_instance_no_public_ip_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "vertex_ai_workbench_instance_no_public_ip_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.vertex_ai_workbench_instance_no_public_ip,
  ]
  tags = local.vertex_ai_workbench_instance_no_public_ip_common_tags
}

control "vertex_ai_workbench_instance_no_public_ip" {
  title       = "Vertex AI Workbench Instance should not have public IP address."
  description = "This control checks if Vertex AI Workbench instances have public IP addresses configured."
  query       = query.vertex_ai_workbench_instance_no_public_ip
}

query "vertex_ai_workbench_instance_no_public_ip" {
  sql = <<-EOQ
    SELECT
      name as resource,
      CASE
        WHEN network_spec ->> 'network_access_type' = 'PRIVATE' 
          OR network_spec -> 'vpc_network' IS NOT NULL 
          OR network_spec ->> 'enable_private_endpoint' = 'true' 
          OR network_spec ->> 'enable_public_ip' = 'false' THEN 'ok'
        ELSE 'alarm'
      END as status,
      CASE
        WHEN network_spec ->> 'network_access_type' = 'PRIVATE' 
          OR network_spec -> 'vpc_network' IS NOT NULL 
          OR network_spec ->> 'enable_private_endpoint' = 'true' 
          OR network_spec ->> 'enable_public_ip' = 'false' THEN title || ' is configured with private network access only.'
        ELSE title || ' has public IP address access configured.'
      END as reason,
      project,
      location
    FROM
      gcp_vertex_ai_notebook_runtime_template;
  EOQ
}