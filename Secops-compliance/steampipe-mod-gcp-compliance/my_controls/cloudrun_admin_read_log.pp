locals {
  cloudrun_admin_read_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "cloudrun_admin_read_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.cloudrun_admin_read_log,
  ]
  tags = local.cloudrun_admin_read_log_common_tags
}

control "cloudrun_admin_read_log" {
  title       = "Project Cloud Run admin read logs should be properly configured"
  description = "Ensure that Cloud Run Admin API has configured data access using 'ADMIN_READ'"
  query       = query.cloudrun_admin_read_log
}

query "cloudrun_admin_read_log" {
  sql = <<-EOQ
   SELECT 
     name as resource,
     CASE 
       WHEN 'ADMIN_READ' IN (
         SELECT 
           jsonb_array_elements(audit_log_configs) ->> 'logType'
         FROM 
           gcp_audit_policy
         WHERE 
           service = 'run.googleapis.com'
       )
       THEN 'ok' 
       ELSE 'alarm' 
     END as status,
     CASE
       WHEN 'ADMIN_READ' IN (
         SELECT 
           jsonb_array_elements(audit_log_configs) ->> 'logType'
         FROM 
           gcp_audit_policy
         WHERE 
           service = 'run.googleapis.com'
       )
       THEN name || ' Cloud Run has Admin Read logs enabled.'
       ELSE name || ' Cloud Run has Admin Read logs not enabled.'
     END as reason,
     project
   FROM 
     gcp_cloud_run_service;
  EOQ
}