locals {
  compute_data_read_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "compute_data_read_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.compute_data_read_log,
  ]
  tags = local.compute_data_read_log_common_tags
}

control "compute_data_read_log" {
  title       = "Ensure that data read access logs for compute service are enabled"
  description = "This control checks if all data read access logs are enabled for the compute service, including ADMIN_READ, DATA_READ, and DATA_WRITE."
  query       = query.compute_data_read_log
}

query "compute_data_read_log" {
  sql = <<-EOQ
  with project_audit_policy as (
    select
      project,
      jsonb_array_elements(audit_log_configs) ->> 'logType' as log_type
    from
      gcp_audit_policy
    where
      service like 'compute.googleapis.com%'
  )
  select
    project as resource,
    case
      when 'DATA_READ' in (select log_type from project_audit_policy) then 'ok'
      else 'alarm'
    end as status,
    case
      when 'DATA_READ' in (select log_type from project_audit_policy)
        then project || ' data read logs enabled.'
      else project || ' data read logs not enabled.'
    end as reason
  from
    project_audit_policy
  group by
    project;
  EOQ
}