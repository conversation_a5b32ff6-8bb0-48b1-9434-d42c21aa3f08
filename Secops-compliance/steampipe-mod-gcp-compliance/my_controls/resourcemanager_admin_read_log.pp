locals {
  resourcemanager_admin_read_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "resourcemanager_admin_read_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.resourcemanager_admin_read_log,
  ]
  tags = local.resourcemanager_admin_read_log_common_tags
}

control "resourcemanager_admin_read_log" {
  title       = "Project Resource Manager admin read logs should be properly configured"
  description = "This control ensures that Project Resource Manager admin read logs is properly configured."
  query       = query.resourcemanager_admin_read_log
}

query "resourcemanager_admin_read_log" {
  sql = <<-EOQ
    WITH project_audit_logs_configuration AS (
      SELECT
        project,
        service
      FROM
        gcp_audit_policy,
        jsonb_array_elements(audit_log_configs) as log_config
      WHERE
        service = 'cloudresourcemanager.googleapis.com'
        and (
          log_config ->> 'logType' = 'ADMIN_READ'
        )
    )
    SELECT
      p.project_id as resource,
      CASE
        WHEN palc.project is null THEN 'alarm'
        ELSE 'ok'
      END as status,
      CASE
        WHEN palc.project is null
          THEN p.name || ' audit policy not configured to log the event type with service account ''cloudresourcemanager.googleapis.com''.'
        ELSE p.name || ' audit policy configured to log the event type with service account ''cloudresourcemanager.googleapis.com''.'
      END as reason
    FROM
      gcp_project AS p
    LEFT JOIN project_audit_logs_configuration as palc
    ON  p.project_id = palc.project
    where p.lifecycle_state = 'ACTIVE';
  EOQ
}