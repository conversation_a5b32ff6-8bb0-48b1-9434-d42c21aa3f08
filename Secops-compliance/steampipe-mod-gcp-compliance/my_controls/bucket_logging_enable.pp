locals {
  bucket_logging_enable_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "bucket_logging_enable_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.bucket_logging_enable,
  ]
  tags = local.bucket_logging_enable_common_tags
}

control "bucket_logging_enable" {
  title       = "Ensure that logging is enabled for Cloud Storage buckets"
  description = "This control checks if logging is enabled for your Cloud Storage buckets."
  query       = query.bucket_logging_enable
}

query "bucket_logging_enable" {
  sql = <<-EOQ
    SELECT
      name as resource,
      CASE
        WHEN log_bucket is not null THEN 'ok'
        ELSE 'alarm'
      END as status,
      CASE
        WHEN log_bucket is not null THEN title || ' logging enabled.'
        ELSE title || ' logging not enabled.'
      END as reason,
      project
    FROM
      gcp_storage_bucket;
  EOQ
}
