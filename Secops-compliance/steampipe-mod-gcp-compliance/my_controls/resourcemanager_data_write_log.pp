locals {
  resourcemanager_data_write_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "resourcemanager_data_write_log_controls" {
  title       = "Resource Manager Data Write Log Controls"
  description = "This benchmark contains controls to ensure Resource Manager data write logs are properly configured."
  children = [
    control.resourcemanager_data_write_log,
  ]
  tags = local.resourcemanager_data_write_log_common_tags
}

control "resourcemanager_data_write_log" {
  title       = "Project Resource Manager data write logs should be properly configured"
  description = "This control ensures that Project Resource Manager data write logs are properly configured."
  query       = query.resourcemanager_data_write_log
}

query "resourcemanager_data_write_log" {
  sql = <<-EOQ
    WITH project_audit_logs_configuration AS (
      SELECT
        project,
        service
      FROM
        gcp_audit_policy,
        jsonb_array_elements(audit_log_configs) as log_config
      WHERE
        service = 'cloudresourcemanager.googleapis.com'
        and (
          log_config ->> 'logType' = 'DATA_WRITE'
        )
    )
    SELECT
      p.project_id as resource,
      CASE
        WHEN palc.project is null THEN 'alarm'
        ELSE 'ok'
      END as status,
      CASE
        WHEN palc.project is null
          THEN p.name || ' audit policy not configured to log DATA_WRITE event type with service account ''cloudresourcemanager.googleapis.com''.'
        ELSE p.name || ' audit policy configured to log DATA_WRITE event type with service account ''cloudresourcemanager.googleapis.com''.'
      END as reason
    FROM
      gcp_project AS p
    LEFT JOIN project_audit_logs_configuration as palc
    ON  p.project_id = palc.project
    where p.lifecycle_state = 'ACTIVE';
  EOQ
}