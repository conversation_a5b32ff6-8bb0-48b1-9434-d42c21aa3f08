locals {
  cloudfunction_data_write_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "cloudfunction_data_write_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.cloudfunction_data_write_log,
  ]
  tags = local.cloudfunction_data_write_log_common_tags
}

control "cloudfunction_data_write_log" {
  title       = "Project Cloud Run data write logs should be properly configured"
  description = "Ensure that Cloud Run Admin API has configured data access using 'DATA_WRITE'"
  query       = query.cloudfunction_data_write_log
}

query "cloudfunction_data_write_log" {
  sql = <<-EOQ
   SELECT 
     name as resource,
     CASE 
       WHEN 'DATA_WRITE' IN (
         SELECT 
           jsonb_array_elements(audit_log_configs) ->> 'logType'
         FROM 
           gcp_audit_policy
         WHERE 
           service = 'run.googleapis.com'
       )
       THEN 'ok' 
       ELSE 'alarm' 
     END as status,
     CASE
       WHEN 'DATA_WRITE' IN (
         SELECT 
           jsonb_array_elements(audit_log_configs) ->> 'logType'
         FROM 
           gcp_audit_policy
         WHERE 
           service = 'run.googleapis.com'
       )
       THEN name || ' Cloud Run has Data Write logs enabled.'
       ELSE name || ' Cloud Run has Data Write logs not enabled.'
     END as reason,
     project
   FROM 
     gcp_cloud_run_service;
  EOQ
}