locals {
  bucket_encrypted_customkey_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "bucket_encrypted_customkey_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.bucket_encrypted_customkey,
  ]
  tags = local.bucket_encrypted_customkey_common_tags
}

control "bucket_encrypted_customkey" {
  title       = "Ensure that Cloud Storage Buckets are encrypted with Customer-Managed Key"
  description = "This control checks whether Cloud Storage buckets are encrypted using customer-managed keys (CMK) instead of default Google-managed keys."
  query       = query.bucket_encrypted_customkey
  tags        = merge(local.gcp_compliance_common_tags)
}

query "bucket_encrypted_customkey" {
  sql = <<-EOQ
    SELECT
      name as resource,
      CASE
        WHEN default_kms_key_name IS NOT NULL THEN 'ok'
        ELSE 'alarm'
      END as status,
      CASE
        WHEN default_kms_key_name IS NOT NULL THEN name || ' is encrypted with a customer-managed key.'
        ELSE name || ' is not encrypted with a customer-managed key.'
      END as reason,
      location,
      project
    FROM
      gcp_storage_bucket;
  EOQ
}
