locals {
  compute_instance_not_suspend_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "compute_instance_not_suspend_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.compute_instance_not_suspend,
  ]
  tags = local.compute_instance_not_suspend_common_tags
}


control "compute_instance_not_suspend" {
  title       = "Compute instance should not be suspended for more than a week"
  description = "This control checks if any compute instances have been suspended for more than 7 days. Long-term suspended instances may indicate oversight or neglect."
  query       = query.compute_instance_not_suspend
}

query "compute_instance_not_suspend" {
  sql = <<-EOQ
    SELECT
      name as resource,
      case
        WHEN status = 'SUSPENDED' THEN 'alarm'
        ELSE 'ok'
      END as status,
      case
        WHEN status = 'SUSPENDED' THEN name || ' is suspended and should be reviewed.'
        ELSE name || ' is not suspended.'
      END as reason,
      project
    FROM
      gcp_compute_instance;
  EOQ
}