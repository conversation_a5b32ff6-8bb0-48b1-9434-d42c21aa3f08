locals {
  cloudrun_service_notallow_ingress_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "cloudrun_service_notallow_ingress_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.cloudrun_service_notallow_ingress,
  ]
  tags = local.cloudrun_service_notallow_ingress_common_tags
}

control "cloudrun_service_notallow_ingress" {
  title       = "Ensure Cloud Run Service does not allow overly permissive ingress rules"
  description = "Restrict the traffic coming in from external network."
  query       = query.cloudrun_service_notallow_ingress
}

query "cloudrun_service_notallow_ingress" {
  sql = <<-EOQ
   select
      name as resource,
      case
       when ingress = 'INGRESS_TRAFFIC_ALL' then 'alarm'
       else 'ok'
      end as status,
      case
        when ingress = 'INGRESS_TRAFFIC_ALL'
          then title || ' allows traffic from any source.'
        else title || '  restricts traffic with ' || ingress || '.'
      end as reason,
      location,
      project
    from
      gcp_cloud_run_service;
  EOQ
}
