locals {
  cloudrun_data_read_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "cloudrun_data_read_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.cloudrun_data_read_log,
  ]
  tags = local.cloudrun_data_read_log_common_tags
}

control "cloudrun_data_read_log" {
  title       = "Project Cloud Run data read logs should be properly configured"
  description = "Ensure that Cloud Run data read logs are enabled, allowing for comprehensive monitoring and analysis of data access within the project."
  query       = query.cloudrun_data_read_log
}

query "cloudrun_data_read_log" {
  sql = <<-EOQ
   SELECT 
     name as resource,
  CASE 
  WHEN 'DATA_READ' IN (
  SELECT 
  jsonb_array_elements(audit_log_configs)->>'log_type'
  FROM 
  gcp_audit_policy
  WHERE 
  service = 'run.googleapis.com'
  )
  THEN 
  'ok' 
  ELSE 
  'alarm' 
  END as status,
     case
      when 'DATA_READ' IN (
        SELECT 
           jsonb_array_elements(audit_log_configs) ->> 'log_type'
        FROM 
          gcp_audit_policy
        WHERE 
          service = 'run.googleapis.com'
       )
       then name || ' Cloud Run has Data Read logs enabled.'
       else name || ' Cloud Run has Data Read logs  not enabled.'
       end as reason,
  project
   FROM 
  gcp_cloud_run_service;
  EOQ
}
