locals {
  compute_admin_read_logs_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "compute_admin_read_logs_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.compute_admin_read_logs,
  ]
  tags = local.compute_admin_read_logs_common_tags
}

control "compute_admin_read_logs" {
  title       = "Project compute admin read logs should be properly configured."
  description = "This control checks if the project compute admin read logs are configured correctly within the GCP environment."
  query       = query.compute_admin_read_logs
}

query "compute_admin_read_logs" {
  sql = <<-EOQ
    SELECT
      p.name AS resource,
      CASE
        WHEN EXISTS (
          SELECT 1
          FROM
            gcp_logging_exclusion AS e
          WHERE
            e.project = p.name
            AND e.filter LIKE '%logName:compute.googleapis.com%'
        )
        THEN 'alarm'
        ELSE 'ok'
      END AS status,
      p.name || ' compute admin read logs are ' ||
      CASE
        WHEN EXISTS (
          SELECT 1
          FROM
            gcp_logging_exclusion AS e
          WHERE
            e.project = p.name
            AND e.filter LIKE '%logName:compute.googleapis.com%'
        )
        THEN 'excluded.'
        ELSE 'included.'
      END AS reason,
      p.name as project
    FROM
      gcp_project AS p;
  EOQ
}