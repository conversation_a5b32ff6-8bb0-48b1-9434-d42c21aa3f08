locals {
  cloudrun_data_write_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "cloudrun_data_write_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.cloudrun_data_write_log,
  ]
  tags = local.cloudrun_data_write_log_common_tags
}

control "cloudrun_data_write_log" {
  title       = "Project Cloud Run data write logs should be properly configured."
  description = "This control checks that all Cloud Run data write logs are properly configured in the project."
  query = query.cloudrun_data_write_log
}

query "cloudrun_data_write_log" {
  sql = <<-EOQ
  SELECT
    p.project_id as resource,
    CASE
      WHEN NOT EXISTS (
        SELECT
          1
        FROM
          gcp_logging_sink AS s
        WHERE
          s.project = p.project_id
          AND s.filter LIKE '%logName:projects/your-project-id/logs/cloudaudit.googleapis.com%data_access%'
          AND s.destination LIKE 'bigquery.googleapis.com/projects/%'
          AND s.disabled = FALSE
      ) THEN 'alarm'
      ELSE 'ok'
    END AS status,
    CASE
      WHEN NOT EXISTS (
        SELECT
          1
        FROM
          gcp_logging_sink AS s
        WHERE
          s.project = p.project_id
          AND s.filter LIKE '%logName:projects/your-project-id/logs/cloudaudit.googleapis.com%data_access%'
          AND s.destination LIKE 'bigquery.googleapis.com/projects/%'
          AND s.disabled = FALSE
      ) THEN p.project_id || ' does not have proper Cloud Run data write log configuration.'
      ELSE p.project_id || ' has proper Cloud Run data write log configuration.'
    END AS reason,
     p.project_id
  FROM
    gcp_project as p;
EOQ
}
