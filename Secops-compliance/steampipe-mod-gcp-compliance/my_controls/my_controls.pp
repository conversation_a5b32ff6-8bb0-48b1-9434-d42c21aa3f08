locals {
  my_controls_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "my_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.testcontrol,
  ]
  tags = local.my_controls_common_tags
}

control "testcontrol" {
  title       = "Ensure IAM service account keys are not more than 3 months"
  description = "Checks if IAM service account keys are rotated within 3 months of creation."
  query       = query.testcontrol
}

query "testcontrol" {
  sql = <<-EOQ
    select
      name as resource,
      case
        when valid_after_time < (current_date - interval '3 months') then 'alarm'
        else 'ok'
      end as status,
      case
        when valid_after_time < (current_date - interval '3 months') then  ' key ' || split_part(name, '/',-1) || ' created on ' || to_char(valid_after_time, 'DD-Mon-YYYY') || ' exceeds 3 months.'
        else  ' key ' || split_part(name, '/',-1) || ' created on ' || to_char(valid_after_time, 'DD-Mon-YYYY') || ' is not older than 3 months.'
      end as reason,
        project
    from
      gcp_service_account_key;
  EOQ
}