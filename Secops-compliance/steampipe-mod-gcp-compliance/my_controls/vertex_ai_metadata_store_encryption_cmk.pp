locals {
  vertex_ai_metadata_store_encryption_cmk_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "vertex_ai_metadata_store_encryption_cmk_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.vertex_ai_metadata_store_encryption_cmk,
  ]
  tags = local.vertex_ai_metadata_store_encryption_cmk_common_tags
}

control "vertex_ai_metadata_store_encryption_cmk" {
  title       = "Vertex AI Metadata Store should be encrypted with a customer-managed key."
  description = "This control checks whether Vertex AI Metadata Store is encrypted with a customer-managed key."
  query       = query.vertex_ai_metadata_store_encryption_cmk

  tags = merge(local.gcp_compliance_common_tags, {
     service = "GCP/VertexAI"
  })
}

query "vertex_ai_metadata_store_encryption_cmk" {
  sql = <<-EOQ
    select
      name as resource,
      case
        when encryption_spec is null then 'alarm'
        when encryption_spec ->> 'kmsKey<PERSON>ame' is not null then 'ok'
        else 'alarm'
      end as status,
      case
        when encryption_spec is null then title || ' not encrypted with CMEK.'
        else title || ' encrypted with CMEK.'
      end as reason,
      location,
      project
    from
      gcp_vertex_ai_model;
  EOQ
}
