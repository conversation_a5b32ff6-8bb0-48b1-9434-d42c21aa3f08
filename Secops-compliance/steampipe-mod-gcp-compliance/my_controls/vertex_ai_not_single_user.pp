locals {
  vertex_ai_not_single_user_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "vertex_ai_not_single_user_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.vertex_ai_not_single_user,
  ]
  tags = local.vertex_ai_not_single_user_common_tags
}

control "vertex_ai_not_single_user" {
  title       = "Vertex AI Workbench user-managed notebooks instance's JupyterLab interface should not assign a single user login."
  description = "Vertex AI Workbench user-managed notebooks instance's JupyterLab interface should not assign a single user login."
  query       = query.vertex_ai_not_single_user
}

query "vertex_ai_not_single_user" {
  sql = <<-EOQ
  select
    name as resource,
    case
      when machine_spec ? 'singleUserOnly' then 'alarm'
      when network_spec ? 'singleUserOnly' then 'alarm'
      when service_account is not null then 'alarm'
      else 'ok'
    end as status,
    case
      when machine_spec ? 'singleUserOnly' then name || ' is user specific (from machine_spec).'
      when network_spec ? 'singleUserOnly' then name || ' is user specific (from network_spec).'
      when service_account is not null then name || ' is user specific (has service_account).'
      else name || ' is not user specific.'
    end as reason,
    location,
    project
  from
    gcp_vertex_ai_notebook_runtime_template
  where 
    notebook_runtime_type = 'USER_MANAGED_NOTEBOOK'
  limit 1;
  EOQ
}