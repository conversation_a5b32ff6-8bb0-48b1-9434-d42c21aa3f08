locals {
  dataproc_cluster_kurberos_auth_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "dataproc_cluster_kurberos_auth_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.dataproc_cluster_kurberos_auth,
  ]
  tags = local.dataproc_cluster_kurberos_auth_common_tags
}

control "dataproc_cluster_kurberos_auth" {
  title       = "Dataproc Cluster should use Kerberos authentication"
  description = "This control checks whether Kerberos is enabled for Dataproc clusters."
  query       = query.dataproc_cluster_kurberos_auth
}

query "dataproc_cluster_kurberos_auth" {
  sql = <<-EOQ
    SELECT 
      cluster_name as resource,
      case
        when (config -> 'securityConfig' ->> 'enableKerberos')::boolean then 'ok'
        else 'alarm'
      end as status,
      case
        when (config -> 'securityConfig' ->> 'enableKerberos')::boolean then title || ' Kerberos authentication enabled.'
        else title || ' Kerberos authentication disabled.'
      end as reason,
      project
    FROM 
      gcp_dataproc_cluster;
  EOQ
}