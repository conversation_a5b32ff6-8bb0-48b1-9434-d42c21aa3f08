locals {
  compute_data_write_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "compute_data_write_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.compute_data_write_log,
  ]
  tags = local.compute_data_write_log_common_tags
}

control "compute_data_write_log" {
  title       = "Project compute data write logs should be properly configured."
  description = "This control ensures that data write logs for project compute is enabled."
  query       = query.compute_data_write_log
}

query "compute_data_write_log" {
  sql = <<-EOQ
 with project_audit_policy as (
    select
      project,
      jsonb_array_elements(audit_log_configs) ->> 'logType' as log_type
    from
      gcp_audit_policy
    where
      service like 'compute.googleapis.com%'
  )
  select
    project as resource,
    case
      when 'DATA_WRITE' in (select log_type from project_audit_policy) then 'ok'
      else 'alarm'
    end as status,
    case
      when 'DATA_WRITE' in (select log_type from project_audit_policy)
        then project || ' data write logs enabled.'
      else project || ' data write logs not enabled.'
    end as reason
  from
    project_audit_policy
  group by
    project;
  EOQ
}
