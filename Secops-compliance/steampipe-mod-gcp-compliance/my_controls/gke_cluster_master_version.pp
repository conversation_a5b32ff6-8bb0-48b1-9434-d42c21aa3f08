locals {
  gke_cluster_master_version_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "gke_cluster_master_version_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.gke_cluster_master_version,
  ]
  tags = local.gke_cluster_master_version_common_tags
}

control "gke_cluster_master_version" {
  title       = "Ensure GKE cluster master version is supported"
  description = "This control checks if the GKE cluster master version is currently supported by Google, ensuring security and stability."
  query       = query.gke_cluster_master_version

  }

query "gke_cluster_master_version" {
  sql = <<-EOQ
    SELECT
      self_link as resource,
      CASE
        WHEN current_master_version LIKE '%-gke.%' THEN
          CASE
            -- Extract the numeric part of the version for comparison (e.g., 1.28.1)
            WHEN (
              SPLIT_PART(SPLIT_PART(current_master_version, '-', 1), '.', 1) :: INT,
              SPLIT_PART(SPLIT_PART(current_master_version, '-', 1), '.', 2) :: INT,
              SPLIT_PART(SPLIT_PART(current_master_version, '-', 1), '.', 3) :: INT
            ) >= (1, 26, 0)  -- Replace 1, 26, 0 with the minimum supported version parts
            THEN 'ok'
            ELSE 'alarm'
          END
        ELSE 'alarm'  -- Not a GKE version string
      END as status,
      CASE
        WHEN current_master_version LIKE '%-gke.%' THEN
          CASE
            WHEN (
              SPLIT_PART(SPLIT_PART(current_master_version, '-', 1), '.', 1) :: INT,
              SPLIT_PART(SPLIT_PART(current_master_version, '-', 1), '.', 2) :: INT,
              SPLIT_PART(SPLIT_PART(current_master_version, '-', 1), '.', 3) :: INT
            ) >= (1, 26, 0)
            THEN title || ' master version ' || current_master_version || ' is supported.'
            ELSE title || ' master version ' || current_master_version || ' is not supported.'
          END
        ELSE title || ' master version ' || current_master_version || ' format is not a standard GKE version.'
      END as reason,
      location,
      project
    FROM
      gcp_kubernetes_cluster;
  EOQ
}
