locals {
  bigquery_migration_admin_read_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "bigquery_migration_admin_read_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.bigquery_migration_admin_read_log,
  ]
  tags = local.bigquery_migration_admin_read_log_common_tags
}

control "bigquery_migration_admin_read_log" {
  title       = "Project BigQuery Migration admin read logs should be properly configured"
  description = "Ensure that BigQuery Migration API has configured data access using 'ADMIN_READ'"
  query       = query.bigquery_migration_admin_read_log
}

query "bigquery_migration_admin_read_log" {
  sql = <<-EOQ
   SELECT 
     name as resource,
     CASE 
       WHEN 'ADMIN_READ' IN (
         SELECT 
           jsonb_array_elements(audit_log_configs) ->> 'logType'
         FROM 
           gcp_audit_policy
         WHERE 
           service = 'bigquerymigration.googleapis.com'
       )
       THEN 'ok' 
       ELSE 'alarm' 
     END as status,
     CASE
       WHEN 'ADMIN_READ' IN (
         SELECT 
           jsonb_array_elements(audit_log_configs) ->> 'logType'
         FROM 
           gcp_audit_policy
         WHERE 
           service = 'bigquerymigration.googleapis.com'
       )
       THEN name || ' BigQuery Migration has Admin Read logs enabled.'
       ELSE name || ' BigQuery Migration has Admin Read logs not enabled.'
     END as reason,
     project
   FROM 
     gcp_bigquery_dataset;
  EOQ
}