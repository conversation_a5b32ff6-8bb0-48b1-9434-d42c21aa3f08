locals {
  bigquery_dbpolicy_data_write_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "bigquery_dbpolicy_data_write_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.bigquery_dbpolicy_data_write_log,
  ]
  tags = local.bigquery_dbpolicy_data_write_log_common_tags
}

control "bigquery_dbpolicy_data_write_log" {
  title       = "Project BigQuery Data Policy data write logs should be properly configured."
  description = "This control checks if the BigQuery Data Policy data write logs are properly configured at the project level."
  query       = query.bigquery_dbpolicy_data_write_log
}

query "bigquery_dbpolicy_data_write_log" {
  sql = <<-EOQ
   SELECT 
     name as resource,
     CASE 
       WHEN 'DATA_WRITE' IN (
         SELECT 
           jsonb_array_elements(audit_log_configs) ->> 'logType'
         FROM 
           gcp_audit_policy
         WHERE 
           service = 'bigquerydatapolicy.googleapis.com'
       )
       THEN 'ok' 
       ELSE 'alarm' 
     END as status,
     CASE
       WHEN 'DATA_WRITE' IN (
         SELECT 
           jsonb_array_elements(audit_log_configs) ->> 'logType'
         FROM 
           gcp_audit_policy
         WHERE 
           service = 'bigquerydatapolicy.googleapis.com'
       )
       THEN name || ' BigQuery Data Policy has Data Write logs enabled.'
       ELSE name || ' BigQuery Data Policy has Data Write logs not enabled.'
     END as reason,
     project
   FROM 
     gcp_bigquery_dataset;
  EOQ
}
