locals {
  gke_on_prem_admin_read_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "gke_on_prem_admin_read_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.gke_on_prem_admin_read_log,
  ]
  tags = local.gke_on_prem_admin_read_log_common_tags
}

control "gke_on_prem_admin_read_log" {
  title       = "Project GKE On-Prem admin read logs should be properly configured."
  description = "Ensure that GKE On-Prem admin read logs are properly configured to send data access logs to a designated storage solution."
  query       = query.gke_on_prem_admin_read_log
}

query "gke_on_prem_admin_read_log" {
    sql = <<-EOQ
      SELECT 
          'projects/' || project || '/locations/global/auditConfigs' as resource,
          CASE
            WHEN audit_log_configs IS NULL THEN 'alarm'
            WHEN (
                SELECT jsonb_agg(log_config ->> 'logType') 
                FROM jsonb_array_elements(audit_log_configs) as log_config
            ) ?| array['ADMIN_READ', 'DATA_READ'] THEN 'ok'
            ELSE 'alarm'
          END as status,
          CASE
            WHEN audit_log_configs IS NULL THEN 'Data access logs not enabled.'
            WHEN (
                SELECT jsonb_agg(log_config ->> 'logType') 
                FROM jsonb_array_elements(audit_log_configs) as log_config
            ) ?| array['ADMIN_READ', 'DATA_READ'] THEN 'Data access logs enabled for ADMIN_READ and DATA_READ.'
            ELSE 'Data access logs not enabled for ADMIN_READ and DATA_READ.'
          END as reason,
          project
      FROM 
          gcp_audit_policy
      WHERE 
          service = 'gkeonprem.googleapis.com';
    EOQ
}