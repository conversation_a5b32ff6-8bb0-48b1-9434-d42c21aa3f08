locals {
  gke_admin_read_log_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "gke_admin_read_log_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.gke_admin_read_log,
  ]
  tags = local.gke_admin_read_log_common_tags
}

control "gke_admin_read_log" {
  title       = "Ensure GKE Hub admin read logs are properly configured"
  description = "Checks if GKE Hub admin read logs are configured with necessary audit policies."
  query       = query.gke_admin_read_log
}

query "gke_admin_read_log" {
  sql = <<-EOQ
  SELECT
    service AS resource,
    CASE
      WHEN audit_log_configs IS NULL THEN 'alarm'
      WHEN audit_log_configs::TEXT LIKE '%ADMIN_READ%' THEN 'ok'
      ELSE 'alarm'
    END AS status,
    CASE
      WHEN audit_log_configs IS NULL THEN 'GKE Hub admin read logging is not configured.'
      WHEN audit_log_configs::TEXT LIKE '%ADMIN_READ%' THEN 'GKE Hub admin read logging is configured.'
      ELSE 'GKE Hub admin read logging is not properly configured.'
    END AS reason,
    project
  FROM
    gcp_audit_policy
  WHERE
    service = 'gkehub.googleapis.com';
  EOQ
}
