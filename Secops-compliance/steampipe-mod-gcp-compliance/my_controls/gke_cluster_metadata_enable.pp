locals {
  gke_cluster_metadata_enable_common_tags = merge(local.gcp_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "gke_cluster_metadata_enable_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.gke_cluster_metadata_enable,
  ]
  tags = local.gke_cluster_metadata_enable_common_tags
}

control "gke_cluster_metadata_enable" {
  title       = "GKE cluster Metadata Server should be enabled"
  description = "This control checks whether Workload Identity is enabled on the cluster or GKE Metadata Server is enabled on all node pools."
  query       = query.gke_cluster_metadata_enable
}

query "gke_cluster_metadata_enable" {
  sql = <<-EOQ
    select
      c.name as resource,
      case
        when c.workload_identity_config is not null and c.workload_identity_config ->> 'workloadPool' <> '' then 'ok'
        when n -> 'config' -> 'workloadMetadataConfig' ->> 'mode' = 'GKE_METADATA' then 'ok'
        else 'alarm'
      end as status,
      case
        when c.workload_identity_config is not null and c.workload_identity_config ->> 'workloadPool' <> '' then c.name || ' has Workload Identity enabled at the cluster level.'
        when n -> 'config' -> 'workloadMetadataConfig' ->> 'mode' = 'GKE_METADATA' then c.name || ' has GKE Metadata Server enabled.'
        else c.name || ' does not have Workload Identity or GKE Metadata Server enabled.'
      end as reason,
      c.location,
      c.project
    from
      gcp_kubernetes_cluster as c,
      jsonb_array_elements(node_pools) as n;
  EOQ
}
