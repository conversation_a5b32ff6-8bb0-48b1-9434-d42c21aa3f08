from flask import Flask, request, jsonify
from flask_cors import CORS
import subprocess
import shlex
import logging
import sys
import json
import os
import threading
import time
from datetime import datetime
import google.auth
from google.oauth2 import service_account
from google.cloud import resourcemanager_v3
from google.cloud import asset_v1  # For project listing
from google.api_core.exceptions import GoogleAPICallError, PermissionDenied, NotFound
from google.protobuf.json_format import MessageToDict  # Add this line
from google.cloud import resourcemanager_v3
from generate_control import run_prompt
import re
import random
from google.cloud import secretmanager 
from google.cloud import secretmanager  # Add this import
from auth import auth, get_service_credentials
from google.auth import default
import run_steampipe
import signal
import atexit
from typing import List, Dict
import requests
from functools import wraps
import traceback

# Configure structured logging for Cloud Run
class StructuredMessage:
    def __init__(self, message, **kwargs):
        self.message = message
        self.kwargs = kwargs

    def __str__(self):
        return json.dumps({
            'message': self.message,
            'severity': 'INFO',
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            **self.kwargs
        })

# Configure logging for Cloud Run
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def log(message, **kwargs):
    """Enhanced logging format for Cloud Run"""
    logger.info(StructuredMessage(message, **kwargs))

# Constants
CONTROLS_FILE_PATH = './my_controls/my_controls.pp'  # Path to your .sp file
domain = os.getenv('DOMAIN')
NEOSEC_VERIFICATION_URL = f"https://{domain}/charge_credits"
NEOSEC_SUBSCRIPTION_URL = f"https://{domain}/check_subscription"

# Initialize global variables for process management
steampipe_process = None
max_retries = 3
database_timeout = 120  # seconds
AUTH_TYPE = os.getenv('AUTH_TYPE', 'adc')  # Default to 'adc' if not set

def cleanup_steampipe():
    """Stop Steampipe service and kill any lingering processes"""
    log("Performing Steampipe cleanup...")
    
    try:
        # First try force stop command which handles unknown states better
        try:
            subprocess.run(
                ["steampipe", "service", "stop", "--force"],
                capture_output=True,
                text=True,
                check=False,
                timeout=20  # Shorter timeout for force stop
            )
            log("Force stop command executed")
        except subprocess.TimeoutExpired:
            log("Force stop command timed out, proceeding with process kill", severity="WARNING")
        except Exception as e:
            log("Error during force stop", error=str(e), severity="ERROR")
        
        # Kill any leftover steampipe processes (more specific)
        subprocess.run(
            ["pkill", "-9", "-f", "steampipe"], 
            check=False, 
            capture_output=True
        )
        
        # Also kill postgres processes started by steampipe (more specific)
        subprocess.run(
            ["pkill", "-9", "-f", "postgres.*steampipe"], 
            check=False, 
            capture_output=True
        )
        
        time.sleep(3)  # Brief pause after cleanup
        log("Cleanup finished")
        
    except Exception as e:
        log("Error during cleanup", error=str(e), severity="ERROR")


def init_steampipe_service():
    """Start Steampipe service with better handling of unknown states"""
    max_attempts = 3
    
    for attempt in range(1, max_attempts + 1):
        log("Steampipe service start attempt", attempt=attempt, max_attempts=max_attempts)
        
        cleanup_steampipe()  # Clean up before each attempt
        
        try:
            # Start the service
            log("Starting Steampipe service...")
            result = subprocess.run(
                ["steampipe", "service", "start"],
                capture_output=True,
                text=True,
                check=False,
                timeout=90
            )
            
            if "running in an unknown state" in result.stderr:
                log("Service is in unknown state, attempting force stop...", severity="WARNING")
                subprocess.run(
                    ["steampipe", "service", "stop", "--force"],
                    capture_output=True,
                    text=True,
                    check=False,
                    timeout=30
                )
                time.sleep(5)
                continue  # Skip to next attempt
            
            # Wait for database to initialize (increased wait time)
            log("Waiting for Steampipe database to initialize...")
            time.sleep(15) 
            
            # Verify service is running with query (retry mechanism)
            verify_attempts = 3
            for v_attempt in range(1, verify_attempts + 1):
                log("Database verification attempt", attempt=v_attempt, max_attempts=verify_attempts)
                try:
                    verify_result = subprocess.run(
                        ["steampipe", "query", "select 1 as test"],
                        capture_output=True,
                        text=True,
                        check=False,
                        timeout=10
                    )
                    
                    if verify_result.returncode == 0:
                        log("Successfully started and verified Steampipe service")
                        return True
                    else:
                        log("Verification query failed", 
                            return_code=verify_result.returncode,
                            stdout=verify_result.stdout,
                            stderr=verify_result.stderr,
                            severity="WARNING")
                        
                        if v_attempt < verify_attempts:
                            log("Waiting before next verification attempt...")
                            time.sleep(5)
                except Exception as e:
                    log("Error during verification", error=str(e), severity="ERROR")
                    if v_attempt < verify_attempts:
                        log("Waiting before next verification attempt...")
                        time.sleep(5)
            
            log("All verification attempts failed", severity="ERROR")
            
        except subprocess.TimeoutExpired as e:
            log("Timeout starting service", attempt=attempt, error=str(e), severity="ERROR")
        except Exception as e:
            log("Error starting service", attempt=attempt, error=str(e), severity="ERROR")
        
        if attempt < max_attempts:
            log("Waiting before next service start attempt...")
            time.sleep(15)
    
    log("Failed to start Steampipe service after all attempts", severity="ERROR")
    return False

# Initialize the Flask app
app = Flask(__name__)
CORS(app)
app.register_blueprint(auth)

# Register cleanup handlers
atexit.register(cleanup_steampipe)
signal.signal(signal.SIGTERM, lambda signum, frame: sys.exit(0))  # Ensure atexit runs on SIGTERM

def get_gcp_organization_id():
    """Retrieve the GCP organization ID using default credentials"""
    try:
        # Get default credentials and project ID
        if not project_id:
            return None, "No project ID found in default credentials"

        # Initialize clients
        projects_client = resourcemanager_v3.ProjectsClient(credentials=credentials)
        
        # For secret_manager auth, try a different approach that doesn't require folder permissions
        if AUTH_TYPE == 'secret_manager':
            try:
                # Use search_projects to find organization ID from any project
                for project in projects_client.search_projects():
                    if hasattr(project, 'parent') and project.parent:
                        parent = project.parent
                        # Extract organization ID if parent is an organization
                        if parent.startswith("organizations/"):
                            org_id = parent.split('/')[1]
                            log(f"Found organization ID {org_id} from project {project.project_id}")
                            return org_id, None
                        # If parent is a folder, we can't traverse without permissions
                        # But we can still return a placeholder or skip
                        elif parent.startswith("folders/"):
                            log(f"Project {project.project_id} is in a folder, but no folder permissions", severity="INFO")
                            # Continue searching for a project directly under org
                            continue
                
                # If no org found, return None but not an error
                return None, "No organization ID found (projects may be in folders)"
                
            except Exception as e:
                log(f"Could not search projects for org detection: {str(e)}", severity="WARNING")
                return None, f"Could not search projects: {str(e)}"
        
        # Original logic for ADC
        project_name = f"projects/{project_id}"
        try:
            project = projects_client.get_project(name=project_name)
            parent = project.parent
        except Exception as e:
            log(f"Could not get project details for {project_id}: {str(e)}", severity="WARNING")
            return None, f"Could not get project details: {str(e)}"

        # Traverse parent hierarchy to find organization
        while parent:
            if parent.startswith("organizations/"):
                return parent.split('/')[1], None
            elif parent.startswith("folders/"):
                folder_id = parent.split('/')[1]
                folders_client = resourcemanager_v3.FoldersClient(credentials=credentials)
                try:
                    folder = folders_client.get_folder(name=f"folders/{folder_id}")
                    parent = folder.parent
                except PermissionDenied:
                    log(f"No permission to access folder {folder_id}, cannot determine organization", severity="WARNING")
                    return None, "Insufficient permissions to determine organization ID"
            else:
                return None, f"Unknown parent type: {parent}"

        return None, "No organization found in hierarchy"

    except PermissionDenied as e:
        return None, f"Permission denied: {e}"
    except NotFound as e:
        return None, f"Resource not found: {e}"
    except GoogleAPICallError as e:
        return None, f"API Error: {e}"
    except Exception as e:
        return None, f"Unexpected error: {str(e)}"

def save_output_to_json(project_id, benchmark, result_data):
    """Save the command output and results to a JSON file"""
    try:
        # Create output directory if it doesn't exist
        output_dir = "/app/steampipe-mod-gcp-compliance/outputs"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{output_dir}/{project_id}_{benchmark}_{timestamp}.json"
        
        # Prepare the JSON data
        json_data = {
            "project_id": project_id,
            "benchmark": benchmark,
            "timestamp": timestamp,
            "results": result_data
        }
        
        # Write JSON to file
        with open(filename, 'w') as f:
            json.dump(json_data, f, indent=2)
            
        return filename
    except Exception as e:
        logger.error(f"Error saving output to JSON file: {str(e)}")
        return None

def run_powerpipe_command(command_name, project_id):
    """Execute powerpipe benchmark command with project_id and return real-time output"""
    try:
        max_retries = 3
        # Construct the command based on whether we want to scan all projects or a specific one
        formatted_project_id = project_id.replace("-", "_")
        # When scanning all projects, don't pass project_id variable
        base_command = [
            "powerpipe",
            "benchmark",
            "run",
            command_name,
            "--search-path-prefix",
            "gcp" if project_id == "gcp" else f"gcp_{formatted_project_id}",
            "--export=json"
        ]
        
        # Only add project_id variable for specific project scans
        if project_id != "gcp":
            base_command.extend(["--var", f"project_id={project_id}"])
        
        log(f"Executing command: {' '.join(base_command)}")
        
        # Log current Steampipe configuration details
        try:
            config_path = "/home/<USER>/.steampipe/config/gcp.spc"
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config_content = f.read()
                connection_count = config_content.count('connection "gcp_')
                log(f"Steampipe config has {connection_count} project connections", severity="INFO")
        except Exception as e:
            log(f"Could not check config: {str(e)}", severity="WARNING")
        
        # Retry logic for dependency resolution errors
        stdout = ""
        stderr = ""
        process = None
        
        for attempt in range(max_retries):
            # Create a process with pipe to capture output
            process = subprocess.Popen(
                base_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                text=True,
                cwd="/app/steampipe-mod-gcp-compliance",
                env={**os.environ, "STEAMPIPE_WORKSPACE": "default"}
            )
            
            # Read output and error streams
            stdout, stderr = process.communicate()
            
            # Check for dependency resolution error
            if process.returncode != 0 and "Failed to resolve dependencies" in stderr:
                log(f"Dependency resolution error on attempt {attempt + 1}/{max_retries}", 
                    error=stderr[:500], severity="WARNING")
                
                if attempt < max_retries - 1:
                    # Wait before retry
                    time.sleep(2)
                    
                    # Try to warm up the connection with a simple command
                    if attempt == 0:
                        warm_up_command = ["powerpipe", "benchmark", "list"]
                        subprocess.run(warm_up_command, capture_output=True, text=True)
                        time.sleep(1)
                    continue
                else:
                    # Final attempt failed
                    log("Failed to resolve dependencies after all retries", severity="ERROR")
                    return {
                        "status": "error",
                        "error": "Benchmark Error: Invalid response format: missing benchmark_results or groups",
                        "command": command_name,
                        "benchmark_results": None
                    }
            
            # If we get here, either success or a different error
            break

        # Try to find and read the exported JSON file
        json_file_path = None
        json_data = None
        
        for line in stdout.splitlines():
            if "File exported to" in line:
                json_file_path = line.split()[-1].strip()
                break
                
        if json_file_path and os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r') as f:
                    json_data = json.load(f)
            except Exception as e:
                log(f"Error reading JSON file: {str(e)}")

        # Save consolidated results to our JSON file
        result_data = {
            "command_output": stdout,
            "command_error": stderr if stderr else None,
            "benchmark_results": json_data
        }
        
        output_file = save_output_to_json(project_id, command_name, result_data)

        return {
            "status": "success" if process.returncode == 0 else "error",
            "output": stdout,
            "error": stderr if stderr else None,
            "command": command_name,
            "return_code": process.returncode,
            "output_file": output_file,
            "benchmark_results": json_data
        }
    except Exception as e:
        log(f"Error executing command: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "command": command_name
        }

def extract_steampipe_data(response_text: str, control_name_suggestion: str = "testcontrol") -> dict:
    """Extracts and formats the Steampipe control and query.
    Returns a dict: {'control_block': ..., 'query_block': ..., 'control_name': ...}
    Returns error dict if critical parts are missing.
    """
    try:
        control_block = ""
        sql_query_block = ""
        control_name = control_name_suggestion

        # Extract the entire control block
        control_match = re.search(r'control\s+"([^"]+)"\s*\{[^}]*\}', response_text, re.DOTALL | re.IGNORECASE)
        if control_match:
            control_name = control_match.group(1).strip()
            control_block = control_match.group(0).strip()

        # Extract the entire query block
        query_match = re.search(r'query\s+"[^"]+"\s*\{[^}]*\}', response_text, re.DOTALL | re.IGNORECASE)
        if not query_match:  # If not in query block, look for a plain SQL block
           query_match = re.search(r'`sql(.*?)`', response_text, re.DOTALL | re.IGNORECASE)
           if query_match: # found sql block only
                sql_query_content = query_match.group(1).strip()
                # create query block
                sql_query_block = f'''query "{control_name}" {{
  sql = <<-EOQ
{sql_query_content}
  EOQ
}}'''
        else:
            sql_query_block = query_match.group(0).strip()

        # Construct default control block, if control block missing, but, sql block available
        if not control_block and sql_query_block:
            control_block = f"""control "{control_name}" {{
  title       = "Generated Control for {control_name}"
  description = "Generated control based on user prompt."
  query       = query.{control_name}
  tags = merge(local.gcp_compliance_common_tags, {{
    service = "GCP/Generated"  // Modify as needed
  }})
}}"""

        if not control_block or not sql_query_block: #check if we have both blocks
            return {"error": "Could not find both control and query blocks."}

        return {
            "control_block": control_block,
            "query_block": sql_query_block,
            "control_name": control_name,
        }

    except Exception as e:
        return {"error": f"Error during extraction/formatting: {e}"}

def write_steampipe_control_file(filepath: str, extracted_data: dict) -> None:
    """
    Overwrites the Steampipe control file with the new content.
    """
    control_name = extracted_data.get('control_name', 'testcontrol')  # Fallback
    control_block = extracted_data.get('control_block')
    query_block = extracted_data.get('query_block')

    if not control_block or not query_block:
        raise ValueError("Control block and query block are required.")

    try:
        # Construct the complete file content
        new_file_content = f"""locals {{
  my_controls_common_tags = merge(local.gcp_compliance_common_tags, {{
    type = "Benchmark"
  }})
}}

benchmark "my_controls" {{
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.{control_name},
  ]
  tags = local.my_controls_common_tags
}}

{control_block}

{query_block}
"""
        # Write the new content to the file (overwrites existing content)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_file_content)

    except Exception as e:
        raise Exception(f"Error writing to Steampipe control file: {e}")

def check_subscription():
    """
    Decorator to verify active subscription status without charging credits.
    Similar to charge_credits but uses the check_subscription endpoint instead.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if AUTH_TYPE == 'adc':
                log("Local development detected, skipping subscription verification", 
                    auth_type=AUTH_TYPE,
                    severity="INFO")
                return f(*args, **kwargs)
            try:
                # Get IAP token from headers
                iap_token = request.headers.get('X-Original-Iap-Jwt')
                log("Processing request headers", 
                    headers=dict(request.headers),
                    severity="DEBUG")
                
                host = request.headers.get('Referer')
                if not iap_token:
                    log("Missing IAP token", severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "Missing IAP token"
                    }), 401

                # Make request to check_subscription endpoint on neosec marketplace
                headers = {
                    "token": iap_token,
                    "Content-Type": "application/json",
                    "subdomain": host
                }
                
                log("Making request to check subscription", 
                    url=NEOSEC_SUBSCRIPTION_URL,  # You'll need to define this constant
                    severity="DEBUG")
                    
                # No need to include cost_credits for subscription check
                data = {}
                
                response = requests.post(
                    NEOSEC_SUBSCRIPTION_URL,
                    headers=headers,
                    json=data
                )

                log("Response from subscription check", 
                    response_status=response.status_code,
                    response_body=response.text,
                    severity="DEBUG")
                
                if response.status_code != 200:
                    log("Subscription verification failed", 
                        status_code=response.status_code,
                        severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "No active subscription found"
                    }), 402  # Payment Required

                return f(*args, **kwargs)
            except Exception as e:
                log("Subscription verification error", 
                    error=str(e),
                    stack_trace=traceback.format_exc(),
                    severity="ERROR")
                return jsonify({
                    "status": "error",
                    "error": f"Subscription verification failed: {str(e)}"
                }), 500
        return decorated_function
    return decorator


def charge_credits(cost_credits=1):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if AUTH_TYPE == 'adc':
                log("Local development detected, skipping IAP verification", 
                    auth_type=AUTH_TYPE,
                    severity="INFO")
                return f(*args, **kwargs)
            try:
                # Get IAP token from headers
                iap_token = request.headers.get('X-Original-Iap-Jwt')
                log("Processing request headers", 
                    headers=dict(request.headers),
                    severity="DEBUG")
                
                host = request.headers.get('Referer')
                if not iap_token:
                    log("Missing IAP token", severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "Missing IAP token"
                    }), 401

                # Make request to charge_credit endpoint on neosec marketplace
                headers = {
                    "token": iap_token,
                    "Content-Type": "application/json",
                    "subdomain": host
                }
                
                log("Making request to Neosec marketplace", 
                    url=NEOSEC_VERIFICATION_URL,
                    credits=cost_credits,
                    severity="DEBUG")
                    
                data = {
                    "cost_credits": cost_credits
                }
                
                response = requests.post(
                    NEOSEC_VERIFICATION_URL,
                    headers=headers,
                    json=data
                )

                log("Response from charge credit", 
                    response_status=response.status_code,
                    response_body=response.text,
                    severity="DEBUG")
                
                if response.status_code != 200:
                    log("Failed to charge credit", 
                        status_code=response.status_code,
                        severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "Failed to charge credit"
                    }), 400

                return f(*args, **kwargs)
            except Exception as e:
                log("IAP verification error", 
                    error=str(e),
                    stack_trace=traceback.format_exc(),
                    severity="ERROR")
                return jsonify({
                    "status": "error",
                    "error": f"IAP verification failed: {str(e)}"
                }), 500
        return decorated_function
    return decorator

@app.route('/api/gcp/generate', methods=['POST'])
def generate():
    """API endpoint to generate control"""
    try:
        data = request.get_json()
        user_query = data['prompt']  # Get the prompt from the request

        response_text = run_prompt(user_query)
        extracted_data = extract_steampipe_data(response_text)

        if "error" in extracted_data:
            return jsonify({'error': extracted_data["error"], 'response': response_text}), 500

        write_steampipe_control_file(CONTROLS_FILE_PATH, extracted_data)

        # Return the *combined*, formatted output to the user.
        formatted_output = f"{extracted_data['control_block']}\n\n{extracted_data['query_block']}"
        return jsonify({'response': formatted_output})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/gcp/run-benchmark-test', methods=['POST'])
def run_benchmark_test():
    """Test endpoint for benchmark without authentication - FOR TESTING ONLY"""
    try:
        data = request.get_json()
        if not data or 'benchmark' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'benchmark' parameter"
            }), 400
            
        if 'project_id' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'project_id' parameter"
            }), 400
            
        benchmark = data['benchmark']
        project_id = data['project_id']
        
        log(f"TEST: Running benchmark: {benchmark} for project: {project_id}")
        
        # Run the powerpipe command with project_id
        result = run_powerpipe_command(benchmark, project_id)
        return jsonify(result)
        
    except Exception as e:
        log(f"API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/gcp/run-benchmark', methods=['POST'])
@check_subscription()
def run_benchmark():
    """API endpoint to run powerpipe benchmark"""
    try:
        log(f"Request headers: {request.headers}")
        data = request.get_json()
        if not data or 'benchmark' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'benchmark' parameter"
            }), 400
            
        if 'project_id' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'project_id' parameter"
            }), 400
            
        benchmark = data['benchmark']
        project_id = data['project_id']
        
        log(f"Running benchmark: {benchmark} for project: {project_id}")
        
        # Run the powerpipe command with project_id
        result = run_powerpipe_command(benchmark, project_id)
        return jsonify(result)
        
    except Exception as e:
        log(f"API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/gcp/testcontrol', methods=['POST'])
def testcontrol():
    """
    Runs a Powerpipe command.  Expects 'command_name' and 'project_id' in the request body.
    """
    data = request.get_json()
    if not data or 'project_id' not in data:
        project_id = 'gcp'
    else:
        project_id = data['project_id']

    try:
        output = run_powerpipe_command("my_controls", project_id)
        log(f"{output}")
        # Return the output as a JSON response, and set the content type to plain text.
        return jsonify({'response': output})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Basic endpoint to verify API is running"""
    # Check if steampipe is truly running by attempting a simple query
    steampipe_status = "running"
    try:
        result = subprocess.run(
            ["steampipe", "query", "select 1"],
            capture_output=True,
            text=True,
            check=False,
            timeout=5
        )
        if result.returncode != 0:
            steampipe_status = "error"
    except Exception as e:
        log(f"Health check error: {str(e)}")
        steampipe_status = "error"
        
    return jsonify({
        "status": "running",
        "steampipe_status": steampipe_status
    })
    
@app.route('/api/gcp/get-org-id', methods=['GET'])
def get_organization_id():
    """API endpoint to fetch GCP organization ID"""
    try:
        org_id, error = get_gcp_organization_id()
        if error and ("Permission denied" in error or "No organization ID found" in error or "Insufficient permissions" in error):
            # Don't fail on permission errors or when org can't be determined, just return null org_id
            log(f"Organization ID fetch warning: {error}", severity="WARNING")
            return jsonify({
                "status": "success",
                "organization_id": None,
                "warning": error
            })
        elif error:
            log(f"Organization ID fetch error: {error}")
            return jsonify({"status": "error", "error": error}), 500
            
        return jsonify({
            "status": "success",
            "organization_id": org_id
        })
    except Exception as e:
        log(f"Organization ID API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500 
        
@app.route('/api/gcp/get-configured-projects', methods=['GET'])
def get_configured_projects():
    """Diagnostic endpoint to check what projects are configured in Steampipe"""
    try:
        config_path = "/home/<USER>/.steampipe/config/gcp.spc"
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config_content = f.read()
            
            # Count connections
            import re
            connections = re.findall(r'connection "gcp_([^"]+)"', config_content)
            
            return jsonify({
                "status": "success",
                "config_path": config_path,
                "total_connections": len(connections),
                "sample_connections": connections[:10] if connections else [],
                "config_length": len(config_content)
            })
        else:
            return jsonify({
                "status": "error",
                "error": f"Config file not found at {config_path}"
            })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/gcp/get-projects', methods=['POST'])
def get_projects():
    """API endpoint to fetch all projects under a GCP organization"""
    try:
        data = request.get_json()
        organization_id = data.get('organization_id') if data else None
        
        if not organization_id:
            # If no organization_id provided, try to discover projects without it
            log("No organization_id provided, using search_projects API")
            try:
                client = resourcemanager_v3.ProjectsClient(credentials=credentials)
                all_projects = []
                
                for project in client.search_projects():
                    all_projects.append({
                        "project_id": project.project_id,
                        "project_number": project.name.split('/')[-1] if '/' in project.name else project.name,
                        "name": project.display_name,
                        "parent": project.parent if hasattr(project, 'parent') else None
                    })
                
                return jsonify({
                    "status": "success",
                    "projects": all_projects
                })
            except Exception as e:
                log(f"Error searching projects: {str(e)}")
                return jsonify({"status": "error", "error": str(e)}), 500
        
        log(f"Fetching projects for organization: {organization_id}")
        projects, error = get_projects_for_organization(organization_id)
        
        if error:
            return jsonify({"status": "error", "error": error}), 500
            
        return jsonify({
            "status": "success",
            "projects": projects
        })
        
    except Exception as e:
        log(f"Error in get-projects API: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

def get_projects_for_organization(organization_id: str) -> (List[Dict], str):
    """List all projects in organization including nested folders"""
    try:
        client = resourcemanager_v3.ProjectsClient(credentials=credentials)
        folders_client = resourcemanager_v3.FoldersClient(credentials=credentials)
        
        all_projects = []
        seen_folders = set()

        def process_container(container: str):
            """Recursively process folders and projects in a container"""
            # Process projects (REMOVED STATE CHECK)
            try:
                for project in client.list_projects(
                    request={"parent": container, "show_deleted": False}
                ):
                    all_projects.append({  # <-- REMOVED ACTIVE PROJECT CHECK
                        "project_id": project.project_id,
                        "project_number": project.name.split('/')[-1],
                        "name": project.display_name,
                        "parent": project.parent
                    })
            except Exception as e:
                log(f"Error listing projects in {container}: {str(e)}")

            # Process folders
            try:
                for folder in folders_client.list_folders(
                    request={"parent": container}
                ):
                    if folder.name not in seen_folders:
                        seen_folders.add(folder.name)
                        process_container(folder.name)  # Recursive call
            except Exception as e:
                log(f"Error listing folders in {container}: {str(e)}")

        # Start with organization root
        process_container(f"organizations/{organization_id}")
        
        return all_projects, None

    except Exception as e:
        log(f"Failed to list projects: {str(e)}")
        return None, f"Project listing failed: {str(e)}"

def main():
    """Main application entry point"""
    global credentials, project_id, steampipe_process, AUTH_TYPE
    
    log("Starting application initialization...")
    
    # Initialize credentials first
    log("Initializing credentials...")
    credentials, project_id = get_service_credentials()
    log(f"Credentials initialized. Project ID: {project_id}")

    # Get organization ID for proper project discovery
    org_id = None
    try:
        log("Attempting to detect organization ID...")
        org_id, error = get_gcp_organization_id()
        if org_id:
            log(f"Successfully detected organization ID: {org_id}")
        elif error:
            log(f"Could not determine organization ID: {error}", severity="WARNING")
            log("Will fall back to search_projects API for project discovery", severity="INFO")
    except Exception as e:
        log(f"Error getting organization ID: {str(e)}", severity="ERROR")
        log("Will fall back to search_projects API for project discovery", severity="INFO")

    # Generate Steampipe configuration with credentials
    log("Generating Steampipe configuration...")
    is_cloud_run = os.getenv('K_SERVICE') is not None
    config_path = run_steampipe.generate_and_save_config(
        credentials=credentials, 
        is_cloud_run=is_cloud_run,
        organization_id=org_id  # Pass org_id if available, or None
    )
    
    # Start Steampipe service with three attempts
    if not init_steampipe_service():
       log("CRITICAL: Failed to start Steampipe service after 3 attempts. Exiting.")
       sys.exit(1)
    
    # Start Flask application
    log("Starting Flask application on port 8080...")
    app.run(host='0.0.0.0', port=8080, debug=False)

if __name__ == '__main__':
    main()