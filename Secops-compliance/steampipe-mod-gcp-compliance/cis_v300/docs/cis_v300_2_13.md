## Description

GCP Cloud Asset Inventory is services that provides a historical view of GCP resources and IAM policies through a time-series database. The information recorded includes metadata on Google Cloud resources, metadata on policies set on Google Cloud projects or resources, and runtime information gathered within a Google Cloud resource.

Cloud Asset Inventory Service (CAIS) API enablement is not required for operation of the service, but rather enables the mechanism for searching/exporting CAIS asset data directly.

The GCP resources and IAM policies captured by GCP Cloud Asset Inventory enables security analysis, resource change tracking, and compliance auditing.

It is recommended GCP Cloud Asset Inventory be enabled for all GCP projects.

## Remediation

### From Console

Enable the Cloud Asset API:

1. Go to `API & Services/Library` by visiting [https://console.cloud.google.com/apis/library](https://console.cloud.google.com/apis/library).
2. Search for `Cloud Asset API` and select the result for *Cloud Asset API*.
3. Click the `ENABLE` button.

### From Command Line

Enable the Cloud Asset API:

1. Enable the Cloud Asset API through the services interface:

```bash
gcloud services enable cloudasset.googleapis.com
```

### Default Value

The Cloud Asset Inventory API is disabled by default in each project.
