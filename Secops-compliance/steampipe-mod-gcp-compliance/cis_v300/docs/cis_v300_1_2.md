## Description

Setup multi-factor authentication for Google Cloud Platform accounts.

Multi-factor authentication requires more than one mechanism to authenticate a user. This secures user logins from attackers exploiting stolen or weak credentials.

## Remediation

### From Console

For each Google Cloud Platform project:

  1. Identify non-service accounts.
  2. Setup multi-factor authentication for each account.

### Default Value

By default, multi-factor authentication is not set.