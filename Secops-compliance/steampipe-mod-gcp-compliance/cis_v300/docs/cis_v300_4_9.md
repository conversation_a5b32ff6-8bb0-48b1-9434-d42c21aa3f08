## Description

Compute instances should not be configured to have external IP addresses.

To reduce your attack surface, Compute instances should not have public IP addresses. Instead, instances should be configured behind load balancers, to minimize the instance's exposure to the internet.

## Remediation

To be able turn on Shielded VM on an instance, your instance must use an image with Shielded VM support.

### From Console

1. Gotothe `VM instances` page by visiting: [https://console.cloud.google.com/compute/instances](https://console.cloud.google.com/compute/instances).
2. Click on the instance name to go the the `Instance detail page`.
3. Click `Edit`.
4. For each Network interface, ensure that `External IP` is set to `None`.
5. Click `Done` and then click `Save`.

### From Command Line

1. Describe the instance properties:

```bash
gcloud compute instances describe <INSTANCE_NAME> --zone=<ZONE>
```

2. Identify the access config name that contains the external IP address. This access config appears in the following format:

```
networkInterfaces:
- accessConfigs:
  - kind: compute#accessConfig
    name: External NAT
    natIP: **************
    type: ONE_TO_ONE_NAT
```

3. Delete the access config.

```bash
gcloud compute instances delete-access-config <INSTANCE_NAME> --zone=<ZONE> --access-config-name <ACCESS_CONFIG_NAME>
```

In the above example, the `ACCESS_CONFIG_NAME` is `External NAT`. The name of your access config might be different. The name of your access config might be different.

### Prevention

You can configure the `Define allowed external IPs for VM instances` Organization Policy to prevent VMs from being configured with public IP addresses. Learn more at: [https://console.cloud.google.com/orgpolicies/compute-vmExternalIpAccess](https://console.cloud.google.com/orgpolicies/compute-vmExternalIpAccess)

### Default Value

By default, Compute instances have a public IP address.
