## Description

Cloud DNS logging records the queries from the name servers within your VPC to <PERSON><PERSON>dr<PERSON>. Logged queries can come from Compute Engine VMs, GKE containers, or other GCP resources provisioned within the VPC.

Security monitoring and forensics cannot depend solely on IP addresses from VPC flow logs, especially when considering the dynamic IP usage of cloud resources, HTTP virtual host routing, and other technology that can obscure the DNS name used by a client from the IP address.

Monitoring of Cloud DNS logs provides visibility to DNS names requested by the clients within the VPC. These logs can be monitored for anomalous domain names, evaluated against threat intelligence , and

Note: For full capture of DNS, firewall must block egress UDP/53 (DNS) and TCP/443 (DNS over HTTPS) to prevent client from using external DNS name server for resolution.

## Remediation

### From Command Line

**Add New DNS Policy With Logging Enabled**

For each VPC network that needs a DNS policy with logging enabled:

```bash
gcloud dns policies create enable-dns-logging --enable-logging --description="Enable DNS Logging" --networks=VPC_NETWORK_NAME
```

The VPC_NETWORK_NAME can be one or more networks in comma-separated list

**Enable Logging for Existing DNS Policy**
For each VPC network that has an existing DNS policy that needs logging enabled:

```bash
gcloud dns policies update POLICY_NAME --enable-logging --networks=VPC_NETWORK_NAME
```

The VPC_NETWORK_NAME can be one or more networks in comma-separated list

### Default Value

Cloud DNS logging is disabled by default on each network.
