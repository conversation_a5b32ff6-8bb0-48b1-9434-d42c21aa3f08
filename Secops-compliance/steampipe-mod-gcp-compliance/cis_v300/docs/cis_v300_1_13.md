## Description

API Keys should only be used for services in cases where other authentication methods are unavailable. In this case, unrestricted keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to restrict API key usage to trusted hosts, HTTP referrers and apps. It is recommended to use the more secure standard authentication flow instead.

Security risks involved in using API-Keys appear below:

- API keys are simple encrypted strings
- API keys do not identify the user or the application making the API request
- API keys are typically accessible to clients, making it easy to discover and steal an API key

In light of these potential risks, Google recommends using the standard authentication flow instead of API keys. However, there are limited cases where API keys are more appropriate. For example, if there is a mobile application that needs to use the Google Cloud Translation API, but doesn't otherwise need a backend server, API keys are the simplest way to authenticate to that API.

In order to reduce attack vectors, API-Keys can be restricted only to trusted hosts, HTTP referrers and applications.

## Remediation

### From Console

### *Leaving Keys in Place*

1. Go to `APIs & Services\Credentials` using [https://console.cloud.google.com/apis/credentials](https://console.cloud.google.com/apis/credentials).
2. In the section `API Keys`, Click the `API Key Name`. The API Key properties display on a new page.
3. In the `Key restrictions` section, set the application restrictions to any of `HTTP referrers, IP addresses, Android apps, iOS apps`.
4. Click `Save`.
5. Repeat steps 2,3,4 for every unrestricted API key.

**Note**: Do not set `HTTP referrers` to wild-cards (* or *.[TLD] or .[TLD]/) allowing access to any/wide HTTP referrer(s) Do not set `IP addresse`s and referrer to `any host (0.0.0.0 or 0.0.0.0/0 or ::0)`

### *Removing Keys*

Another option is to remove the keys entirely.

1. Go to `APIs & Services\Credentials` using [https://console.cloud.google.com/apis/credentials](https://console.cloud.google.com/apis/credentials).
2. In the section `API Keys`, select the check box next to each key you wish to remove.
3. Select `Delete` and confirm.

### Default Value

By default, `Application Restrictions` are set to `None`.
