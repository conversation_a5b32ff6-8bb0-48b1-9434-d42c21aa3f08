## Description

It is recommended that Essential Contacts is configured to designate email addresses for Google Cloud services to notify of important technical or security information.

Many Google Cloud services, such as Cloud Billing, send out notifications to share important information with Google Cloud users. By default, these notifications are sent to members with certain Identity and Access Management (IAM) roles. With Essential Contacts, you can customize who receives notifications by providing your own list of contacts.

## Remediation

### From Console

1. Go to `Essential Contacts` by visiting [https://console.cloud.google.com/iam-admin/essential-contacts](https://console.cloud.google.com/iam-admin/essential-contacts).
2. Make sure the organization appears in the resource selector at the top of the page. The resource selector tells you what project, folder, or organization you are currently managing contacts for.
3. Click `+Add contact`.
4. In the `Email` and `Confirm Email` fields, enter the email address of the contact.
5. From the `Notification categories` drop-down menu, select the notification categories that you want the contact to receive communications for.
6. Click `Save`.

### From Command Line

1. To add an organization Essential Contacts run a command:

```bash
gcloud essential-contacts create --email="<EMAIL>" \--notification-categories="<NOTIFICATION_CATEGORIES>" \--organization=<ORGANIZATION_ID>
```

### Default Value

By default, there are no Essential Contacts configured.
In the absence of an Essential Contact, the following IAM roles are used to identify users to notify for the following categories:

- Legal: `roles/billing.admin`
- Security: `roles/resourcemanager.organizationAdmin`
- Suspension: `roles/owner`
- Technical: `roles/owner`
- Technical Incidents: `roles/owner`
