## Description

It is recommended that the IAM policy on BigQuery datasets does not allow anonymous and/or public access.

Granting permissions to `allUsers` or `allAuthenticatedUsers` allows anyone to access the dataset. Such access might not be desirable if sensitive data is being stored in the dataset. Therefore, ensure that anonymous and/or public access to a dataset is not allowed.

## Remediation

### From Console

1. Go to `BigQuery` by visiting: [https://console.cloud.google.com/bigquery](https://console.cloud.google.com/bigquery).
2. Select the dataset from 'Resources'.
3. Click `SHARING` near the right side of the window and select `Permissions`.
4. Review each attached role.
5. Click the delete icon for each member `allUsers` or `allAuthenticatedUsers`. On the popup click `Remove`.

### From Command Line

List the name of all datasets.

```bash
bq ls
```

Retrieve the data set details:

```bash
bq show --format=prettyjson PROJECT_ID:DATASET_NAME > PATH_TO_FILE
```

In the access section of the JSON file, update the dataset information to remove all roles containing `allUsers` or `allAuthenticatedUsers`.

Update the dataset:

```bash
bq update --source PATH_TO_FILE PROJECT_ID:DATASET_NAME
```

### Prevention

You can prevent Bigquery dataset from becoming publicly accessible by setting up the `Domain restricted sharing` organization policy at: [https://console.cloud.google.com/iam-admin/orgpolicies/iam-allowedPolicyMemberDomains](https://console.cloud.google.com/iam-admin/orgpolicies/iam-allowedPolicyMemberDomains).

### Default Value

By default, BigQuery datasets are not publicly accessible.
