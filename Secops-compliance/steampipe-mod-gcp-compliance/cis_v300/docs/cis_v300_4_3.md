## Description

It is recommended to use Instance specific SSH key(s) instead of using common/shared project-wide SSH key(s) to access Instances.

Project-wide SSH keys are stored in Compute/Project-meta-data. Project wide SSH keys can be used to login into all the instances within project. Using project-wide SSH keys eases the SSH key management but if compromised, poses the security risk which can impact all the instances within project. It is recommended to use Instance specific SSH keys which can limit the attack surface if the SSH keys are compromised.

## Remediation

### From Console

1. Go to the `VM instances` page by visiting: [https://console.cloud.google.com/compute/instances](https://console.cloud.google.com/compute/instances). It will list all the instances in your project.
2. Click on the name of the Impacted instance.
3. Click `Edit` in the toolbar.
4. Under SSH Keys, go to the `Block project-wide SSH keys` checkbox.
5. To block users with project-wide SSH keys from connecting to this instance, select `Block project-wide SSH keys`.
6. Click `Save` at the bottom of the page.
7. Repeat steps for every impacted Instance.

### From Command Line

To block project-wide public SSH keys, set the metadata value to `TRUE`:

```bash
gcloud compute instances add-metadata <INSTANCE_NAME> --metadata block- project-ssh-keys=TRUE
```

### Default Value

By Default `Block Project-wide SSH keys` is not enabled.
