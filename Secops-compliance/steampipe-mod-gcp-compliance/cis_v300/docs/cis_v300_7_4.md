## Description

BigQuery tables can contain sensitive data that for security purposes should be discovered, monitored, classified, and protected. Google Cloud's Sensitive Data Protection tools can automatically provide data classification of all BigQuery data across an organization.

Using a cloud service or 3rd party software to continuously monitor and automate the process of data discovery and classification for BigQuery tables is an important part of protecting the data.

Sensitive Data Protection is a fully managed data protection and data privacy platform that uses machine learning and pattern matching to discover and classify sensitive data in Google Cloud.

## Remediation

### Enable profiling:

1. Go to Cloud DLP by visiting https://console.cloud.google.com/dlp/landing/dataProfiles/configurations.
2. Click "Create Configuration".
3. For projects follow https://cloud.google.com/dlp/docs/profile-project. For organizations or folders follow https://cloud.google.com/dlp/docs/profile-org-folder.

### Review findings:

- Columns or tables with high data risk have evidence of sensitive information without additional protections. To lower the data risk score, consider doing the following:
- For columns containing sensitive data, apply a BigQuery policy tag to restrict access to accounts with specific access rights.
- De-identify the raw sensitive data using de-identification techniques like masking and tokenization.

### Incorporate findings into your security and governance operations:

- Enable sending findings into your security and posture services. You can publish data profiles to Security Command Center and Chronicle.
- Automate remediation or enable alerting of new or changed data risk with Pub/Sub.