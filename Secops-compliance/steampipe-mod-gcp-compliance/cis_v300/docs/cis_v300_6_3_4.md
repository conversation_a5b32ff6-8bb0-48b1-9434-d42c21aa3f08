## Description

It is recommended that, `user options` database flag for Cloud SQL SQL Server instance should not be configured.

The `user options` option specifies global defaults for all users. A list of default query processing options is established for the duration of a user's work session. The user options option allows you to change the default values of the SET options (if the server's default settings are not appropriate).

A user can override these defaults by using the SET statement. You can configure user options dynamically for new logins. After you change the setting of user options, new login sessions use the new setting; current login sessions are not affected. This recommendation is applicable to SQL Server database instances.

## Remediation

### From Console

1. Go to the Cloud SQL Instances page in the Google Cloud Console by visiting [https://console.cloud.google.com/sql/instances](https://console.cloud.google.com/sql/instances).
2. Select the SQL Server instance for which you want to enable to database flag.
3. Click `Edit`.
4. Scroll down to the `Flags` section.
5. Click the X next `user options` flag shown.
6. Click `Save` to save your changes.
7. Confirm your changes under `Flags` on the Overview page.

### From Command Line

1. List all Cloud SQL database Instances

```bash
gcloud sql instances list
```

2. Clear the `user options` database flag for every Cloud SQL SQL Server database instance using either of the below commands.

Clearing all flags to their default value

```bash
gcloud sql instances patch <INSTANCE_NAME> --clear-database-flags
```

OR

To clear only `user options` database flag, configure the database flag by overriding the `user options`. Exclude `user options` flag and its value, and keep all other flags you want to configure.

```bash
gcloud sql instances patch <INSTANCE_NAME> --database-flags [FLAG1=VALUE1,FLAG2=VALUE2]
```

**Note:** This command will overwrite all database flags previously set. To keep those and add new ones, include the values for all flags you want set on the instance; any flag not specifically included is set to its default value. For flags that do not take a value, specify the flag name followed by an equals sign ("=").

### Default Value

By default 'user options' is not configured.