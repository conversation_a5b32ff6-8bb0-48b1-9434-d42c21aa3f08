## Description

It is recommended to configure your instance to not use the default Compute Engine service account because it has the Editor role on the project.

The default Compute Engine service account has the Editor role on the project, which allows read and write access to most Google Cloud Services. To defend against privilege escalations if your VM is compromised and prevent an attacker from gaining access to all of your project, it is recommended to not use the default Compute Engine service account. Instead, you should create a new service account and assigning only the permissions needed by your instance.

The default Compute Engine service account is named `[PROJECT_NUMBER]- <EMAIL>`.

## Remediation

### From Console

1. Go to the `VM instances` page by visiting: [https://console.cloud.google.com/compute/instances](https://console.cloud.google.com/compute/instances).
2. Click on the instance name to go to its `VM instance details` page.
3. Click `STOP` and then click `EDIT`.
4. Under the section `API and identity management`, select a service account other than the default Compute Engine service account. You may first need to create a new service account.
5. Click `Save` and then click `START`.

### From Command Line

1. Stop the instance:

```bash
gcloud compute instances stop <INSTANCE_NAME>
```

2. Update the instance:

```bash
gcloud compute instances set-service-account <INSTANCE_NAME> --serviceaccount=<SERVICE_ACCOUNT>
```

3. Restart the instance:

```bash
gcloud compute instances start <INSTANCE_NAME>
```

### Default Value

By default, Compute instances are configured to use the default Compute Engine service account.
