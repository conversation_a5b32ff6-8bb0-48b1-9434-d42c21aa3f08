## Description

It is recommended to enforce all incoming connections to SQL database instance to use SSL.

SQL database connections if successfully trapped (MITM); can reveal sensitive data like credentials, database queries, query outputs etc. For security, it is recommended to always use SSL encryption when connecting to your instance. This recommendation is applicable for Postgresql, MySql generation 1, MySql generation 2 and SQL Server 2017 instances.

## Remediation

### From Console

1. Go to [https://console.cloud.google.com/sql/instances](https://console.cloud.google.com/sql/instances).
2. Click on an instance name to see its configuration overview.
3. In the left-side panel, select `Connections`.
4. In the `security` section, select SSL mode as `Allow only SSL connections`.
5. Under `Configure SSL server certificates` click `Create new certificate` and save the setting.

### From Command Line

1. To enforce SSL encryption for an instance run the command:

```bash
gcloud sql instances patch INSTANCE_NAME --ssl-mode= ENCRYPTED_ONLY
```

**Note**:`RESTART` is required for type MySQL Generation 1 Instances `(backendType: FIRST_GEN)` to get this configuration in effect.

### Default Value

By default parameter `settings: ipConfiguration: requireSsl` is not set which is equivalent to `sslMode:ALLOW_UNENCRYPTED_AND_ENCRYPTED.`.