## Description

Interacting with a serial port is often referred to as the serial console, which is similar to using a terminal window, in that input and output is entirely in text mode and there is no graphical interface or mouse support.

If you enable the interactive serial console on an instance, clients can attempt to connect to that instance from any IP address. Therefore interactive serial console support should be disabled.

A virtual machine instance has four virtual serial ports. Interacting with a serial port is similar to using a terminal window, in that input and output is entirely in text mode and there is no graphical interface or mouse support. The instance's operating system, BIOS, and other system-level entities often write output to the serial ports, and can accept input such as commands or answers to prompts. Typically, these system-level entities use the first serial port (port 1) and serial port 1 is often referred to as the serial console.

The interactive serial console does not support IP-based access restrictions such as IP whitelists. If you enable the interactive serial console on an instance, clients can attempt to connect to that instance from any IP address. This allows anybody to connect to that instance if they know the correct SSH key, username, project ID, zone, and instance name.

Therefore interactive serial console support should be disabled.

## Remediation

Interactive serial console support should be disabled.

### From Console

1. Login to Google Cloud console.
2. Go to Computer Engine.
3. Go to VMinstances.
4. Click on the Specific VM.
5. Click `EDIT`.
6. Unselect `Enable connecting to serial ports` below `Remote access` block.
7. Click `Save`.

### From Command Line

Use the below command to disable

```bash
gcloud compute instances add-metadata INSTANCE_NAME --zone=ZONE --metadata=serial-port-enable=false
```

or

```bash
gcloud compute instances add-metadata <INSTANCE_NAME> --zone=<ZONE> --metadata=serial-port-enable=0
```

### Prevention

You can prevent VMs from having serial port access enable by `Disable VM serial port access` organization policy:
[https://console.cloud.google.com/iam-admin/orgpolicies/compute-disableSerialPortAccess](https://console.cloud.google.com/iam-admin/orgpolicies/compute-disableSerialPortAccess).

### Default Value

By default, connecting to serial ports is not enabled.
