## Description

API Keys should only be used for services in cases where other authentication methods are unavailable. API keys are always at risk because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to restrict API keys to use(call) only APIs required by an application.

Security risks involved in using API-Keys appear below:

- API keys are simple encrypted strings
- API keys do not identify the user or the application making the API request
- API keys are typically accessible to clients, making it easy to discover and steal an API key

In light of these potential risks, Google recommends using the standard authentication flow instead of API keys. However, there are limited cases where API keys are more appropriate. For example, if there is a mobile application that needs to use the Google Cloud Translation API, but doesn't otherwise need a backend server, API keys are the simplest way to authenticate to that API.

In order to reduce attack surfaces by providing `least privileges`, API-Keys can be restricted to use (call) only APIs required by an application.

## Remediation

### From Console

1. Go to `APIs & Services\Credentials` using [https://console.cloud.google.com/apis/credentials](https://console.cloud.google.com/apis/credentials).
2. In the section `API Keys`, Click the `API Key Name`. The API Key properties display on a new page.
3. In the `Key restrictions` section go to `API restrictions`.
4. Click the `Select API` drop-down to choose an API.
5. Click `Save`.
6. Repeat steps 2,3,4,5 for every unrestricted API key.

**Note**: Do not set `API restrictions` to `Google Cloud APIs`, as this option allows access to all services offered by Google cloud.

### From Command Line

1. List all API keys.

```bash
gcloud services api-keys list
```

**Note** the `UID` of the key to add restrictions to.

2. Run the update command with the appropriate flags to add the required restrictions.

```bash
gcloud alpha services api-keys update <UID> <restriction_flags>
```

**Note** - Flags can be found by running

```bash
gcloud alpha services api-keys update --help
```

or in this documentation [https://cloud.google.com/sdk/gcloud/reference/alpha/services/api-keys/update](https://cloud.google.com/sdk/gcloud/reference/alpha/services/api-keys/update)

### Default Value

By default, `API restrictions` are set to `None`.
