steps:
  - name: 'node:18'
    args:
      - '-c'
      - |
        echo "Installing dependencies in $(pwd)"
        npm install
        npm install web-vitals --save
        npm install lucide-react
        npm install --save-dev @babel/plugin-proposal-private-property-in-object
    dir: frontend
    entrypoint: bash
  - name: 'node:18'
    args:
      - '-c'
      - |
        echo "Building application in $(pwd)"
        npm run build
    dir: frontend
    entrypoint: bash
  - name: gcr.io/cloud-builders/docker
    args:
      - build
      - '-t'
      - gcr.io/$PROJECT_ID/steampipe-service-frontend
      - '-f'
      - frontend/Dockerfile
      - ./frontend
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - gcr.io/$PROJECT_ID/steampipe-service-frontend
  - name: gcr.io/cloud-builders/gcloud
    args:
      - run
      - deploy
      - steampipe-service-frontend
      - '--image'
      - gcr.io/$PROJECT_ID/steampipe-service-frontend
      - '--region'
      - us-central1
      - '--platform'
      - managed
      - '--allow-unauthenticated'
      - '--port'
      - '8081'
      - '--set-env-vars'
      - 'BACKEND_GCP_URL=https://steampipe-service-gcp-$PROJECT_NUMBER.${_REGION}.run.app'
      - '--set-env-vars'
      - 'BACKEND_AWS_URL=https://steampipe-service-aws-$PROJECT_NUMBER.${_REGION}.run.app'
      - '--set-env-vars'
      - 'BACKEND_AZURE_URL=https://steampipe-service-azure-$PROJECT_NUMBER.${_REGION}.run.app'        
options:
  logging: CLOUD_LOGGING_ONLY
substitutions:
  _PROJECT_ID: '816268782019'
  _REGION: us-central1
