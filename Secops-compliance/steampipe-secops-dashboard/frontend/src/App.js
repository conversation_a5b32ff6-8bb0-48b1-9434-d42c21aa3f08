import React, { useState } from "react";
import BenchmarkDashboard from './Components/Dashboard';
import GenerateControl from './Components/GenerateControl'; // Import the new component

function App() {
  const [activeTab, setActiveTab] = useState('dashboard'); // State to manage the active tab

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      <div className="p-6 max-w-7xl mx-auto">
        {/* Tab Navigation */}
        <div className="mb-4 border-b border-gray-200 dark:border-gray-700">
          <ul className="flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500 dark:text-gray-400">
            <li className="mr-2">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${
                  activeTab === 'dashboard'
                    ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500'
                    : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
                }`}
                onClick={() => setActiveTab('dashboard')}
              >
                Benchmark Dashboard
              </button>
            </li>
            <li className="mr-2">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${
                  activeTab === 'generate'
                    ? 'border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500'
                    : 'hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300'
                }`}
                onClick={() => setActiveTab('generate')}
              >
                Generate Control
              </button>
            </li>
          </ul>
        </div>

        {/* Tab Content */}
        {activeTab === 'dashboard' && <BenchmarkDashboard />}
        {activeTab === 'generate' && <GenerateControl />}
      </div>
    </div>
  );
}

export default App;