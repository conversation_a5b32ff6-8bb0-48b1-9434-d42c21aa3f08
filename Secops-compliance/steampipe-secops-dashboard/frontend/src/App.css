/* Tailwind overrides for highlight.js syntax highlighting */
.hljs-keyword {
    @apply text-blue-400 font-bold; /* Example for keyword color */
  }
  
  .hljs-string {
    @apply text-yellow-400; /* Example for string color */
  }
  
  .hljs-comment {
    @apply text-green-400 italic; /* Example for comment color */
  }
  
  .hljs-function {
    @apply text-purple-500; /* Example for function name color */
  }
  
  .hljs-number {
    @apply text-orange-400; /* Example for number color */
  }
  
  .hljs-variable {
    @apply text-pink-500; /* Example for variable color */
  }
  