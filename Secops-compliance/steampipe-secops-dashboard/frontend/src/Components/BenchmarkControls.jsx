import React from 'react';
import { X, AlertCircle, CheckCircle, Info, SkipForward, ChevronDown, ChevronRight } from 'lucide-react';

const StatusIcon = ({ status, className = "" }) => {
  switch (status?.toLowerCase()) {
    case 'ok': return <CheckCircle className={`${className} text-green-500`} />;
    case 'alarm': return <AlertCircle className={`${className} text-red-500`} />;
    case 'error': return <AlertCircle className={`${className} text-gray-500`} />;
    case 'info': return <Info className={`${className} text-blue-500`} />;
    case 'skip': return <SkipForward className={`${className} text-gray-500`} />;
    default: return null;
  }
};

const BenchmarkControl = ({ control }) => {
  const [isExpanded, setIsExpanded] = React.useState(false);

  // Format tags for display
  const formattedTags = control.tags ? Object.entries(control.tags).map(([key, value]) => ({
    key,
    value: typeof value === 'boolean' ? value.toString() : value
  })) : [];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 dark:hover:bg-gray-700 rounded-t-lg"
      >
        <div className="flex items-start gap-3">
          {isExpanded ? 
            <ChevronDown className="w-5 h-5 text-gray-400 mt-1" /> : 
            <ChevronRight className="w-5 h-5 text-gray-400 mt-1" />
          }
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {control.title}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Control ID: {control.control_id}
            </p>
          </div>
        </div>
        
        {control.summary && (
          <div className="flex gap-2">
            {Object.entries(control.summary).map(([status, count]) => 
              count > 0 && (
                <div key={status} className="flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full">
                  <StatusIcon status={status} className="w-4 h-4" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {count}
                  </span>
                </div>
              )
            )}
          </div>
        )}
      </button>

      {isExpanded && (
        <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
          {control.description && (
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              {control.description}
            </p>
          )}

          {formattedTags.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</h4>
              <div className="flex flex-wrap gap-2">
                {formattedTags.map(({ key, value }) => (
                  <span key={key} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                    {key}: {value}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* {control.results && control.results.length > 0 && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Results</h4>
              {control.results.map((result, index) => (
                <div key={index} className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="flex items-start gap-2">
                    <StatusIcon status={result.status} className="w-5 h-5 mt-1" />
                    <div>
                      <p className="text-sm text-gray-900 dark:text-white">{result.reason}</p>
                      {result.resource && (
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Resource: <code className="px-2 py-0.5 bg-gray-100 dark:bg-gray-600 rounded">{result.resource}</code>
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {control.run_error && (
            <div className="mt-2 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
              <p className="text-sm text-red-600 dark:text-red-400">
                Error: {control.run_error}
              </p>
            </div>
          )} */}
        </div>
      )}
    </div>
  );
};

const BenchmarkControls = ({ data, isOpen, onClose }) => {
  // Extract controls from nested groups
  const extractControls = (groups) => {
    let controls = [];
    if (!groups) return controls;
    
    groups.forEach(group => {
      if (group.controls) {
        controls = [...controls, ...group.controls];
      }
      if (group.groups) {
        controls = [...controls, ...extractControls(group.groups)];
      }
    });
    
    return controls;
  };

  const controls = data?.benchmark_results?.groups ? 
    extractControls(data.benchmark_results.groups) : [];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] flex flex-col">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            Benchmark Controls
          </h2>
          <button 
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <X className="w-6 h-6 text-gray-500 dark:text-gray-400" />
          </button>
        </div>
        
        <div className="overflow-y-auto flex-1 p-4">
          <div className="space-y-4">
            {controls.map((control) => (
              <BenchmarkControl 
                key={control.control_id} 
                control={control}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BenchmarkControls;