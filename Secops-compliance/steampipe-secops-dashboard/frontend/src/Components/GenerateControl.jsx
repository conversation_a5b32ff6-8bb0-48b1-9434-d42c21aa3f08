// src/Components/GenerateControl.jsx
import React, { useState, useEffect } from 'react';
import { Loader2, CheckCircle, Cloud, CloudCog, CloudLightning } from 'lucide-react';

const GenerateControl = () => {
  const [prompt, setPrompt] = useState('');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [error, setError] = useState(null);
  const [testSuccess, setTestSuccess] = useState(false);
  const [testResult, setTestResult] = useState("");
  const [activeProvider, setActiveProvider] = useState('gcp');

  // Providers configuration
  const providers = [
    { 
      id: 'gcp', 
      name: 'GCP', 
      color: 'from-blue-500 to-blue-600',
      icon: Cloud,
      iconColor: 'text-blue-500',
      generateEndpoint: '/api/gcp/generate',
      testEndpoint: '/api/gcp/testcontrol'
    },
    { 
      id: 'aws', 
      name: 'AWS', 
      color: 'from-orange-500 to-orange-600',
      icon: CloudCog,
      iconColor: 'text-orange-500',
      generateEndpoint: '/api/aws/generate',
      testEndpoint: '/api/aws/testcontrol'
    },
    { 
      id: 'azure', 
      name: 'Azure', 
      color: 'from-blue-600 to-blue-700',
      icon: CloudLightning,
      iconColor: 'text-blue-600',
      generateEndpoint: '/api/azure/generate',
      testEndpoint: '/api/azure/testcontrol'
    }
  ];

  // Find the current provider
  const currentProvider = providers.find(p => p.id === activeProvider) || providers[0];

  const handleGenerate = async () => {
    if (!prompt) {
      setError("Please enter a prompt.");
      return;
    }

    setLoading(true);
    setError(null);
    setResponse('');
    setTestSuccess(false);
    setTestResult("");

    try {
      const res = await fetch(currentProvider.generateEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          prompt,
          provider: activeProvider // Pass the active provider to backend
        }),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || `HTTP error! status: ${res.status}`);
      }

      const data = await res.json();
      setResponse(data.response);

    } catch (err) {
      console.error('Benchmark error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleTestControl = async () => {
    setTestLoading(true);
    setError(null);
    setTestSuccess(false);
    setTestResult("");

    try {
      const res = await fetch(currentProvider.testEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          provider: activeProvider // Pass the active provider to backend
        }),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || `HTTP error! Status: ${res.status}`);
      }
    
      const data = await res.json();
      setTestResult(JSON.stringify(data, null, 2));

      // Determine success based on the response structure
      // This may need to be adjusted based on the actual response format
      if (data && Array.isArray(data) && data.every(item => item.status === 'ok')) {
        setTestSuccess(true);
      } else {
        setTestSuccess(false);
      }

    } catch (err) {
      setError(err.message);
      setTestSuccess(false);
    } finally {
      setTestLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">Generate Control</h2>
        
        {/* Cloud Provider Tabs */}
        <div className="flex space-x-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          {providers.map((provider) => {
            const IconComponent = provider.icon;
            const isActive = activeProvider === provider.id;
            
            return (
              <button
                key={provider.id}
                onClick={() => setActiveProvider(provider.id)}
                className={`px-3 py-2 rounded-md flex items-center gap-2 transition-all duration-200 ${
                  isActive 
                    ? `bg-gradient-to-r ${provider.color} text-white shadow-sm`
                    : `text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 ${provider.iconColor}`
                }`}
              >
                <IconComponent className="w-4 h-4" />
                <span className="text-xs font-medium">{provider.name}</span>
              </button>
            );
          })}
        </div>
      </div>

      <div className="mb-4">
        <label htmlFor="prompt" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Prompt:
        </label>
        <textarea
          id="prompt"
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          className="mt-1 block w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          placeholder={`Enter your prompt to generate a ${activeProvider.toUpperCase()} control...`}
          rows="4"
        />
      </div>

      <div className="flex gap-2">
        <button
          onClick={handleGenerate}
          disabled={loading || testLoading}
          className={`px-4 py-2 text-white rounded-md hover:opacity-90 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2 ${
            !loading ? `bg-gradient-to-r ${currentProvider.color}` : 'bg-gray-400'
          }`}
        >
          {loading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <currentProvider.icon className="w-4 h-4" />
              Generate {currentProvider.name} Control
            </>
          )}
        </button>

        <button
          onClick={handleTestControl}
          disabled={loading || testLoading || !response}
          className={`px-4 py-2 text-white rounded-md flex items-center gap-2
                    ${response && !testLoading ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-400 cursor-not-allowed'}
                    `}
        >
          {testLoading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              Testing...
            </>
          ) : (
            <>
              {testSuccess && <CheckCircle className="w-4 h-4" />}
              Test Control
            </>
          )}
        </button>
      </div>

      {error && (
        <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 text-red-500 rounded-md">
          {error}
        </div>
      )}

      {response && (
        <div className="mt-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Generated {currentProvider.name} Control:
          </h3>
          <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded-md overflow-x-auto text-sm text-gray-700 dark:text-gray-300">
            <code>
              {response}
            </code>
          </pre>
        </div>
      )}
      
      {testResult && (
        <div className="mt-4">
          <h3 className={`text-lg font-semibold mb-2 ${testSuccess ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
            Test Results:
          </h3>
          <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded-md overflow-x-auto text-sm text-gray-700 dark:text-gray-300">
            <code>{testResult}</code>
          </pre>
        </div>
      )}
    </div>
  );
};

export default GenerateControl;