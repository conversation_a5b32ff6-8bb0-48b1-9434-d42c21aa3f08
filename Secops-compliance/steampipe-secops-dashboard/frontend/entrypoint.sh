#!/bin/sh
echo "=== Nginx Frontend Startup ==="
echo "Current environment variables:"

# Check and display GCP backend
if [ -n "$BACKEND_GCP_URL" ]; then
    echo "BACKEND_GCP_URL: ${BACKEND_GCP_URL}"
    curl -v "$BACKEND_GCP_URL"
    sed -i "s|BACKEND1_URL_PLACEHOLDER|$BACKEND_GCP_URL|g" /etc/nginx/nginx.conf
else
    echo "BACKEND_GCP_URL is not set"
    sed -i "s|BACKEND1_URL_PLACEHOLDER|http://localhost:8080|g" /etc/nginx/nginx.conf
fi

# Check and display AWS backend
if [ -n "$BACKEND_AWS_URL" ]; then
    echo "BACKEND_AWS_URL: ${BACKEND_AWS_URL}"
    curl -v "$BACKEND_AWS_URL"
    sed -i "s|BACKEND2_URL_PLACEHOLDER|$BACKEND_AWS_URL|g" /etc/nginx/nginx.conf
else
    echo "BACKEND_AWS_URL is not set"
    sed -i "s|BACKEND2_URL_PLACEHOLDER|http://localhost:8082|g" /etc/nginx/nginx.conf
fi

# Check and display Azure backend
if [ -n "$BACKEND_AZURE_URL" ]; then
    echo "BACKEND_AZURE_URL: ${BACKEND_AZURE_URL}"
    curl -v "$BACKEND_AZURE_URL"
    sed -i "s|BACKEND3_URL_PLACEHOLDER|$BACKEND_AZURE_URL|g" /etc/nginx/nginx.conf
else
    echo "BACKEND_AZURE_URL is not set"
    sed -i "s|BACKEND3_URL_PLACEHOLDER|http://localhost:8083|g" /etc/nginx/nginx.conf
fi

echo "Nginx config after replacement:"
grep -A 3 "proxy_pass" /etc/nginx/nginx.conf

# Print a confirmation message
echo "Starting Nginx with backend URLs:"
[ -n "$BACKEND_GCP_URL" ] && echo "GCP: ${BACKEND_GCP_URL}" || echo "GCP: Not configured"
[ -n "$BACKEND_AWS_URL" ] && echo "AWS: ${BACKEND_AWS_URL}" || echo "AWS: Not configured"
[ -n "$BACKEND_AZURE_URL" ] && echo "Azure: ${BACKEND_AZURE_URL}" || echo "Azure: Not configured"

# Start nginx
exec nginx -g 'daemon off;'