events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging settings
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Set global timeout values
    client_body_timeout 1000s;
    client_header_timeout 1000s;
    send_timeout 1000s;
    proxy_connect_timeout 1000s;
    proxy_read_timeout 1000s;
    proxy_send_timeout 1000s;

    # Enable keepalive connections
    keepalive_timeout 65;
    keepalive_requests 100;

    # Define upstream servers for your backends (without protocol/http://)
    server {
        listen 8081;
        server_name _;
        
        # Root directory for static files
        root /usr/share/nginx/html;
        index index.html;

        # Health check endpoint for Cloud Run
        location /health {
            access_log off;
            return 200 'healthy\n';
        }

        # Handle frontend routes
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-cache";
        }

        # Proxy requests to the first backend
        location /api/gcp/ {
            resolver ******* ******* valid=300s;
            proxy_pass BACKEND1_URL_PLACEHOLDER;

            proxy_http_version 1.1;
            proxy_set_header Host $proxy_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";

            # Forward the original IAP JWT token
            proxy_set_header X-Original-Iap-Jwt $http_x_goog_iap_jwt_assertion;
            
            # Forward other IAP headers
            # proxy_set_header X-Goog-Iap-Jwt-Assertion $http_x_goog_iap_jwt_assertion;
            # proxy_set_header X-Goog-Authenticated-User-Email $http_x_goog_authenticated_user_email;
            # proxy_set_header X-Goog-Authenticated-User-Id $http_x_goog_authenticated_user_id;

            # Prevent redirect loops
            proxy_redirect off;
            proxy_buffering off;
            proxy_max_temp_file_size 0;

            # Limit retries
            proxy_next_upstream off;

            # CORS headers
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' '*' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
                add_header 'Access-Control-Allow-Headers' '*';
                add_header 'Access-Control-Allow-Credentials' 'true';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }

            proxy_intercept_errors on;
            error_page 502 503 504 @backend_down;
        }

        # Proxy requests to the second backend
        location /api/aws/ {
            resolver ******* ******* valid=300s;
            proxy_pass BACKEND2_URL_PLACEHOLDER;

            proxy_http_version 1.1;
            proxy_set_header Host $proxy_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";


            # Forward the original IAP JWT token
            proxy_set_header X-Original-Iap-Jwt $http_x_goog_iap_jwt_assertion;

            # Prevent redirect loops
            proxy_redirect off;
            proxy_buffering off;
            proxy_max_temp_file_size 0;

            # Limit retries
            proxy_next_upstream off;

            # CORS headers
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' '*' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
                add_header 'Access-Control-Allow-Headers' '*';
                add_header 'Access-Control-Allow-Credentials' 'true';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }

            proxy_intercept_errors on;
            error_page 502 503 504 @backend_down;
        }

        # Proxy requests to the third backend
        location /api/azure/ {
            resolver ******* ******* valid=300s;
            proxy_pass BACKEND3_URL_PLACEHOLDER;

            proxy_http_version 1.1;
            proxy_set_header Host $proxy_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";


            # Forward the original IAP JWT token
            proxy_set_header X-Original-Iap-Jwt $http_x_goog_iap_jwt_assertion;

            # Prevent redirect loops
            proxy_redirect off;
            proxy_buffering off;
            proxy_max_temp_file_size 0;

            # Limit retries
            proxy_next_upstream off;

            # CORS headers
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' '*' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
                add_header 'Access-Control-Allow-Headers' '*';
                add_header 'Access-Control-Allow-Credentials' 'true';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }

            proxy_intercept_errors on;
            error_page 502 503 504 @backend_down;
        }

        # Backend down handler
        location @backend_down {
            default_type application/json;
            return 503 '{"error": "Backend service temporarily unavailable. Please try again later."}';
        }

        # Custom error pages
        error_page 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }
    }
}