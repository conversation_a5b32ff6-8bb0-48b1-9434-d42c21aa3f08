# Deployment configuration for IronFort client
client_name: ironfort
project_id: ${GCP_PROJECT_ID}  # Will be replaced during deployment
region: us-central1

# IAP Configuration
iap:
  enabled: true
  authorized_emails:
    - uday<PERSON><PERSON><EMAIL>
    # Add more authorized emails as needed

# Cloud Run Configuration
services:
  # Backend services (private)
  aws-backend:
    name: ironfort-aws-compliance
    image: gcr.io/${GCP_PROJECT_ID}/steampipe-aws-compliance:latest
    port: 8082
    memory: 2Gi
    cpu: 2
    min_instances: 1
    max_instances: 10
    concurrency: 100
    timeout: 600  # 10 minutes for long-running compliance checks
    environment:
      - name: AUTH_TYPE
        value: secret_manager
      - name: CLIENT_NAME
        value: ironfort
      - name: DOMAIN
        value: https://marketplace.neosec.com
    ingress: internal  # Private service
    
  azure-backend:
    name: ironfort-azure-compliance
    image: gcr.io/${GCP_PROJECT_ID}/steampipe-azure-compliance:latest
    port: 8083
    memory: 2Gi
    cpu: 2
    min_instances: 1
    max_instances: 10
    concurrency: 100
    timeout: 600
    environment:
      - name: AUTH_TYPE
        value: adc
      - name: AZURE_CLIENT_ID
        value: YOUR_AZURE_CLIENT_ID
      - name: AZURE_CLIENT_SECRET
        value: YOUR_AZURE_CLIENT_SECRET
      - name: AZURE_TENANT_ID
        value: YOUR_AZURE_TENANT_ID
      - name: CLIENT_NAME
        value: ironfort
      - name: DOMAIN
        value: https://marketplace.neosec.com
    ingress: internal  # Private service
    
  gcp-backend:
    name: ironfort-gcp-compliance
    image: gcr.io/${GCP_PROJECT_ID}/steampipe-gcp-compliance:latest
    port: 8080
    memory: 2Gi
    cpu: 2
    min_instances: 1
    max_instances: 10
    concurrency: 100
    timeout: 600
    environment:
      - name: AUTH_TYPE
        value: secret_manager
      - name: CLIENT_NAME
        value: ironfort
      - name: DOMAIN
        value: https://marketplace.neosec.com
    ingress: internal  # Private service
    
  # Frontend service (public)
  frontend:
    name: ironfort-compliance-dashboard
    image: gcr.io/${GCP_PROJECT_ID}/steampipe-dashboard:latest
    port: 80
    memory: 1Gi
    cpu: 1
    min_instances: 1
    max_instances: 5
    concurrency: 1000
    timeout: 60
    environment:
      - name: REACT_APP_API_BASE_URL
        value: https://ironfort-compliance-gateway-${RANDOM_SUFFIX}.a.run.app
      - name: CLIENT_NAME
        value: ironfort
    ingress: all  # Public service
    
  # API Gateway (public, routes to private backends)
  api-gateway:
    name: ironfort-compliance-gateway
    image: gcr.io/${GCP_PROJECT_ID}/api-gateway:latest
    port: 8081
    memory: 1Gi
    cpu: 1
    min_instances: 1
    max_instances: 10
    concurrency: 1000
    timeout: 60
    environment:
      - name: AWS_BACKEND_URL
        value: https://ironfort-aws-compliance-${RANDOM_SUFFIX}.a.run.app
      - name: AZURE_BACKEND_URL
        value: https://ironfort-azure-compliance-${RANDOM_SUFFIX}.a.run.app
      - name: GCP_BACKEND_URL
        value: https://ironfort-gcp-compliance-${RANDOM_SUFFIX}.a.run.app
      - name: ADMIN_BACKEND_URL
        value: https://ironfort-admin-api-${RANDOM_SUFFIX}.a.run.app
      - name: CLIENT_NAME
        value: ironfort
    ingress: all  # Public service with IAP
  
  # Admin API (internal)
  admin-api:
    name: ironfort-admin-api
    image: gcr.io/${GCP_PROJECT_ID}/admin-api:latest
    port: 8084
    memory: 1Gi
    cpu: 1
    min_instances: 1
    max_instances: 5
    concurrency: 100
    timeout: 60
    environment:
      - name: AUTH_TYPE
        value: secret_manager
      - name: CLIENT_NAME
        value: ironfort
      - name: GCP_PROJECT_ID
        value: ${GCP_PROJECT_ID}
      - name: DOMAIN
        value: https://marketplace.neosec.com
    ingress: internal  # Private service
  
  # Admin UI (public)
  admin-ui:
    name: ironfort-admin-ui
    image: gcr.io/${GCP_PROJECT_ID}/admin-ui:latest
    port: 80
    memory: 512Mi
    cpu: 0.5
    min_instances: 1
    max_instances: 3
    concurrency: 1000
    timeout: 60
    environment:
      - name: REACT_APP_API_BASE_URL
        value: https://ironfort-compliance-gateway-${RANDOM_SUFFIX}.a.run.app
      - name: CLIENT_NAME
        value: ironfort
    ingress: all  # Public service with IAP

# VPC Connector for private services
vpc_connector:
  name: ironfort-connector
  ip_cidr_range: ********/28
  machine_type: f1-micro
  min_instances: 2
  max_instances: 10

# Secret Manager Configuration
secret_manager:
  prefix: ironfort  # Secrets will be named: ironfort-{customer}-{cloud}