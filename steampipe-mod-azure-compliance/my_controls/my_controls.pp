locals {
  my_controls_common_tags = merge(local.azure_compliance_common_tags, {
    type = "Benchmark"
  })
}

benchmark "my_controls" {
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    # Controls will be added here dynamically
  ]
  tags = local.my_controls_common_tags
}

# This file will be overwritten when new controls are generated
